# @iam/login-shared

## 4.0.0-thirdApp.229

### Patch Changes

- feat: 在登录组件中添加第三方认证支持

## 4.0.0-thirdApp.228

### Patch Changes

- fix: update zt-ticket param to zt_ticket in goTripartiteAuth function

## 4.0.0-thirdApp.227

### Patch Changes

- fix: zt 不阻断任何流程

## 4.0.0-thirdApp.226

### Patch Changes

- feat: 增加 ZT 票据异常处理和错误提示

## 4.0.0-thirdApp.225

### Patch Changes

- refactor: 临时注释 refreshZtTokenInWave 调用

## 4.0.0-thirdApp.224

### Patch Changes

- 三方认证流程，增加 ZT 票据支持

## 4.0.0-hw.224

### Patch Changes

- fix: 添加 otp_token 支持到 useFlowToken hook

## 4.0.0-hw.223

### Patch Changes

- feat: 优化登录流程和错误处理机制

## 4.0.0-hw.222

### Patch Changes

- Revert "refactor: extract login form hooks into separate files"

## 4.0.0-hw.221

### Patch Changes

- fix: style

## 4.0.0-hw.220

### Patch Changes

- refactor: update vite config for better compatibility and debugging

## 4.0.0-hw.219

### Patch Changes

- refactor: extract login form hooks into separate files

## 4.0.0-hw.218

### Patch Changes

- style: adjust button font sizes and simplify otp border styles

## 4.0.0-hw.217

### Patch Changes

- style: adjust logout button styles for consistency

## 4.0.0-hw.216

### Patch Changes

- style: adjust otp and login form styles for mobile responsiveness

## 4.0.0-hw.215

### Patch Changes

- fix: adjust modal width and clean up otp link url

## 4.0.0-hw.214

### Patch Changes

- feat: privacy policy translations

## 4.0.0-hw.213

### Patch Changes

- chore: update @otakus/design version and remove duplicate StyleProvider

## 4.0.0-hw.212

### Patch Changes

- feat: update otp bind and verify ui with video modal and help links
- 37e4af4: feat: add luming animation support
- c5630e1: feat: add luming animation support

## 4.0.0-hw.211

### Patch Changes

- │ fix: handle undefined data in LoginForm policy check

## 4.0.0-hw.210

### Patch Changes

- feat: add sentry error reporting to login components

## 4.0.0-hw.209

### Major Changes

- │ feat: add hardware binding SDK with crypto and storage support

## 4.0.0-exchangeApproval.208

### Patch Changes

- feat: 更新多语言

## 4.0.0-exchangeApproval.207

### Patch Changes

- feat: 换绑表单 modal

## 4.0.0-exchangeApproval.206

### Patch Changes

- feat: 更新多语言

## 4.0.0-exchangeApproval.205

### Patch Changes

- fix: otp title

## 4.0.0-exchangeApproval.204

### Patch Changes

- feat: 更新多语言

## 4.0.0-exchangeApproval.203

### Patch Changes

- feat: 更新多语言

## 4.0.0-exchangeApproval.202

### Patch Changes

- feat: 多语言更新

## 4.0.0-exchangeApproval.201

### Patch Changes

- fix: 测试逻辑

## 4.0.0-exchangeApproval.200

### Patch Changes

- feat:250423

## 4.0.0-exChangeOtp.199

### Patch Changes

- fix: style

## 4.0.0-exChangeOtp.198

### Patch Changes

- fix: style

## 4.0.0-exChangeOtp.197

### Patch Changes

- fix: style

## 4.0.0-exChangeOtp.196

### Patch Changes

- fixL style

## 4.0.0-exChangeOtp.195

### Patch Changes

- fix: style

## 4.0.0-exChangeOtp.194

### Patch Changes

- fix: locale bugs

## 4.0.0-exChangeOtp.193

### Patch Changes

- feat: 更新多语言

## 4.0.0-exChangeOtp.192

### Patch Changes

- fix: style

## 4.0.0-exChangeOtp.191

### Patch Changes

- upd: login@4.0.0-exChangeOtp.191

## 4.0.0-exChangeOtp.190

### Patch Changes

- feat: 单独使用 mfa 可关闭弹窗

## 4.0.0-exChangeOtp.189

### Patch Changes

- upd: login@@4.0.0-exChangeOtp.188

## 4.0.0-exChangeOtp.188

### Patch Changes

- fix: 在 wave 端内不唤起快速登录

## 4.0.0-exChangeOtp.187

### Patch Changes

- feat: 文案修改

## 4.0.0-exChangeOtp.186

### Patch Changes

- upd: login@4.0.0-exChangeOtp.186

## 4.0.0-exChangeOtp.185

### Patch Changes

- iam/login-ui

## 4.0.0-exChangeOtp.184

### Patch Changes

- feat: 立即注册

## 4.0.0-exChangeOtp.183

### Patch Changes

- fix: 0326 bug

## 4.0.0-exChangeOtp.182

### Patch Changes

- feat: player

## 4.0.0-exChangeOtp.181

### Patch Changes

- fix: INVALID_TOTP_VERIFY_CODE_V2 清除表单状态

## 4.0.0-exChangeOtp.180

### Patch Changes

- feat: showBackButton

## 4.0.0-exChangeOtp.179

### Patch Changes

- feat: 0326

## 4.0.0-otpText.178

### Patch Changes

- feat: 250326

## 4.0.0-otpText.177

### Patch Changes

- feat: 绑定提示

## 4.0.0-otpText.176

### Patch Changes

- fix: moatClientId 获取 兼容 HOYOVERSE_DOMAIN

## 4.0.0-otpText.175

### Patch Changes

- e732c8e: feat: hoyoverse
- Merge branch 'feat-next-otaku-hoyoverse' into feat-otaku-0219

## 4.0.0-otpText.174

### Patch Changes

- feat: 更改 otp:bind:btn 文案

## 4.0.0-mfaClose.173

### Patch Changes

- fix: 标题闪烁

## 4.0.0-mfaClose.172

### Patch Changes

- d4f60dd: feat: 减少客户端主动取消请求的 sentry 错误上报
- upd: login@4.0.0-mfaClose.172

## 4.0.0-mfaClose.171

### Patch Changes

- upd: login@4.0.0-mfaClose.171

## 4.0.0-mfaClose.170

### Patch Changes

- feat: 支持 showMfaCloseIcon，以及弹窗关闭时间

## 4.0.0-otpNext.169

### Patch Changes

- upd: login@4.0.0-otpNext.169

## 4.0.0-otpNext.168

### Patch Changes

- upd: login@4.0.0-otpNext.168

## 4.0.0-otpNext.167

### Patch Changes

- upd: login@4.0.0-otpNext.167

## 4.0.0-otpNext.166

### Patch Changes

- upd: login@4.0.0-otpNext.166

## 4.0.0-otpNext.165

### Patch Changes

- upd: login@4.0.0-otpNext.165

## 4.0.0-otpNext.164

### Patch Changes

- release: 4.0.0-otpNext.164

## 4.0.0-otpNext.163

### Patch Changes

- release: 4.0.0-otpNext.163

## 4.0.0-otpNext.162

### Minor Changes

- release: login@4.1.0-otpNext.161

## 4.0.0-otpNext.161

### Patch Changes

- release:4.0.0-otpNext.161

## 4.0.0-otpNext.160

### Patch Changes

- upd: login@4.0.0-otpNext.160

## 4.0.0-otpNext.159

### Patch Changes

- upd: login@4.0.0-otpNext.159

## 4.0.0-otpNext.158

### Patch Changes

- release: login@4.0.0-otpNext.158

## 4.0.0-otpNext.157

### Patch Changes

- release:4.0.0-otpNext.156

## 4.0.0-otpFeature.156

### Patch Changes

- fix: codeInput

## 4.0.0-otpFeature.155

### Patch Changes

- upd: login@4.0.0-otpFeature.154

## 4.0.0-otpFeature.154

### Patch Changes

- release:4.0.0-otpFeature.154

## 4.0.0-otpFeature.153

### Patch Changes

- release: login@4.0.0-otpFeature.153

## 4.0.0-otpFeature.152

### Patch Changes

- release: 4.0.0-241211.152

## 4.0.0-241211.151

### Minor Changes

- release: 4.0.0-241211.151

## 4.0.0-241211.150

### Minor Changes

- release: 4.0.0-241211.148

## 4.0.0-vmfa.149

### Patch Changes

- release: 4.0.0-vmfa.148

## 4.0.0-vmfa.148

### Patch Changes

- release: 4.0.0-vmfa.148

## 4.0.0-vmfa.147

### Patch Changes

- release: 4.0.0-vmfa.147

## 4.0.0-vmfa.146

### Patch Changes

- release: 4.0.0-vmfa.146

## 4.0.0-vmfa.145

### Patch Changes

- release: 4.0.0-vmfa.145

## 4.0.0-vmfa.144

### Patch Changes

- release: 4.0.0-vmfa.143

## 4.0.0-vmfa.143

### Patch Changes

- fix: 二验

## 4.0.0-vmfa.142

### Patch Changes

- release: 4.0.0-vmfa.142

## 4.0.0-vmfa.141

### Patch Changes

- release: 4.0.0-vmfa.141

## 4.0.0-vmfa.140

### Patch Changes

- release: 4.0.0-vmfa.40

## 4.0.0-vmfa.139

### Patch Changes

- release: 4.0.0-vmfa.139

## 4.0.0-vmfa.138

### Patch Changes

- release: 4.0.0-vmfa.138

## 4.0.0-vmfa.137

### Patch Changes

- fix: style

## 4.0.0-vmfa.136

### Patch Changes

- release: 4.0.0-vmfa.136
- 0b38f6c: feat: wave 免登 同步登录态

## 4.0.0-vmfa.135

### Patch Changes

- release: 4.0.0-vmfa.135

## 4.0.0-vmfa.134

### Patch Changes

- release: 4.0.0-vmfa.134

## 4.0.0-vmfa.133

### Patch Changes

- release: 4.0.0-vmfa.133

## 4.0.0-vmfa.132

### Patch Changes

- release: 4.0.0-vmfa.132

## 4.0.0-vmfa.131

### Patch Changes

- release: 4.0.0-vmfa.131

## 4.0.0-vmfa.130

### Patch Changes

- release: 4.0.0-vmfa.130

## 4.0.0-vmfa.129

### Patch Changes

- release: 4.0.0-vmfa.129

## 4.0.0-vmfa.128

### Patch Changes

- release: 4.0.0-vmfa.128

## 4.0.0-vmfa.127

### Patch Changes

- release: 4.0.0-vmfa.127

## 4.0.0-vmfa.126

### Patch Changes

- fix: style

## 4.0.0-vmfa.125

### Patch Changes

- release: 4.0.0-vmfa.125

## 4.0.0-vmfa.124

### Patch Changes

- release: 4.0.0-vmfa.124

## 4.0.0-vmfa.123

### Patch Changes

- release: 4.0.0-vmfa.123

## 4.0.0-vmfa.122

### Patch Changes

- release: 4.0.0-vmfa.122

## 4.0.0-vmfa.121

### Patch Changes

- release: 4.0.0-vmfa.121

## 4.0.0-vmfa.120

### Patch Changes

- release: 4.0.0-vmfa.120

## 4.0.0-vmfa.119

### Patch Changes

- release: login@4.0.0-vmfa.119

## 4.0.0-vmfa.118

### Patch Changes

- release: login@4.0.0-vmfa.118

## 4.0.0-vmfa.117

### Patch Changes

- release: login@4.0.0-vmfa.117

## 4.0.0-vmfa.116

### Patch Changes

- release: login@4.0.0-vmfa.116

## 4.0.0-vmfa.115

### Patch Changes

- release: 4.0.0-vmfa.114

## 4.0.0-vmfa.114

### Patch Changes

- fix: some bugs

## 4.0.0-vmfa.113

### Patch Changes

- release: 4.0.0-otaku.112

## 4.0.0-vmfa.112

### Patch Changes

- release: 4.0.0-otaku.111

## 4.0.0-vmfa.111

### Patch Changes

- release: 4.0.0-vmfa.111

## 4.0.0-vmfa.110

### Patch Changes

- release: 4.0.0-vmfa.110

## 4.0.0-vmfa.109

### Patch Changes

- release: 4.0.0-vmfa.109

## 4.0.0-vmfa.108

### Patch Changes

- release: 4.0.0-vmfa.109

## 4.0.0-vmfa.107

### Patch Changes

- release: vmufa init

## 4.0.0-otaku.111

### Patch Changes

- feat: wave 免登 同步登录态

## 4.0.0-otaku.110

### Patch Changes

- fix: PhoneInput type

## 4.0.0-otaku.109

### Patch Changes

- release: 4.0.0-otaku.109

## 4.0.0-otaku.108

### Patch Changes

- release: 4.0.0-otaku.108

## 4.0.0-otaku.107

### Patch Changes

- release: 4.0.0-otaku.107

## 4.0.0-otaku.106

### Patch Changes

- release: 4.0.0-otaku.106

## 4.0.0-otaku.105

### Patch Changes

- release: 4.0.0-otaku.105

## 4.0.0-otaku.104

### Patch Changes

- release: 4.0.0-otaku.104

## 4.0.0-otaku.103

### Patch Changes

- release: 4.0.0-otaku.103

## 4.0.0-otaku.102

### Patch Changes

- release: 4.0.0-otaku.102

## 4.0.0-otaku.101

### Patch Changes

- release: 4.0.0-otaku.101

## 4.0.0-otaku.100

### Patch Changes

- release: 4.0.0-otaku.100

## 4.0.0-otaku.99

### Patch Changes

- release: 4.0.0-otaku.99

## 4.0.0-otaku.98

### Patch Changes

- release: 4.0.0-otaku.98

## 4.0.0-otaku.97

### Patch Changes

- release: 4.0.0-otaku.97

## 4.0.0-otaku.96

### Patch Changes

- release: 4.0.0-otaku.96

## 4.0.0-otaku.95

### Patch Changes

- code format

## 4.0.0-otaku.94

### Patch Changes

- release: 4.0.0-otaku.94

## 4.0.0-otaku.93

### Patch Changes

- release: 4.0.0-otaku.93

## 4.0.0-otaku.92

### Patch Changes

- release: 4.0.0-otaku.92

## 4.0.0-otaku.91

### Patch Changes

- release: 4.0.0-otaku.90

## 4.0.0-otaku.90

### Patch Changes

- release: 4.0.0-otaku.90

## 4.0.0-otaku.89

### Patch Changes

- release: 4.0.0-otaku.89

## 4.0.0-otaku.88

### Patch Changes

- release: 4.0.0-otaku.88

## 4.0.0-otaku.87

### Patch Changes

- release: 4.0.0-otaku.87

## 4.0.0-otaku.86

### Patch Changes

- release: 4.0.0-otaku.86

## 4.0.0-otaku.85

### Patch Changes

- release: 4.0.0-otaku.85

## 4.0.0-otaku.84

### Patch Changes

- release: 4.0.0-otaku.84

## 4.0.0-otaku.83

### Patch Changes

- release: 4.0.0-otaku.83

## 4.0.0-otaku.82

### Patch Changes

- release: 4.0.0-otaku.82

## 4.0.0-otaku.81

### Patch Changes

- release: 4.0.0-otaku.81

## 4.0.0-otaku.80

### Patch Changes

- release: 4.0.0-otaku.80

## 4.0.0-otaku.79

### Patch Changes

- release: 4.0.0-otaku.79

## 4.0.0-otaku.78

### Patch Changes

- release: v4.0.0-otaku.78

## 4.0.0-otaku.77

### Patch Changes

- feat: 调整一些样式

## 4.0.0-otaku.76

### Patch Changes

- feat: sm 尺寸样式

## 4.0.0-otaku.75

### Patch Changes

- fix: style

## 4.0.0-otaku.74

### Patch Changes

- fix: style

## 4.0.0-otaku.73

### Patch Changes

- feat: 样式调整

## 4.0.0-otaku.72

### Patch Changes

- feat: NextComponentSecondVerification

## 4.0.0-otaku.71

### Patch Changes

- 7.76 kB │ gzip: 2.52 kB │ map: 4.94 kB
- release: v4.0.0-otaku

## 4.0.0-nextApp.70

### Patch Changes

- fix: third_party_auth_method_configs

## 4.0.0-nextApp.69

### Patch Changes

- fix: 三方

## 4.0.0-nextApp.68

### Patch Changes

- feat: 更换 logout 接口

## 4.0.0-nextApp.67

### Patch Changes

- feat: getSamlRequest

## 4.0.0-nextApp.66

### Minor Changes

- feat: 支持 logout

## 4.0.0-nextApp.65

### Patch Changes

- feat: 重新发布

## 4.0.0-nextApp.64

### Patch Changes

- feat: source 网关标识

## 4.0.0-nextApp.63

### Patch Changes

- fix: mb showTab

## 4.0.0-nextApp.62

### Patch Changes

- fix: fix: userCenter 支持 -5

## 4.0.0-nextApp.61

### Patch Changes

- fix:userCenter 支持 -5

## 4.0.0-nextApp.60

### Patch Changes

- feat； userCenter 支持 -5

## 4.0.0-nextApp.59

### Patch Changes

- feat: handle -5

## 4.0.0-nextApp.58

### Patch Changes

- feat: 设备指纹

## 4.0.0-nextApp.57

### Patch Changes

- feat: bc 测试

## 4.0.0-nextApp.56

### Patch Changes

- fix: submit cid

## 4.0.0-nextApp.55

### Patch Changes

- fix: mb SmsForm

## 4.0.0-nextApp.54

### Patch Changes

- feat: trim form value

## 4.0.0-nextApp.53

### Patch Changes

- fix: trim 空指针 修复

## 4.0.0-nextApp.52

### Patch Changes

- fix: 修复 getLocation 结构问题

## 4.0.0-nextApp.51

### Patch Changes

- sessioncheck

## 4.0.0-nextApp.50

### Patch Changes

- fix: wave sessionCheck

## 4.0.0-nextApp.49

### Patch Changes

- feat: 修复 sessionCheck 逻辑

## 4.0.0-nextApp.48

### Patch Changes

- feat: sessionCheck
- de403e8: feat: sessionCheck

## 4.0.0-nextApp.47

### Patch Changes

- feat: 优化 FCP

## 4.0.0-nextApp.46

### Patch Changes

- fix: 表单校验

## 4.0.0-nextApp.45

### Patch Changes

- fix: 跨应用 免登

## 4.0.0-nextApp.44

### Patch Changes

- fix: wave

## 4.0.0-nextApp.43

### Patch Changes

- fix: getCurrentMethod

## 4.0.0-nextApp.42

### Patch Changes

- fix: mb waveQrcode

## 4.0.0-nextApp.41

### Patch Changes

- fix: 修复 perAuth 域名

## 4.0.0-nextApp.40

### Patch Changes

- feat: 支持账号中心迁入网关

## 4.0.0-nextApp.39

### Minor Changes

- 6a48a05: feat: 更新我忘记密码 guide

### Patch Changes

- 7f274b8: feat: 修复 passwd 语言问题
- Merge branch 'master' into feat-next

## 4.0.0-nextApp.38

### Patch Changes

- feat: 更新参数

## 4.0.0-nextApp.37

### Patch Changes

- feat: 修改 pp 服务端 domain

## 4.0.0-nextApp.36

### Major Changes

- feat: 支持账号中心迁入网关

## 4.0.0-nextApp.35

### Patch Changes

- feat: 支持语言切换 不再刷新

## 4.0.0-nextApp.34

### Patch Changes

- fix: moatClientId

## 4.0.0-nextApp.33

### Patch Changes

- fix: domain

## 4.0.0-nextApp.32

### Patch Changes

- feat: 上报 sdk 错误至 sentry 且 组件 展示 message

## 4.0.0-nextApp.31

### Patch Changes

- fix: open passwd page

## 4.0.0-nextApp.30

### Patch Changes

- feat: 处理使用 iframe 之后，window 的问题处理

## 4.0.0-nextApp.29

### Patch Changes

- feat: 删除多余代码

## 4.0.0-nextApp.28

### Patch Changes

- feat: authnV3Privacy

## 4.0.0-nextApp.27

### Patch Changes

- feat: 完善 request handler

## 4.0.0-nextApp.26

### Patch Changes

- feat: sentryCaptureException 优化

## 4.0.0-nextApp.25

### Patch Changes

- feat: sentryCaptureException 格式化

## 4.0.0-nextApp.24

### Patch Changes

- feat: requestInfo

## 4.0.0-nextApp.23

### Patch Changes

- feat； 日志修复

## 4.0.0-nextApp.22

### Patch Changes

- feat: test error

## 4.0.0-nextApp.21

### Patch Changes

- fix: moatSilentLoginApi

## 4.0.0-nextApp.20

### Patch Changes

- feat: report

## 4.0.0-nextApp.19

### Patch Changes

- feat: 增加 log

## 4.0.0-nextApp.18

### Patch Changes

- fix: sourcemap

## 4.0.0-nextApp.17

### Patch Changes

- feat: sentryCaptureException contexts

## 4.0.0-nextApp.16

### Patch Changes

- fix: getSentryCapture

## 4.0.0-nextApp.15

### Patch Changes

- ;feat 测试 getSentryCapture

## 4.0.0-nextApp.14

### Patch Changes

- fix: version

## 4.0.0-nextApp.13

### Patch Changes

- feat； sourcemap

## 4.0.0-nextApp.12

### Patch Changes

- fix: request

## 4.0.0-nextApp.11

### Patch Changes

- Update request.ts

## 4.0.0-nextApp.10

### Patch Changes

- feat: no catch

## 4.0.0-nextApp.9

### Patch Changes

- feat: 完善接口日志上报

## 4.0.0-nextApp.8

### Patch Changes

- feat: test sentryCaptureException

## 4.0.0-nextApp.7

### Patch Changes

- feat: getSentryCapture

## 4.0.0-nextApp.6

### Patch Changes

- feat: shim

## 4.0.0-nextApp.5

### Patch Changes

- fix: preAuthApi 空指针

## 4.0.0-nextApp.4

### Patch Changes

- feat: 实现同步 session 至 老 IAM

## 4.0.0-nextApp.3

### Patch Changes

- feat: 修复 test 接口路径

## 4.0.0-nextApp.2

### Patch Changes

- feat: 更改 mfa prefix

## 4.0.0-nextApp.1

### Patch Changes

- fix: v3 api prefix

## 4.0.0-nextApp.0

### Major Changes

- feat: nextApp 首次提交

## 3.6.0-guide.7

### Patch Changes

- feat: WxLoading

## 3.6.0-guide.6

### Patch Changes

- feat: WxLoading

## 3.6.0-guide.5

### Patch Changes

- Update ComponentWithUseSSo.tsx

## 3.6.0-guide.4

### Patch Changes

- Merge branch 'feat-cog' into feat-0731

## 3.6.0-guide.3

### Patch Changes

- feat: 优化 渲染时机

## 3.6.0-guide.2

### Patch Changes

- feat: 修复 passwd 语言问题

## 3.6.0-guide.1

### Minor Changes

- feat: 更新我忘记密码 guide

## 3.5.1-oppp.0

### Patch Changes

- feat: 新增 对接 op pp 环境

## 3.6.0

### Minor Changes

- hoyoverse-->cog

## 3.5.0

### Minor Changes

- 080f7d6: feat: crypto-js
- release:0619

### Patch Changes

- f307c38: fix: 跳转至 修改密码 携带语言
- a1d5e06: feat: pc 账号中心 端内调用 jsapi 跳转 忘记密码 以及 忘记二级密码
- afcd48c: Merge branch 'feat-extreme-optimization' into feat-0619-optimization
- 852e3a5: feat: 优化核心流程
- 3721160: fix: 账号中心兼容 wave
- eebf1f4: fix: 丢失 clientId

## 3.5.0-accountInWaveAndOptimization.3

### Minor Changes

- 080f7d6: feat: crypto-js

### Patch Changes

- Merge branch 'feat-extreme-optimization' into feat-0619-optimization
- 852e3a5: feat: 优化核心流程
- eebf1f4: fix: 丢失 clientId

## 3.4.2-accountInWave.2

### Patch Changes

- fix: 跳转至 修改密码 携带语言

## 3.4.2-accountInWave.1

### Patch Changes

- fix: 账号中心兼容 wave

## 3.4.2-accountInWave.0

### Patch Changes

- feat: pc 账号中心 端内调用 jsapi 跳转 忘记密码 以及 忘记二级密码

## 3.4.1

### Patch Changes

- upd: login@3.4.1
- 1e7c951: feat: handleCLose

## 3.4.1-close.0

### Patch Changes

- feat: handleCLose

## 3.4.0

### Minor Changes

- release: 0522

### Patch Changes

- e624860: feat: 0522 测试
- fbaf3b6: feat: TipModal
- 2e92194: feat: accountPerf
- 1bd93b0: fix: mb mfa style
- fed6eb8: fix: tipModal style

## 3.3.1-optimization.0

### Patch Changes

- feat: accountPerf

## 3.4.0-extreme.2

### Patch Changes

- fix: 丢失 clientId

## 3.4.0-extreme.1

### Patch Changes

- feat: 优化核心流程

## 3.4.0-extreme.0

### Minor Changes

- feat: crypto-js

## 3.3.0

### Minor Changes

- feat: 0515

### Patch Changes

- 5f93f23: feat: 增加日志
- 1a53364: feat: useUpdateLayoutEffect
- 12dfbfc: fix: setVisible 判断
- 66ee45d: 支持 MessageSlot
- 70fea1d: fix: memo
- 04e28b2: fix: 渲染优化
- 8b2df03: feat: 更改 slot-message 样式
- 72395c6: fix: memo 明命名
- 9655977: feat: 测试 MessageSlot useLayout
- 73aef7e: feat: clear Modal
- 69308ca: fix: WAVE_CROSS_TENANT 判断

## 3.2.4-optimization.14

### Patch Changes

- fix: mb mfa style

## 3.2.4-optimization.13

### Patch Changes

- fix: tipModal style

## 3.2.4-optimization.12

### Patch Changes

- feat: TipModal

## 3.2.4-optimization.11

### Patch Changes

- feat: 0522 测试

## 3.2.4-messageSlot.10

### Patch Changes

- feat: 增加日志

## 3.2.4-messageSlot.9

### Patch Changes

- feat: useUpdateLayoutEffect

## 3.2.4-messageSlot.8

### Patch Changes

- fix: setVisible 判断

## 3.2.4-messageSlot.7

### Patch Changes

- fix: memo 明命名

## 3.2.4-messageSlot.6

### Patch Changes

- fix: memo

## 3.2.4-messageSlot.5

### Patch Changes

- feat: 测试 MessageSlot useLayout

## 3.2.4-messageSlot.4

### Patch Changes

- fix: 渲染优化

## 3.2.4-messageSlot.3

### Patch Changes

- feat: 更改 slot-message 样式

## 3.2.4-messageSlot.2

### Patch Changes

- feat: clear Modal

## 3.2.4-messageSlot.1

### Patch Changes

- fix: WAVE_CROSS_TENANT 判断

## 3.2.4-messageSlot.0

### Patch Changes

- 支持 MessageSlot

## 3.2.3

### Patch Changes

- d63a838: feat: Revert "feat: 删除多余企微免等逻辑"
- release(0425)
- 12432b4: feat: merge from master login@3.2.2

## 3.2.3-sdk.1

### Patch Changes

- feat: Revert "feat: 删除多余企微免等逻辑"

## 3.2.3-sdk.0

### Patch Changes

- feat: merge from master login@3.2.2

## 3.2.2

### Patch Changes

- feat: 更新 海外环境 APP_ID release
- f248e595: feat: 更新 海外 pts prod 环境的 app_id
- 369e9fd5: fix: 海外测试环境 APP_ID

## 3.2.2-appId.1

### Patch Changes

- feat: 更新 海外 pts prod 环境的 app_id

## 3.2.2-appId.0

### Patch Changes

- fix: 海外测试环境 APP_ID

## 3.2.1

### Patch Changes

- fix: style

## 3.2.0

### Minor Changes

- feat: 3.2.0

## 3.1.0

### Minor Changes

- feat: 0410

### Patch Changes

- 65a7f05a: fix: style
- a0f2b9cb: feat: merge from feat-0327-temp
- 321b45cd: fix: style
- 95aab90a: fix: style
- 722607b2: Merge branch 'feat-0327-tmp' into feat-0410
- 43e66785: feat: init sdk
- e2c726f8: fix: style
- 23583c82: fix: style
- 67124016: fix: style
- 5b4f574e: fix: style
- 0d2b3cbb: Merge branch 'feat-0327-tmp' into feat-0410

## 3.1.0-sdk.65

### Patch Changes

- Merge branch 'feat-0327-tmp' into feat-0410

## 3.1.3

### Patch Changes

- feat: 3.1.3
- 3c636668: fix: exports
- 3c636668: fix: export

## 3.1.3-exports.1

### Patch Changes

- fix: export

## 3.1.3-exports.0

### Patch Changes

- fix: exports

## 3.1.2

### Patch Changes

- fix: style

## 3.1.0-sdk.64

### Patch Changes

- fix: style

## 3.1.0-sdk.63

### Patch Changes

- Merge branch 'feat-0327-tmp' into feat-0410

## 3.1.1

### Patch Changes

- feat: 修改 scripts/inject-css-dependence.js

## 3.1.0

### Minor Changes

- 187444be: fix: vue css
- feat: 三方认证

### Patch Changes

- 84055f61: fix: style
- 5070d1c1: fix: 配置
- b6ff9728: fix； 跳转
- 08d245f0: feat: scripts\compile-and-merge-css.js
- 474ed8c7: feat: goTripartiteAuth delete source
- 9a5fa450: feat: postcss
- 1fa67804: fix: copyright
- 92ea7e85: Update compile-and-merge-css.js
- 9677502d: feat: dist-css
- e625153f: fix: style
- 05915c9d: feat: pc default.less
- 19a11c95: feat: locale
- 0f0b7051: fix: OP_TYPE
- c1378f36: feat: getTLD 容错判断
- e349eb72: fix: pc export bugs
- 2bd5e791: feat: export 类型定义
- 746ef79d: fix: style
- 37bfa009: feat: inject-css-dependence.js
- 2e44744d: fix: passwd mb lang
- 88046433: feat: 跨租户 联调
- 0192fc0a: fix: passwd AssetsProvider
- 4d948cbe: fix: useWave 判断条件
- 033372b4: feat: postcss
- df1ddabc: feat: 修改多语言
- 7ef9bc4e: upd: locales
- 47122aa9: fix: style
- 3635dddf: fix: postcss
- 5a25c9e1: feat: goTripartiteAuthErrByZt locale
- e8046897: fix: OTPList
- dd2334a2: feat: message 调用方式更换
- c822be24: fix: getIncompatibleMessage
- d3bd9f18: fix: copyright style
- 8c822733: fix: passwd 语言问题
- 995fc2da: pnpm bump
- 2b4224fc: fix: createWxOAuth2UrlByPathname 因为参数格式可能丢失的问题
- 0a0a5a87: fix: localhost domain 问题
- 895e271c: upd: example
- 61b8b301: fix: useWave port
- 11e898a8: fix: copyright style
- fb63c00f: fix: goTripartiteAuth
- 6f2132b4: feat: 打包策略更新完成
- 030f1df2: fix: compile-and-merge-css
- 3aabcc0e: fix: 更换 api 请求协议
- 9668965c: fix: 伪类 样式
- 9138177e: fix: passwd hoyoverse 域名错误
- 1618ffe7: fix: mb min-height
- e1554bd6: upd: pub:plat
- aa9b1b4d: feat: 修复主体问题
- 1ec4f046: feat: 新增 op inc 联调配置
- 169087a5: fix: 修复 wave 免登失败
- 292afc1e: fix: third redirect
- 5d45c9c3: Merge branch 'feat-0228' into feat-third-new
- 066f93a6: fix: 修复 mb style
- dabe1ca3: fix: useWave 逻辑
- f94e4d5e: feat: 高度低于 860px 出现滚动条
- 067cf664: feat: 修复 registerDomain
- a2cd5648: fix: logo style
- 50848bc8: upd: locales
- 7364ae71: fix: index.css
- 7c324a72: fix: hoyoverse-inc op 组件域名
- d36068bd: fix: export typess
- b500c224: fix: export
- 5d69f7d7: fix: goTripartiteAuth 参数

## 3.1.0-sdk.62

### Patch Changes

- fix: style

## 3.1.0-sdk.61

### Patch Changes

- fix: style

## 3.1.0-sdk.60

### Patch Changes

- fix: style

## 3.1.0-sdk.59

### Patch Changes

- fix: style

## 3.1.0-sdk.58

### Patch Changes

- fix: style

## 3.1.0-sdk.57

### Patch Changes

- fix: style

## 3.1.0-sdk.56

### Patch Changes

- feat: merge from feat-0327-temp
- 9677502d: feat: dist-css
- e625153f: fix: style
- e349eb72: fix: pc export bugs
- 2bd5e791: feat: export 类型定义
- 37bfa009: feat: inject-css-dependence.js
- 033372b4: feat: postcss
- 895e271c: upd: example
- d36068bd: fix: export typess
- b500c224: fix: export

## 3.1.0-sdk.55

### Patch Changes

- feat: init sdk

## 3.1.0-export.63

### Patch Changes

- upd: example

## 3.1.0-export.62

### Patch Changes

- fix: style

## 3.1.0-export.61

### Patch Changes

- feat: postcss

## 3.1.0-export.60

### Patch Changes

- fix: export typess

## 3.1.0-export.59

### Patch Changes

- feat: export 类型定义

## 3.1.0-export.58

### Patch Changes

- fix: export

## 3.1.0-export.57

### Patch Changes

- fix: pc export bugs

## 3.1.0-export.56

### Patch Changes

- feat: inject-css-dependence.js

## 3.1.0-export.55

### Patch Changes

- feat: dist-css

## 3.1.0-tripartiteAuth.54

### Patch Changes

- Update compile-and-merge-css.js

## 3.1.0-tripartiteAuth.53

### Patch Changes

- fix: copyright style

## 3.1.0-tripartiteAuth.52

### Patch Changes

- fix: copyright style

## 3.1.0-tripartiteAuth.51

### Patch Changes

- fix: copyright

## 3.1.0-tripartiteAuth.50

### Patch Changes

- fix: style

## 3.1.0-tripartiteAuth.49

### Patch Changes

- fix: style

## 3.1.0-tripartiteAuth.48

### Patch Changes

- fix: passwd hoyoverse 域名错误

## 3.1.0-tripartiteAuth.47

### Patch Changes

- fix: mb min-height

## 3.1.0-tripartiteAuth.46

### Patch Changes

- feat: 修改多语言

## 3.1.0-tripartiteAuth.45

### Patch Changes

- fix: passwd mb lang

## 3.1.0-tripartiteAuth.44

### Patch Changes

- feat: 高度低于 860px 出现滚动条

## 3.1.0-tripartiteAuth.43

### Patch Changes

- fix: passwd 语言问题

## 3.1.0-tripartiteAuth.42

### Patch Changes

- fix: passwd AssetsProvider

## 3.1.0-tripartiteAuth.41

### Patch Changes

- feat: goTripartiteAuthErrByZt locale

## 3.1.0-tripartiteAuth.40

### Patch Changes

- feat: locale

## 3.1.0-tripartiteAuth.39

### Patch Changes

- fix: 配置

## 3.1.0-tripartiteAuth.38

### Patch Changes

- feat: goTripartiteAuth delete source

## 3.1.0-tripartiteAuth.37

### Patch Changes

- fix； 跳转

## 3.1.0-tripartiteAuth.36

### Patch Changes

- fix: useWave port

## 3.1.0-tripartiteAuth.35

### Patch Changes

- fix: useWave 逻辑

## 3.1.0-tripartiteAuth.34

### Patch Changes

- fix: logo style

## 3.1.0-tripartiteAuth.33

### Patch Changes

- fix: style

## 3.1.0-tripartiteAuth.32

### Patch Changes

- fix: OTPList

## 3.1.0-tripartiteAuth.31

### Patch Changes

- feat: message 调用方式更换

## 3.1.0-tripartiteAuth.30

### Patch Changes

- fix: 伪类 样式

## 3.1.0-tripartiteAuth.29

### Patch Changes

- fix: compile-and-merge-css

## 3.1.0-tripartiteAuth.28

### Patch Changes

- fix: postcss

## 3.1.0-tripartiteAuth.27

### Patch Changes

- upd: pub:plat

## 3.1.0-tripartiteAuth.26

### Patch Changes

- fix: index.css

## 3.1.0-tripartiteAuth.25

### Patch Changes

- feat: scripts\compile-and-merge-css.js

## 3.1.0-tripartiteAuth.24

### Patch Changes

- feat: postcss

## 3.1.0-tripartiteAuth.23

### Patch Changes

- feat: pc default.less

## 3.1.0-tripartiteAuth.22

### Patch Changes

- feat: 打包策略更新完成

## 3.1.0-tripartiteAuth.21

### Minor Changes

- fix: vue css

## 3.0.14-tripartiteAuth.20

### Patch Changes

- fix: 修复 wave 免登失败

## 3.0.14-tripartiteAuth.19

### Patch Changes

- fix: hoyoverse-inc op 组件域名

## 3.0.14-tripartiteAuth.18

### Patch Changes

- upd: locales

## 3.0.14-tripartiteAuth.17

### Patch Changes

- fix: 修复 mb style

## 3.0.14-tripartiteAuth.16

### Patch Changes

- fix: createWxOAuth2UrlByPathname 因为参数格式可能丢失的问题

## 3.0.14-tripartiteAuth.15

### Patch Changes

- upd: locales

## 3.0.14-tripartiteAuth.14

### Patch Changes

- fix: third redirect

## 3.0.14-tripartiteAuth.13

### Patch Changes

- feat: 修复 registerDomain

## 3.0.14-tripartiteAuth.12

### Patch Changes

- feat: 修复主体问题

## 3.0.14-tripartiteAuth.11

### Patch Changes

- Merge branch 'feat-0228' into feat-third-new

## 3.0.14-tripartiteAuth.10

### Patch Changes

- fix: localhost domain 问题

## 3.0.14-tripartiteAuth.9

### Patch Changes

- fix: OP_TYPE

## 3.0.14-tripartiteAuth.8

### Patch Changes

- feat: getTLD 容错判断

## 3.0.14-tripartiteAuth.7

### Patch Changes

- feat: 新增 op inc 联调配置

## 3.0.14-tripartiteAuth.6

### Patch Changes

- fix: goTripartiteAuth

## 3.0.14-tripartiteAuth.5

### Patch Changes

- fix: goTripartiteAuth 参数

## 3.0.14-tripartiteAuth.4

### Patch Changes

- fix: useWave 判断条件

## 3.0.14-tripartiteAuth.3

### Patch Changes

- fix: 更换 api 请求协议

## 3.0.14-tripartiteAuth.2

### Patch Changes

- fix: getIncompatibleMessage

## 3.0.14-tripartiteAuth.1

### Patch Changes

- feat: 跨租户 联调

## 3.0.14-waveRefresh.0

### Patch Changes

- pnpm bump

## 3.0.14

### Patch Changes

- feat: release 0228
- 1f447f31: feat: account add phone scene
- b1562849: feat: notification

## 3.0.14-notification.1

### Patch Changes

- feat: account add phone scene

## 3.0.14-notification.0

### Patch Changes

- feat: notification

## 3.0.13

### Patch Changes

- fix: waveQrcodeCb async

## 3.0.12

### Patch Changes

- ce777122: feat: upd @hoyowave/login"
- 12d7eee9: feat: 删除 老滑块代码
- 44b6fc8c: feat: changeset config
- ea51ea9a: feat: 升级 hoyowave-login
- feat: 0117
- 556d7c71: fix：risk 路由
- a7be1f5d: feat: 修复 policy title
- ac83a100: upd: 升级 @hoyowave/login
- bfd230ae: feat: 企微 loading 修改

## 3.0.12-risk.7

### Patch Changes

- 44b6fc8c: feat: changeset config

## 3.0.12-risk.6

### Patch Changes

- upd: 升级 @hoyowave/login

## 3.0.12-risk.5

### Patch Changes

- feat: 修复 policy title

## 3.0.12-risk.4

### Patch Changes

- feat: 企微 loading 修改

## 3.0.12-risk.3

### Patch Changes

- feat: 删除 老滑块代码

## 3.0.12-risk.2

### Patch Changes

- feat: upd @hoyowave/login"

## 3.0.12-risk.1

### Patch Changes

- fix：risk 路由

## 3.0.12-risk.0

### Patch Changes

- feat: 升级 hoyowave-login

## 3.0.11

### Patch Changes

- fix: fp&

## 3.0.10

### Patch Changes

- fix: 多语言

## 3.0.9

### Patch Changes

- f6c90e53: fix: 类型问题
- 7ac9e80f: fix：wave style
- 45049039: fix: 代码错误
- feat: 对接 wave 扫码
- 8a958fbf: fix: risk api
- 2c8b2942: feat: 增强企微容错
- 9eb08dfe: fix: style
- 0127c952: feat: 测试 wave 扫码
- 210e24e3: feat: 企微扫码 回调 增加 fp
- feat: 对接 wave 扫码
- 46dbe2f5: fix: 三天免登
- b5877c6d: fix: useWxScript resolve
- 42247ab9: feat: 升级 wave sdk
- c60b5b71: fix: lost_password
- a753bc6c: merge from second-paswd
- 85ad8646: feat: wave 扫码
- e649dce3: feat: D:\info-iam-login
- c27e7ef6: feat: getPreCheck 不使用 pwdScene

## 3.0.9-waveScan.11

### Patch Changes

- fix: risk api

## 3.0.9-waveScan.10

### Patch Changes

- feat: 升级 wave sdk

## 3.0.9-waveScan.9

### Patch Changes

- fix：wave style

## 3.0.9-waveScan.8

### Patch Changes

- fix: style

## 3.0.9-waveScan.7

### Patch Changes

- feat: 企微扫码 回调 增加 fp

## 3.0.9-waveScan.6

### Patch Changes

- fix: lost_password

## 3.0.9-waveScan.5

### Patch Changes

- fix: useWxScript resolve

## 3.0.9-waveScan.4

### Patch Changes

- feat: 增强企微容错

## 3.0.9-waveScan.3

### Patch Changes

- feat: getPreCheck 不使用 pwdScene

## 3.0.9-waveScan.2

### Patch Changes

- feat: D:\info-iam-login

## 3.0.9-waveScan.1

### Patch Changes

- fix: 三天免登

## 3.0.9-waveScan.0

### Patch Changes

- a753bc6c: merge from second-paswd

## 3.0.8

### Patch Changes

- fix: second-passwd-setting-btn style

## 3.0.7

### Patch Changes

- 98e66244: feat: SwitchComponent 兜底逻辑
- c9e3f6ab: feat: getFp
- 6a16fcda: fix: bugs
- a2a745b6: fix: pc 多语言初始化
- a28baf16: feat: loading block
- aff9b272: fix: style
- 598d0fc0: feat: useLogin 增加 source
- 7e2bb2a3: feat: 日志收集整理
- 90ff35b5: feat: switch componen
- 41abaa67: feat: test
- b9583ce1: feat: merge from antd global
- 66d32a67: feat: 调试
- 1bb7e6ce: fix: mfa/second/check
- f7deae00: fix: locales
- 7c20974d: feat: handleClose
- feat: 二级密码 生产
- 1bf48a8e: feat: 增加 useEffect 依赖
- 946e2d64: feat: onCancel
- 3126c24b: feat: locale
- ffde5707: feat: 测试错误端口
- 3bad1d3f: feat: secondpwd
- 5373068d: feat: 存在 pwdScene 不展示 reset:tips
- a0c66107: fix: style
- dbd41b4a: pnpm bump
- 2fd4e73e: feat: 测试 secondPwd
- 23442d08: feat: 兼容没有 中文名或者英文名的情况
- 6e6a7f54: merge from zt
- 48beb078: feat: test props

## 3.0.7-secondPwd.12

### Patch Changes

- fix: pc 多语言初始化

## 3.0.7-secondPwd.11

### Patch Changes

- fix: locales

## 3.0.7-secondPwd.10

### Patch Changes

- fix: style

## 3.0.7-secondPwd.9

### Patch Changes

- feat: locale

## 3.0.7-secondPwd.8

### Patch Changes

- feat: 存在 pwdScene 不展示 reset:tips

## 3.0.7-secondPwd.7

### Patch Changes

- feat: 增加 useEffect 依赖

## 3.0.7-secondPwd.6

### Patch Changes

- feat: test props

## 3.0.7-secondPwd.5

### Patch Changes

- feat: 兼容没有 中文名或者英文名的情况

## 3.0.7-secondPwd.4

### Patch Changes

- pnpm bump

## 3.0.7-secondPwd.3

### Patch Changes

- fix: style

## 3.0.7-secondPwd.2

### Patch Changes

- feat: loading block

## 3.0.7-secondPwd.1

### Patch Changes

- feat: SwitchComponent 兜底逻辑

## 3.0.7-secondPwd.0

### Patch Changes

- feat: useLogin 增加 source

## 3.0.7-waveScan.3

### Patch Changes

- fix: 代码错误

## 3.0.7-waveScan.2

### Patch Changes

- feat: wave 扫码

## 3.0.7-waveScan.1

### Patch Changes

- fix: 类型问题

## 3.0.7-waveScan.0

### Patch Changes

- feat: 测试 wave 扫码

## 3.0.6

### Patch Changes

- 697a1078: fix: render null
- 17da2d67: feat: source
- a130c391: fix: useWave 判断
- bf28af7f: fix: 展示逻辑
- ef56b01c: feat: switch
- fix: 阉割 wave

## 3.0.6-secondPwd.2

### Patch Changes

- 697a1078: fix: render null
- 17da2d67: feat: source
- a130c391: fix: useWave 判断
- bf28af7f: fix: 展示逻辑
- ef56b01c: feat: switch
- merge from zt

## 3.0.6-secondPwd.1

### Patch Changes

- feat: 测试错误端口

## 3.0.6-secondPwd.0

### Patch Changes

- feat: merge from antd global

## 3.0.6-switch.3

### Patch Changes

- fix: render null

## 3.0.6-switch.2

### Patch Changes

- fix: 展示逻辑

## 3.0.6-switch.1

### Patch Changes

- fix: useWave 判断

## 3.0.6-switch.0

### Patch Changes

- feat: source

## 3.0.5

### Patch Changes

- feat: antd global 删除 正式
- f5dea322: fix: global less

## 3.0.5-globalStyle.0

### Patch Changes

- fix: global less

## 3.0.5-secondPwd.2

### Patch Changes

- feat: onCancel

## 3.0.5-secondPwd.1

### Patch Changes

- feat: handleClose

## 3.0.5-secondPwd.0

### Patch Changes

- feat: 日志收集整理

## 3.0.4

### Patch Changes

- fix: scrollbar style

## 3.0.4-switch.0

### Patch Changes

- feat: switch

## 3.0.4-secondPwd.2

### Patch Changes

- fix: bugs

## 3.0.4-secondPwd.1

### Patch Changes

- feat: switch componen

## 3.0.4-secondPwd.0

### Patch Changes

- feat: secondpwd

## 3.0.3

### Patch Changes

- fix: mfa env

## 3.0.2

### Patch Changes

- feat: getClientId withCredentials

## 3.0.1

### Patch Changes

- fix: wave 优先级

## 3.0.1-secondPwd.0

### Patch Changes

- feat: getFp
- 41abaa67: feat: test
- 66d32a67: feat: 调试
- 1bb7e6ce: fix: mfa/second/check
- 2fd4e73e: feat: 测试 secondPwd

## 3.0.0

### Major Changes

- b47c50f2: fix: some error

### Patch Changes

- feat: wave zt 对接
- 3f7f20e3: fix: waveLogin 判断逻辑
- 154ba589: feat: toast
- 319cf04a: fix: clipboardRef
- e88d3512: feat: pc copy
- 1ca1f65f: fix: getPort
- 86e449c2: fix: p 标签
- 3d096cad: feat: 更换海外 pp 域名
- 543af62d: fix: copyId
- 929acde0: feat: copy
- 7cd649b9: fix: copy-icon style
- 0a83ce03: feat: 海外 domain
- 00bd0303: fix: 类型错误
- 6d0b85e6: fix: km error
- 943f54d9: fix: empty description
- e94e2b74: feat: 测试环境
- e936c6b6: fix: promise any
- 746f87dd: feat: test
- ab2b354f: feat: test
- 15ec2777: feat: pp 环境 wave 配置

## 3.0.0-kmouter.17

### Patch Changes

- feat: 更换海外 pp 域名

## 3.0.0-kmouter.16

### Patch Changes

- fix: copy-icon style

## 3.0.0-kmouter.15

### Patch Changes

- feat: pp 环境 wave 配置

## 3.0.0-kmouter.14

### Patch Changes

- feat: 海外 domain

## 3.0.0-kmouter.13

### Patch Changes

- fix: waveLogin 判断逻辑

## 3.0.0-secondPwd.24

### Patch Changes

- fix: userCode

## 3.0.0-secondPwd.23

### Patch Changes

- fix: mfa/second/check

## 3.0.0-secondPwd.22

### Patch Changes

- fix: useCode

## 3.0.0-secondPwd.21

### Patch Changes

- feat: test

## 3.0.0-secondPwd.20

### Major Changes

- b47c50f2: fix: some error

### Patch Changes

- 154ba589: feat: toast
- 319cf04a: fix: clipboardRef
- e88d3512: feat: pc copy
- 1ca1f65f: fix: getPort
- 86e449c2: fix: p 标签
- feat: 调试
- 543af62d: fix: copyId
- 929acde0: feat: copy
- 00bd0303: fix: 类型错误
- 6d0b85e6: fix: km error
- 943f54d9: fix: empty description
- e936c6b6: fix: promise any
- ab2b354f: feat: test

## 3.0.0-kmouter.12

### Patch Changes

- fix: clipboardRef

## 3.0.0-kmouter.11

### Patch Changes

- fix: copyId

## 3.0.0-kmouter.10

### Patch Changes

- fix: 类型错误

## 3.0.0-kmouter.9

### Patch Changes

- feat: pc copy

## 3.0.0-kmouter.8

### Patch Changes

- feat: toast

## 3.0.0-kmouter.7

### Patch Changes

- feat: test

## 3.0.0-kmouter.6

### Patch Changes

- feat: copy

## 3.0.0-kmouter.5

### Patch Changes

- fix: p 标签

## 3.0.0-kmouter.4

### Patch Changes

- fix: promise any

## 3.0.0-kmouter.3

### Patch Changes

- fix: getPort

## 3.0.0-kmouter.2

### Patch Changes

- fix: empty description

## 3.0.0-kmouter.1

### Major Changes

- fix: some error

## 2.0.6-kmouter.0

### Patch Changes

- fix: km error
- e94e2b74: feat: 测试环境
- 746f87dd: feat: test

## 2.0.5

### Patch Changes

- 3aabfeef: feat: merge from fix/wave
- d6a1efe1: feat: 复原 km 需求
- 94c88417: fix: app_key
- 66c321cf: fix: wave err msg
- 50591dd8: fix: lock 轮训
- d711b7f8: feat: timeout
- 9aaaf28b: fix: render
- 355d7b1b: feat: 删除日志
- 9ffbd699: feat: 调试
- fb2b183f: feat: 测试
- 898b1055: feat: test km
- feat: 1122 版本
- 53fb3e5a: fix: SOURCE_FROM_ZT
- a8e16390: fix: SilentMethods 类型
- 2e004e71: feat: extraRequest 增加 app_key
- bb9401c2: feat: 修改 各个环境 appId
- 2dc84727: fix: GetRegisterPublicKeyRes
- 6ddb4344: feat: test source
- 67339665: feat: 测试
- 9878270b: feat: 测试 km

## 2.0.5-secondPwd.19

### Patch Changes

- feat: 测试 secondPwd

## 2.0.5-chores.18

### Patch Changes

- feat: test

## 2.0.5-1206.17

### Patch Changes

- feat: 测试环境

## 2.0.5-km.16

### Patch Changes

- fix: app_key

## 2.0.5-km.15

### Patch Changes

- fix: lock 轮训

## 2.0.5-km.14

### Patch Changes

- feat: 复原 km 需求

## 2.0.5-km.13

### Patch Changes

- fix: wave err msg

## 2.0.5-km.12

### Patch Changes

- feat: 删除日志

## 2.0.5-km.11

### Patch Changes

- feat: 调试

## 2.0.5-km.10

### Patch Changes

- feat: 测试

## 2.0.5-km.9

### Patch Changes

- fix: SOURCE_FROM_ZT

## 2.0.5-km.8

### Patch Changes

- feat: 修改 各个环境 appId

## 2.0.5-km.7

### Patch Changes

- feat: timeout

## 2.0.5-km.6

### Patch Changes

- fix: GetRegisterPublicKeyRes

## 2.0.5-km.5

### Patch Changes

- fix: render

## 2.0.5-km.4

### Patch Changes

- fix: SilentMethods 类型

## 2.0.5-km.3

### Patch Changes

- feat: test source

## 2.0.5-km.2

### Patch Changes

- feat: 测试

## 2.0.5-km.1

### Patch Changes

- feat: extraRequest 增加 app_key

## 2.0.5-km.0

### Patch Changes

- feat: test km

## 2.0.4

### Patch Changes

- upd: locales

## 2.0.3

### Patch Changes

- fix: 体验问题

## 2.0.2-km.8

### Patch Changes

- feat: merge from fix/wave

## 2.0.2-km.7

### Patch Changes

- feat: 测试 km

## 2.0.2

### Patch Changes

- 177ff304: feat: 增加日志
- fc962a1d: fix: wave
- 137eaf23: feat: 测试
- 99308937: fix: slience wave
- fix: wave 免登问题
- 8cf8c4a9: feat: 完善域名
- 93313231: fix: wave
- f4927e33: fix: wave silence
- de4a4648: feat: 删除日志

## 2.0.2-slienceWave.7

### Patch Changes

- feat: 完善域名

## 2.0.2-slienceWave.6

### Patch Changes

- feat: 删除日志

## 2.0.2-slienceWave.5

### Patch Changes

- feat: 增加日志

## 2.0.2-slienceWave.4

### Patch Changes

- feat: 测试

## 2.0.2-slienceWave.3

### Patch Changes

- fix: wave

## 2.0.2-slienceWave.2

### Patch Changes

- fix: wave

## 2.0.2-slienceWave.1

### Patch Changes

- fix: wave silence

## 2.0.2-slienceWave.0

### Patch Changes

- fix: slience wave

## 2.0.1

### Patch Changes

- fix: locales

## 2.0.0

### Major Changes

- feat: 支持个人中心，注册

### Minor Changes

- 564b2120: 测试 pc-register

### Patch Changes

- 2b9d6d29: 更新个人中心 M 端信息展示样式
- fb05da2a: fix: cid
- 71c1b9fc: feat: otp 异常 停止轮训
- 9776cc42: update 个人中心英文展示更新&视觉更新
- aac5be8b: feat: 移动端图片采用 远程资源
- b19431d6: add passwd 账密验证
- 1f034916: fix: 无认证方式不展示二次认证&个人中心 M 端昵称展示规则更新
- 3c06cadd: fix: 解绑公共账号传参错误
- e05cafd9: fix: bugs
- d70a498a: fix: cid
- 9ddc469f: fix: pwd 移动端修改 methodkey 标识
- 4e735432: fix: passwd
- c883b641: feat: input ref focus
- 11776bd8: fix: 解绑公共账号传参错误
- cd0c0d5c: fix: register domain 错误
- cb293889: fix: pc cid
- c80dc71f: fix: style bugs
- 713d45b4: fix: 重复触发
- ********: fix: 修复 添加邮箱的判断
- 74b96c7a: fix: teg url
- 4cf5d792: fix: 多语言问题
- be0f1541: fix: style
- 36d04213: fix: submit
- bc42c7b1: fix: 修复多语言问题
- e425a66b: fix: pc areaCode
- f8813398: test center
- dc58f417: fix: emailId 传递
- cf33af23: fix: 会话失效回调提示问题
- e565c109: fix: personalCenter domain
- e7bfc7bb: fix: bug
- ec0095c0: fix: AccountCenter bugs
- 4c714339: fix: bug
- 4d7c72ac: fix: bugs
- ********: fix: bugs
- ********: fix:个人中心 m 端增加弹窗 tips&样式优化&多语言增加 key
- 1f4e09a5: fix: CircularProgressbar 未引入样式
- b73894c4: register sms 不跳过
- 5dd92080: fix:bugs
- 7ea1e0b2: merge from master
- 4d5a42ac: fix: bug
- 04edb7ad: 迁移 micro
- 5b4b4dd8: feat: 完善多语言
- 4c5c8d4b: feat: 不需要默认语言
- 47154b8e: fix: locales
- f312c49f: fix:更新个人中心 M 端企业邮箱编辑逻辑
- 4ecf4ce6: fix: style
- 0e4995b3: fix: useStep 加密问题
- 1b40eb1d: feat: 样式调整
- eac6d2ae: fix: locales
- f352768a: fix: register showLang
- 7b6f031b: fix: 拼写错误
- 9d03dbbc: fix: handleChangeLang
- f3f2e61a: feat: 修复样式问题，代码优化
- f4cf7d9a: fix: undefined request
- 9ab2d40d: fix: account:phone:bind:tips
- 46fa3ffe: feat: 新增日志调试
- c33fc41f: fix: bugs
- 0eb40f34: fix: 个人中心 M 端增加 PWD 跳转弹窗
- 44ccb459: fix: -3 return
- 051289e5: 注册超时 刷新
- 1ee83b82: fix: bugs
- ae4a9687: fix: style
- 14f0ec48: fix: dcode 取值错误，expiredTime 取值错误
- ef01ef43: fix: 修复空格问题
- e7307df3: feat: 增加日志调试

## 1.4.1-center.64

### Patch Changes

- fix: teg url

## 1.4.1-center.63

### Patch Changes

- fix: locales

## 1.4.1-center.62

### Patch Changes

- fix: style

## 1.4.1-center.61

### Patch Changes

- fix:更新个人中心 M 端企业邮箱编辑逻辑

## 1.4.1-center.60

### Patch Changes

- fix: locales

## 1.4.1-center.59

### Patch Changes

- fix: style

## 1.4.1-center.58

### Patch Changes

- fix: cid

## 1.4.1-center.57

### Patch Changes

- fix: pc cid

## 1.4.1-center.56

### Patch Changes

- fix: cid

## 1.4.1-center.55

### Patch Changes

- fix: personalCenter domain

## 1.4.1-center.54

### Patch Changes

- fix: bugs

## 1.4.1-center.53

### Patch Changes

- fix:个人中心 m 端增加弹窗 tips&样式优化&多语言增加 key

## 1.4.1-center.52

### Patch Changes

- fix: 个人中心 M 端增加 PWD 跳转弹窗

## 1.4.1-center.51

### Patch Changes

- fix: bug

## 1.4.1-center.50

### Patch Changes

- fix: bug

## 1.4.1-center.49

### Patch Changes

- fix: pwd 移动端修改 methodkey 标识

## 1.4.1-center.48

### Patch Changes

- fix: 无认证方式不展示二次认证&个人中心 M 端昵称展示规则更新

## 1.4.1-center.47

### Patch Changes

- 更新个人中心 M 端信息展示样式

## 1.4.1-center.46

### Patch Changes

- update 个人中心英文展示更新&视觉更新

## 1.4.1-center.45

### Patch Changes

- fix: style bugs

## 1.4.1-center.44

### Patch Changes

- add passwd 账密验证

## 1.4.1-center.43

### Patch Changes

- fix: passwd

## 1.4.1-center.42

### Patch Changes

- fix: bugs

## 1.4.1-center.41

### Patch Changes

- fix: account:phone:bind:tips

## 1.4.1-center.40

### Patch Changes

- fix: bug

## 1.4.1-center.39

### Patch Changes

- fix: bugs

## 1.4.1-center.38

### Patch Changes

- fix: pc areaCode

## 1.4.1-center.37

### Patch Changes

- feat: 样式调整

## 1.4.1-center.36

### Patch Changes

- fix: style

## 1.4.1-center.35

### Patch Changes

- fix: bugs

## 1.4.1-center.34

### Patch Changes

- fix: bugs

## 1.4.1-center.33

### Patch Changes

- fix: undefined request

## 1.4.1-center.32

### Patch Changes

- fix: AccountCenter bugs

## 1.4.1-center.31

### Patch Changes

- 注册超时 刷新

## 1.4.1-center.30

### Patch Changes

- fix: 重复触发

## 1.4.1-center.29

### Patch Changes

- fix: submit

## 1.4.1-center.28

### Patch Changes

- register sms 不跳过

## 1.4.1-center.27

### Patch Changes

- feat: 移动端图片采用 远程资源

## 1.4.1-center.26

### Patch Changes

- merge from master

## 1.4.1-center.25

### Patch Changes

- fix: CircularProgressbar 未引入样式

## 1.4.1-center.24

### Patch Changes

- fix: register domain 错误

## 1.4.1-center.23

### Patch Changes

- feat: otp 异常 停止轮训

## 1.4.1-center.22

### Patch Changes

- fix:bugs

## 1.4.1-center.21

### Patch Changes

- 迁移 micro

## 1.4.1-center.20

### Patch Changes

- fix: 会话失效回调提示问题

## 1.4.1-center.19

### Patch Changes

- fix: register showLang

## 1.4.1-center.18

### Patch Changes

- fix: 多语言问题

## 1.4.1-center.17

### Patch Changes

- feat: 新增日志调试

## 1.4.1-center.16

### Patch Changes

- fix: handleChangeLang

## 1.4.1-center.15

### Patch Changes

- fix: 修复多语言问题

## 1.4.1-center.14

### Patch Changes

- fix: 修复 添加邮箱的判断

## 1.4.1-center.13

### Patch Changes

- fix: 解绑公共账号传参错误

## 1.4.1-center.12

### Patch Changes

- fix: 解绑公共账号传参错误

## 1.4.1-center.11

### Patch Changes

- fix: -3 return

## 1.4.1-center.10

### Patch Changes

- feat: 不需要默认语言

## 1.4.1-center.9

### Patch Changes

- feat: 增加日志调试

## 1.4.1-center.8

### Patch Changes

- feat: 完善多语言

## 1.4.1-center.7

### Patch Changes

- fix: emailId 传递

## 1.4.1-center.6

### Patch Changes

- fix: 拼写错误

## 1.4.1-center.5

### Patch Changes

- fix: 修复空格问题

## 1.4.1-center.4

### Patch Changes

- feat: 修复样式问题，代码优化

## 1.4.1-center.3

### Patch Changes

- feat: input ref focus

## 1.4.1-center.2

### Patch Changes

- fix: useStep 加密问题

## 1.4.1-center.1

### Patch Changes

- fix: dcode 取值错误，expiredTime 取值错误

## 1.4.1-center.0

### Patch Changes

- test center

## 1.4.1

### Patch Changes

- feat: 0920 latest
- 94feb734: feat: 企微扫码流程优化
- 16616d0a: feat: 企微流程优化，不再请求 second check 相关逻辑

## 1.4.1-wxscan.1

### Patch Changes

- feat: 企微流程优化，不再请求 second check 相关逻辑

## 1.4.1-wxscan.0

### Patch Changes

- feat: 企微扫码流程优化

## 1.4.0

### Minor Changes

- feat: 接入平台组设备指纹 节流 trailing

### Patch Changes

- 1d484f91: feat:wxscan
- 0a420bf3: wxscan 优化
- 62714e07: feat: 企微优化回退，delay 2000ms
- 435317b1: feat: 企微兼容老逻辑
- e9670cfa: trailing

## 1.3.1-wxscan.3

### Patch Changes

- trailing

## 1.3.1-wxscan.2

### Patch Changes

- feat: 企微优化回退，delay 2000ms

## 1.3.1-wxscan.1

### Patch Changes

- feat: 企微兼容老逻辑

## 1.3.1-wxscan.0

### Patch Changes

- feat:wxscan
- 0a420bf3: wxscan 优化

## 1.3.0

### Minor Changes

- feat: 修改密码重置密码流程

### Patch Changes

- d4b1a25e: feat: 密码不 trim
- 40ea044a: feat: 操作 db catch error
- ed5613eb: feat: 域账号 forget 跳转 passwd
- dfcf0774: 修复 backErr
- 4c65ceb5: mfa 新增 scene 参数
- 8d7dd0a2: 更新多语言词条
- 15bb065d: wave code test
- 18e51bf9: 密码 不 trim
- ec6c83ed: 咕咕请求失败兜底逻辑
- 65c16b84: 修改密码 新增 域账号 增加极验以及 totp
- e9d99d04: 密码不 trim
- c3b9f0b5: upd: i18n cache
- 29979ecf: 修改 pp passwd 地址
- b7793ce7: test waveotp
- e0408a6e: 删除日志代码，修改密码 二次认证流程
- a00b143e: fix: 2FAConfigs 逻辑
- 50142aa5: wave_otp
- 9e25fbe6: fix: forget loading 兜底逻辑

## 1.2.24-passwd.14

### Patch Changes

- 修改 pp passwd 地址

## 1.2.24-passwd.13

### Patch Changes

- feat: 密码不 trim

## 1.2.24-passwd.12

### Patch Changes

- feat: 操作 db catch error

## 1.2.24-passwd.11

### Patch Changes

- feat: 域账号 forget 跳转 passwd
- 15bb065d: wave code test
- b7793ce7: test waveotp
- 50142aa5: wave_otp

## 1.2.26-waveOtp.8

### Patch Changes

- wave code test

## 1.2.26-waveOtp.1

### Patch Changes

- wave_otp

## 1.2.26-waveOtp.0

### Patch Changes

- test waveotp

## 1.2.24-passwd.10

### Patch Changes

- fix: 2FAConfigs 逻辑

## 1.2.24-passwd.9

### Patch Changes

- 密码不 trim

## 1.2.24-passwd.8

### Patch Changes

- 密码 不 trim

## 1.2.24-passwd.7

### Patch Changes

- 修复 backErr

## 1.2.25

### Patch Changes

- release

## 1.2.24

### Patch Changes

- 27d0ba6: 微信免登 bugfix
- release
- d975f57: wechat login

## 1.2.24-beta.1

### Patch Changes

- 微信免登 bugfix

## 1.2.24-beta.0

### Patch Changes

- wechat login

## 1.2.24-passwd.6

### Patch Changes

- 咕咕请求失败兜底逻辑

## 1.2.24-passwd.5

### Patch Changes

- 更新多语言词条

## 1.2.24-passwd.4

### Patch Changes

- fix: forget loading 兜底逻辑

## 1.2.24-passwd.3

### Patch Changes

- upd: i18n cache

## 1.2.24-passwd.2

### Patch Changes

- mfa 新增 scene 参数

## 1.2.24-passwd.1

### Patch Changes

- 删除日志代码，修改密码 二次认证流程

## 1.2.24-passwd.0

### Patch Changes

- 修改密码 新增 域账号 增加极验以及 totp

## 1.2.23

### Patch Changes

- 1.2.23

## 1.2.22

### Patch Changes

- 43ce1d1d: 企微 mfa 通过 searchparam iamWxMfa 区分
- 3f1ba604: 合并 0712 版本
- 99bb21d7: test
- d51bef03: test
- 99bb21d7: test
- 99bb21d7: test
- e179d9cd: test
- 06226545: 修改获取咕咕获取多语言接口
- e179d9cd: test
- 支持 wave 免登 prod
- 7cff7a10: 修改背景图在火狐浏览器的兼容问题

## 1.2.22-wave.1

### Patch Changes

- 修改获取咕咕获取多语言接口

## 1.2.22-wave.0

### Patch Changes

- 43ce1d1: 企微 mfa 通过 searchparam iamWxMfa 区分
- 合并 0712 版本
- 99bb21d: test
- d51bef0: test
- 99bb21d: test
- 99bb21d: test
- e179d9c: test
- e179d9c: test
- 7cff7a1: 修改背景图在火狐浏览器的兼容问题

## 1.2.21

### Patch Changes

- 极验对接 0712 生产版本

## 1.2.21-wave.27

### Patch Changes

- test

## 1.2.21-wave.26

### Patch Changes

- test

## 1.2.21-wave.25

### Patch Changes

- test

## 1.2.21-wave.24

### Patch Changes

- test

## 1.2.21-wave.23

### Patch Changes

- test

## 1.2.21-wave.22

### Patch Changes

- test

## 1.2.21-wave.21

### Patch Changes

- test

## 1.2.21-wave.20

### Patch Changes

- 修改背景图在火狐浏览器的兼容问题

## 1.2.21-wave.19

### Patch Changes

- 企微 mfa 通过 searchparam iamWxMfa 区分

## 1.2.21-gee.18

### Patch Changes

- 修复节流问题

## 1.2.21-gee.17

### Patch Changes

- 升级极验版本

## 1.2.21-gee.16

### Patch Changes

- 极验问题修复

## 1.2.21-gee.13

### Patch Changes

- 人机校验升级为极验

## 1.2.21-changeset.12

### Patch Changes

- 迁移 vue 目录

## 1.2.21-changeset.11

### Patch Changes

- change

## 1.2.21-changeset.10

### Patch Changes

- 再次测试云端构建

## 1.2.21-changeset.9

### Patch Changes

- { "extends": "../../tsconfig.base.json", "include": [ "../../typings.d.ts", "src" ], "compilerOptions": { "skipLibCheck": true, "declarationDir": "dist/esm", "emitDeclarationOnly": true, "outDir": "dist/esm", "baseUrl":

## 1.2.21-changeset.8

### Patch Changes

- 再次测试云端构建

## 1.2.21-changeset.7

### Patch Changes

- 再次测试云端构建

## 1.2.21-changeset.6

### Patch Changes

- 再次测试云端构建

## 1.2.21-changeset.5

### Patch Changes

- 再次测试云端构建

## 1.2.21-changeset.4

### Patch Changes

- 72b3c70: 再次测试云端构建

## 1.2.21-changeset.3

### Patch Changes

- 497506c: 再次测试云端构建

## 1.2.21-changeset.2

### Patch Changes

- 788aeee: 再次测试云端构建

## 1.2.21-changeset.1

### Patch Changes

- c5f8908: 再次测试云端构建

## 1.2.21-changeset.0

### Patch Changes

- 62fe917: changeset 云端构建测试

## 1.2.20

### Patch Changes

- aebc8da: 测试 changeset 流程
