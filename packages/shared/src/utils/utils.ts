import {
  config,
  getZTTicket,
  openWith<PERSON>rowser,
  refreshZTTokenCookie,
} from '@hoyowave/jsapi';
import { requestPostMessage, sendPostMessage } from '@iam/message-channel';
import fingerprint from '@plat/mihoyo-device-fingerprint-jssdk';
import locale from '@shared/locales';
import {
  authKeyRoute,
  authnV3CaptchaGetTicketApi,
  authnV3checkNetWorkApi,
  authnV3getMfaConfigApi,
  authnV3GetPublicKeyAndHashApi,
  authnV3LogOutApi,
  authnV3WaveSilentLoginAPi,
  createBindOtpLinkApi,
  createFollowTokenApi,
  getRegisterPublicKeyApi,
  getSilentMethodsApi,
  getWaveJsTicketAndAppIdApi,
  getWaveJsTicketApi,
  logOutApi,
  mfaUserCheck,
  personalAccountGee<PERSON>pi,
  personalAccountLangGet,
  personalAccountLangSet,
  registerPasswd<PERSON><PERSON>,
  v2GetSecondCheck<PERSON><PERSON>,
} from '@shared/routes';
import {
  checkMFARes,
  ComponentType,
  CreateBindOtpLinkRes,
  CreateFlowTokenRes,
  Env,
  GeetestResponse,
  GetAuthKey,
  GetPreCheck,
  GetRegisterPublicKeyRes,
  GetSecondCheck,
  GetSysClientId,
  ISentryCapture,
  IV3_SCENE_TYPE,
  IV3AuthMethodConfigs,
  IV3AuthRes,
  IV3CaptchaTicketRes,
  IV3GetAuthKeyRes,
  IV3Method,
  IV3MfaConfig,
  IV3MfaConfigRes,
  IV3PerCheckParams,
  IV3PhoneAreaCode,
  IV3WaveJsTicketRes,
  Lang,
  LangRes,
  Method,
  MfaCheckRes,
  MfaCodeCheckRes,
  MFARes,
  PhoneAreaCode,
  PreCheckParams,
  PwdVerify,
  Response,
  SecondAuth,
  SilentMethodsRes,
  WaveJsTicketRes,
} from '@shared/types';
import {
  accountDomainMap,
  authApiPrefix,
  componentTypeToIAmOAuth2UrlPathname,
  CURRENT_METHOD_FOR_PROTOCOL_KEY,
  GRAY_COOKIE_KEY,
  MethodToApiMap,
  otpApiPrefix,
  passwdDomainMap,
  personalCenterApiPrefix,
  preAuthApiPrefix,
  QUICK_LOGIN_FOR_PROTOCOL_KEY,
  registerDomainMap,
  REQUIRE_AUTH_CODE,
  SDK_ACTION,
  wxCodeLoginUrlMap,
} from '@shared/utils/map';
import request, { requestGet } from '@shared/utils/request';
import { dispatch, subscribe } from '@shared/utils/store';
import Wave from '@shared/utils/wave';
import Zt from '@shared/utils/zt';
import Cookies from 'js-cookie';
import { JSEncrypt } from 'jsencrypt';
import queryString, { parse as parseUrl } from 'query-string';
import UAParser from 'ua-parser-js';
import { getTLD } from './domain';
import message from '@shared/utils/message';

const getLang = () => {
  let language = '';
  subscribe(({ lang }) => {
    language = lang;
  }, 'changeLang');
  return language as unknown as Lang;

  // return localStorage.getItem(LANGUAGE_KEY) as unknown as Lang;
};

const getClient = () => {
  let _clientId = '';
  subscribe(({ clientId }) => {
    _clientId = clientId;
  }, 'clientId');
  return _clientId;

  // return localStorage.getItem('clientId') || '';
};

const getSentryCapture = () => {
  let _sentryCapture: ISentryCapture = {
    sentryCaptureMessage: () => {},
    sentryCaptureException: () => {},
  };
  subscribe((sentryCapture) => {
    _sentryCapture = sentryCapture;
  }, 'sentry');
  return _sentryCapture;
};

const getPageCode = () => {
  let _loginPageConfigCode = '';
  subscribe(({ loginPageConfigCode }) => {
    _loginPageConfigCode = loginPageConfigCode;
  }, 'loginPageConfigCode');
  return _loginPageConfigCode;

  // return localStorage.getItem('loginPageConfigCode') || '';
};

const getRequestGlobalHeader = () => {
  let _globalHeader = {};
  subscribe(({ globalHeader }) => {
    _globalHeader = globalHeader;
  }, 'globalHeader');
  return _globalHeader;
};

const getEnv = () => {
  let _env = '';
  subscribe(({ env }) => {
    _env = env;
  }, 'env');
  return (_env || 'prod') as Env;
};

const setRequestGlobalHeader = (globalHeader: Record<string, any>) => {
  dispatch({ globalHeader: globalHeader }, true, 'globalHeader');
};

interface SetComponentVisible {
  visible: boolean;
  component: JSX.Element;
}

const setVisible = (_: SetComponentVisible) => {
  const { visible, component } = _;
  if (visible) {
    return component;
  }
  return null;
};

const rsa = (publicKey: string, password: string) => {
  const instance = new JSEncrypt();
  const _publicKey = publicKey
    .replace(/-----BEGIN RSA PUBLIC KEY-----/, '')
    .replace(/-----END RSA PUBLIC KEY-----/, '')
    .replace(/\n/g, '');
  instance.setPublicKey(_publicKey);
  const _password = instance.encrypt(password);
  if (!_password) {
    return '';
  }
  return _password as string;
};

// 表单提交
const handleSubmit = async (val: any, authApi: string, active: IV3Method) => {
  const api = MethodToApiMap.get(active);
  const data = await request<IV3AuthRes>(`${authApi}/${api}`, {
    ...val,
  });
  if (data.code === 0) {
    return {
      isLogin: true,
      mfa: null,
      ticket: data?.data?.ticket,
    };
  }
  if (data.code === -5) {
    return {
      isLogin: false,
      mfaConfig: data?.data?.['methods'],
    };
  }
  return {
    isLogin: false,
    mfaConfig: null,
    error: {
      code: data.code,
      message: data.message,
    },
  };
};

// 预检
const getPreCheck = async (
  env: Env,
  captcha: boolean,
  params: any,
  noCheckCallback: (val: IV3PerCheckParams) => Promise<void>,
  checkCallback: (captcha_id: string) => Promise<any>,
  rejectFn: () => void,
) => {
  if (!captcha) {
    return noCheckCallback(null);
  }
  const data = await request<IV3CaptchaTicketRes>(
    `${authApiPrefix[env]}/${authnV3CaptchaGetTicketApi}`,
    {
      ...params,
    },
  );
  if (data.code === 0) {
    const {
      data: { captcha_id = '' },
      ...rest
    } = data?.data;
    if (captcha_id) {
      return checkCallback(captcha_id).then((data: GeetestResponse) => {
        const captcha = {
          ...rest,
          data,
        };
        return noCheckCallback(captcha);
      });
    } else {
      rejectFn && rejectFn();
      return null;
    }
  } else {
    // 直接报错
    rejectFn && rejectFn();
    return null;
  }
};

const getPreCheckToPersonalCenter = async (
  env: Env,
  captcha: 0 | 1,
  params: any,
  noCheckCallback: (val: PreCheckParams) => Promise<void>,
  checkCallback: (val: PreCheckParams) => Promise<PreCheckParams>,
  rejectFn: () => void,
) => {
  if (!captcha) {
    return noCheckCallback(null);
  }
  // 云端 所需设备信息
  let _navigator = {};
  for (let key in navigator) {
    _navigator[key] = navigator[key];
  }
  // true 必现 code 400
  // _navigator['webdriver'] = true;
  const data = await request<GetPreCheck>(
    `${personalCenterApiPrefix[env]}/${personalAccountGeeApi}`,
    {
      ...params,
      sig: window.btoa(JSON.stringify(_navigator)),
    },
  );
  if (data.code === 0) {
    const { code, ...rest } = data?.data;
    // 预检通过，直接走业务逻辑
    if (code === 200) {
      return noCheckCallback(rest);
    }
    // 需要人机验证，获取人机凭证后异步走业务逻辑
    if (code === 400) {
      return checkCallback(rest).then(noCheckCallback);
    } else {
      // 直接报错
      rejectFn && rejectFn();
      return null;
    }
  }
};

// 获取 公钥和hash盐
const getKey = async (authApi: string, clientId: string) => {
  const data = await request<GetAuthKey>(`${authApi}/${authKeyRoute}`, {
    clientId,
  });
  if (data.code === 0) {
    return data?.data;
  }
};

// v3 获取 公钥和hash盐
const getKeyV3 = async (authApi: string, clientId: string) => {
  const data = await request<IV3GetAuthKeyRes>(
    `${authApi}/${authnV3GetPublicKeyAndHashApi}`,
    {
      clientId,
    },
  );
  if (data.code === 0) {
    return data?.data;
  }
};

const getKeyToRegister = async (
  personalCenterApi: string,
  clientId: string,
) => {
  const data = await request<GetAuthKey>(
    `${personalCenterApi}/${registerPasswdKey}`,
    {
      clientId,
    },
  );
  if (data.code === 0) {
    return data?.data;
  }
  return {} as unknown as {
    publicKey: string; // 公钥
    hash: string; //  hash盐
  };
};

// 设备标识
const getVisitorId = () => {
  return fingerprint.getDeviceFp();
};

const getAppKey = () => {
  let appKey = '';
  subscribe((val) => {
    appKey = val?.appKey;
  }, 'appKey');
  subscribe((val) => {
    appKey = val?.appKey;
  }, 'iam-config');
  return appKey || '';
};

const getGrey = () => {
  return Cookies.get(GRAY_COOKIE_KEY) || '';
};

// 判断是否企微环境
const getIsWxWorkEnv = () => {
  const lowerCaseUa = navigator.userAgent.toLowerCase();
  return lowerCaseUa.includes('wxwork');
};

const getIsWaveEnv = () => {
  const lowerCaseUa = navigator.userAgent.toLowerCase();
  return lowerCaseUa.includes('hoyowave');
};

// 企微认证
const checkWxWorkUserInfo = async (url: string, code: string | string[]) => {
  const params = {
    code: Array.isArray(code) ? code.at(-1) : code,
  };
  const data = await request(url, { ...params });
  return data?.code === 0;
};

// 通过当前组件类型 获取 企微授权链接 pathname
const getWxPathnameByComponentType = (type: ComponentType) => {
  return componentTypeToIAmOAuth2UrlPathname[type];
};

const createWxOAuth2UrlByHref = (env: Env, search?: string) => {
  // const pathname = getWxPathnameByPathname(env);
  // const iamWxUrl = iamWxUrlMap[env].replace('*', pathname);
  const iamWxUrl = wxCodeLoginUrlMap[env];
  return iamWxUrl + search;
  // return iamWxUrl + window.encodeURIComponent(location.href);
};

const getIframe = () => {
  return !(window.self === window.top);
};

// const parseSearch = () => {
//   return parseUrl(location.search);
// };

const getUA = () => {
  const uap = UAParser();
  return {
    browser: uap?.browser,
    os: uap?.os,
    plat: uap?.device,
  };
};

const getWxLang = (lang: Lang) => {
  return lang.includes('zh') ? 'zh' : 'en';
};

// 二次认证提交
const secondAuthSubmit = async (
  val: any,
  method: IV3Method,
  authApi: string,
  clientId: string,
  cb: (mfaCode?: string) => Promise<void>,
) => {
  let params: {};
  if (method === IV3Method.SecondPwd) {
    const authKey = await getKeyV3(authApi, clientId);
    const { public_key = '', hash = '' } = authKey || {};
    const { password } = val;
    const _password = rsa(public_key, password + hash);
    params = {
      ...val,
      password: _password,
    };
  } else {
    params = {
      ...val,
    };
  }
  const api = MethodToApiMap.get(method);
  const data = await request<IV3AuthRes>(`${authApi}/${api}`, { ...params });
  if (data.code === 0) {
    await cb(data?.data?.ticket);
  } else {
    // -3 ?
    return {
      error: data,
    };
  }
};

const userCenterSecondAuthSubmit = async (
  val: any,
  method: IV3Method,
  personalCenterApi: string,
  cb: () => Promise<void>,
) => {
  let params: {};
  const api = MethodToApiMap.get(method);
  const data = await request<MFARes>(`${personalCenterApi}/${api}`, { ...val });
  if (data?.code === 0) {
    await cb();
  }
};

const forgetSubmit = async (
  val: any,
  method: Method,
  authApi: string,
  clientId: string,
) => {
  let params: {};
  if (
    method === Method['ldap:forget'] ||
    method === Method['ldap:account:forget']
  ) {
    const authKey = await getKey(authApi, clientId);
    const { publicKey, hash } = authKey;
    const { password } = val;
    const _password = rsa(publicKey, password + hash);
    params = {
      ...val,
      password: _password,
    };
  } else {
    params = {
      ...val,
    };
  }
  const api = MethodToApiMap.get(method);
  const data = await request<PwdVerify>(`${authApi}/${api}`, { ...params });
  if (data.code === 0) {
    return data?.data;
    // if (data?.data?.contextToken) {
    //   return data?.data?.contextToken;
    // }
    // if (data?.data?.['2FAConfigs']?.length > 0) {
    //   return {
    //     method: data?.data?.['2FAConfigs'],
    //   };
    // }
  }
  // if (CROSS_TENANT_PASSWD_WORK_CODE_LIST.includes(data.code)) {
  //   return data?.Message;
  // }
  return null;
};

const getSecondConfig = (active: IV3Method, method: IV3MfaConfig[]) => {
  return method?.filter((e) => e.method === active)?.[0];
};

const getXRequestId = () => {
  return `front-${Date.now()}-${Math.floor(Math.random() * 10000)}-${Math.floor(
    Math.random() * 10000,
  )}-${Math.floor(Math.random() * 1000000000)}`;
};

const getTimeoutMessage = () => {
  const lang = getLang();
  return locale[lang]?.timeout;
};

const getWaveMsg = () => {
  const lang = getLang();
  return locale[lang]?.['wave:err:message'];
};

const getClientId = async (clientApi: string, hideSysHeader: boolean) => {
  const { code, data } = await request<GetSysClientId>(
    clientApi,
    {},
    {
      withCredentials: hideSysHeader,
      hideSysHeader,
    },
  );
  if (code === 0) {
    const clientId = data?.clientId;
    dispatch({ clientId }, false, 'clientId');
    return clientId;
  }
  return '';
};

const getMfaSecondCheck = async (
  clientId: string,
  userCode: string,
  scene: string,
  env: Env,
) => {
  const { code, data } = await request<GetSecondCheck>(
    `${authApiPrefix[env]}/${v2GetSecondCheckApi}`,
    {
      clientId,
      userCode,
      scene,
    },
  );
  if (code === 0) {
    // 需要二级认证，返回false，并设置二级认证参数
    if (Object.prototype.toString.call(data) === '[object Object]') {
      const { '2FAConfigs': secondAuths = [] } =
        typeof data === 'object' && data;
      if (!!secondAuths?.length) {
        return secondAuths;
      }
    }
    // 不需要二级认证，走ticket
    return [] as unknown as SecondAuth[];
  }
};

const checkMFA = async (env: Env) => {
  const data = await request<checkMFARes>(
    `${authApiPrefix[env]}/${mfaUserCheck}`,
    {},
  );
  if (data.code === 0) {
    return data?.data?.checked;
  }
  return false;
};

const lineToHump = (str: string): string => {
  return str.replace(/_(\w)/g, (_, char) => {
    return char.toUpperCase();
  });
};

const formatProps = (props: any): any => {
  const _query = {};
  Object.keys(props).forEach((key) => {
    _query[lineToHump(key)] = props[key];
  });
  return _query;
};

const sortAreaCode = (areaCodes: IV3PhoneAreaCode[]) => {
  const result = areaCodes.slice();
  result.sort((a, b) => {
    // 先按 sortedGroup 降序排列
    if (a.sorted_group !== b.sorted_group) {
      return b.sorted_group - a.sorted_group;
    } else {
      // sortedGroup 相同，按 sorted 降序排列
      return b.sorted - a.sorted;
    }
  });

  return result;
};

const sortUserCenterAreaCode = (areaCodes: PhoneAreaCode[]) => {
  const result = areaCodes.slice();
  result.sort((a, b) => {
    // 先按 sortedGroup 降序排列
    if (a.sortedGroup !== b.sortedGroup) {
      return b.sortedGroup - a.sortedGroup;
    } else {
      // sortedGroup 相同，按 sorted 降序排列
      return b.sorted - a.sorted;
    }
  });

  return result;
};

const getGeeLang = (lang: Lang) => {
  return lang === 'zh-CN' ? 'zho' : 'eng';
};

const getRemoteLocale = async (env: Env) => {
  const apiPrefix = personalCenterApiPrefix[env];
  const data = await request<LangRes>(
    `${apiPrefix}/${personalAccountLangGet}`,
    {},
  );
  if (data?.code === 0) {
    return data.data?.language;
  }
  const cookieLang = Cookies.get('x-mi-iaac-language');
  return (cookieLang || 'zh-CN') as unknown as Lang;
};

const setRemoteLocale = async (lang: Lang, env: Env) => {
  const apiPrefix = personalCenterApiPrefix[env];
  const data = await request<Response>(
    `${apiPrefix}/${personalAccountLangSet}`,
    {
      language: lang,
    },
  );
  return data?.code === 0;
};

const getCidFormId = (id: string, arr: PhoneAreaCode[]) => {
  return arr?.find((e) => e?.id === id);
};

const getIdFormCid = (cid: string, arr: PhoneAreaCode[]) => {
  return arr?.find((e) => e?.value === cid);
};

const getSilentMethods = async (env: Env) => {
  const api = authApiPrefix[env] + '/' + getSilentMethodsApi;
  const data = await request<SilentMethodsRes>(api, {});
  if (data?.code === 0) {
    return data?.data;
  }
};

const handleRefresh = () => {
  reloadLocation();
};

const waveLogin = async (
  code: string,
  env: Env,
  zt_ticket: string,
): Promise<Response<{ ticket?: string }>> => {
  const data = await request<Response>(
    `${authApiPrefix[env]}/${authnV3WaveSilentLoginAPi}`,
    {
      verify_code: code,
      otp_scene: IV3_SCENE_TYPE.InitialLogin,
      is_from_wave: true,
      zt_ticket,
    },
  );
  if (data.code === 0) {
    return data;
  }
  return {} as unknown as Response;
};

const quickLogin = async (
  code: string,
  ticket: string,
  env: Env,
  remember_me: boolean,
  is_from_wave = false,
) => {
  return await request<Response>(
    `${authApiPrefix[env]}/${authnV3WaveSilentLoginAPi}`,
    {
      verify_code: code,
      zt_ticket: ticket,
      otp_scene: IV3_SCENE_TYPE.InitialLogin,
      remember_me,
      is_from_wave,
    },
  );
};

const loginZtWithWaveClient = async (env: Env, clientId: string) => {
  const waveInstance = new Wave(env, clientId);
  const ztInstance = new Zt(env);
  const ticket = await waveInstance.getZtTicket();
  if (ticket) {
    return await ztInstance.ztLogin(ticket);
  } else {
    return {
      success: false,
      msg: 'Page load failed, please retry',
    };
  }
};

const checkZtLogin = async (env: Env) => {
  const ztInstance = new Zt(env);
  return await ztInstance.checkZtLogin();
};

const getEncryptedClientId = async (env: Env, registerSessionId: string) => {
  const data = await request<GetRegisterPublicKeyRes>(
    `${personalCenterApiPrefix[env]}/${getRegisterPublicKeyApi}`,
    {
      registerSessionId,
    },
  );
  if (data?.code === 0) {
    return data?.data;
  }
  return '';
};

const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

const getSecondPwd = (env: Env, lang: Lang) => {
  return `${authApiPrefix[env]}/${authnV3checkNetWorkApi}?type=secondaryPassword&lang=${lang}`;
};

const getPwd = (env: Env, lang: Lang) => {
  return `${passwdDomainMap[env]}?lang=${lang}`;
};

const goSecondPwd = (env: Env, lang: Lang) => {
  const url = `${authApiPrefix[env]}/${authnV3checkNetWorkApi}?&lang=${lang}`;
  const isIframe = getIframe();
  if (isIframe) {
    postOpenLocation(url);
    return;
  }
  window.open(url, '_blank');
};

const checkUserMfa = async (url: string) => {
  const data = await request<MfaCheckRes>(url, {});
  if (data?.code === 0) {
    return data?.data;
  }
};

const mfaCodeCheck = async (url: string, mfaCode: string) => {
  const data = await request<MfaCodeCheckRes>(url, { mfaCode });
  if (data?.code === 0) {
    return data?.data;
  }
};

const formatCurMethods = (authMethodConfigs: IV3AuthMethodConfigs[]) => {
  if (!authMethodConfigs) {
    return [];
  }
  return authMethodConfigs?.filter(
    (config) =>
      config?.method in IV3Method &&
      config?.method !== IV3Method.WaveQuickLogin,
  );
};
const goTripartiteAuth = async (redirectUriString: string, ticket?: string) => {
  if (!redirectUriString) {
    return '';
  }
  const href = await getLocation();
  const currentUrl = new URL(href);
  const redirectUrl = currentUrl.toString();
  const redirectUri = new URL(redirectUriString);
  const innerRedirectUri = new URL(
    redirectUri?.searchParams?.get('redirect_uri'),
  );
  if (!innerRedirectUri) {
    return '';
  }
  innerRedirectUri.searchParams.append('redirect_url', redirectUrl);
  const innerRedirectUriString = innerRedirectUri.toString();
  redirectUri.searchParams.set('redirect_uri', innerRedirectUriString);
  if (ticket && typeof ticket === 'string') {
    redirectUri.searchParams.append('zt_ticket', ticket);
  }
  return redirectUri.toString();
};

const getIV3MfaConfig = async (env: Env) => {
  const data = await request<IV3MfaConfigRes>(
    `${authApiPrefix[env]}/${authnV3getMfaConfigApi}`,
    {},
  );
  if (data?.code === 0) {
    return data?.data;
  }
  return null;
};

const getLocation = async () => {
  const isIframe = getIframe();
  if (isIframe) {
    const { url } = await requestPostMessage({
      target: window.parent,
      eventName: SDK_ACTION.GET_LOCATION,
      data: {},
    });
    return url;
  }
  return window.location.href;
};

const postChangeLocation = (url: string) => {
  sendPostMessage({
    target: window.parent,
    eventName: SDK_ACTION.CHANGE_LOCATION,
    data: {
      url,
    },
  });
};

const postOpenLocation = (url: string) => {
  sendPostMessage({
    target: window.parent,
    eventName: SDK_ACTION.OPEN_LOCATION,
    data: {
      url,
    },
  });
};

const postReloadLocation = () => {
  sendPostMessage({
    target: window.parent,
    eventName: SDK_ACTION.RELOAD_LOCATION,
    data: {},
  });
};

const reloadLocation = () => {
  const isIframe = getIframe();
  if (isIframe) {
    postReloadLocation();
    return;
  }
  window.location.reload();
};

const postDestroy = () => {
  sendPostMessage({
    target: window.parent,
    eventName: SDK_ACTION.DESTROY,
    data: {},
  });
};

const goToRegister = async (env: Env, clientId: string, lang: Lang) => {
  const sourceUrl = await getLocation();
  const domain = registerDomainMap[env];
  const url = `${domain}?sourceRedirectUrl=${sourceUrl}&clientId=${clientId}&lang=${lang}`;
  const isIframe = getIframe();
  if (isIframe) {
    postChangeLocation(url);
    return;
  }
  window.location.href = url;
};

const goToPasswd = (env: Env, lang: Lang) => {
  const url = passwdDomainMap[env] || passwdDomainMap['prod'];
  const _url = url + `?lang=${lang}`;
  const isIframe = getIframe();
  if (isIframe) {
    postOpenLocation(_url);
    return;
  }
  window.open(_url, '_blank');
};

const goAccount = (env: Env, lang: Lang) => {
  const url = accountDomainMap[env] || accountDomainMap['prod'];
  const _url = url + `?lang=${lang}`;
  const isIframe = getIframe();
  if (isIframe) {
    postOpenLocation(_url);
    return;
  }
  window.open(_url, '_blank');
};

const getPathname = () => {
  return (window && window.location.pathname) || '';
};

const getTenant = () => {
  const tld = getTLD();
  return tld === 'hoyoverse-inc.com' ? 'Cognosphere' : 'miHoYo';
};

const getCrossTenant = () => {
  const tld = getTLD();
  return tld === 'hoyoverse-inc.com' ? 'miHoYo' : 'Cognosphere';
};

const getLanguageFromUA = (uaString: string): Lang => {
  const languageMatch = uaString.match(/Language\/([\w-]+)/);
  let language = languageMatch ? languageMatch[1] : null;
  if (language === 'zh-cn') {
    return 'zh-CN';
  } else if (language === 'en_us') {
    return 'en-US';
  } else if (!language) {
    return 'zh-CN';
  } else {
    return 'zh-CN';
  }
};

const getWaveJsTicketAndAppId = async (env: Env) => {
  const data = await request<WaveJsTicketRes>(
    `${personalCenterApiPrefix[env]}/${getWaveJsTicketApi}`,
    {},
  );
  if (data?.code === 0) {
    return data?.data;
  }
  return null;
};

const V3GetWaveJsTicketAndAppId = async (env: Env) => {
  const data = await request<IV3WaveJsTicketRes>(
    `${authApiPrefix[env]}/${getWaveJsTicketAndAppIdApi}`,
    {},
  );
  if (data?.code === 0) {
    return data?.data;
  }
  return null;
};

const openWithBrowserInWave = async (env: Env, url: string) => {
  try {
    const { appId, ticket } = await getWaveJsTicketAndAppId(env);
    await config({ appId, ticket, jsApiList: ['openWithBrowser'] });
    const result = await openWithBrowser({ url });
    return result.errCode === 0;
  } catch (e) {
    return false;
  }
};

const V3OpenWithBrowserInWave = async (env: Env, url: string) => {
  try {
    const { app_id: appId, ticket } = await V3GetWaveJsTicketAndAppId(env);
    await config({ appId, ticket, jsApiList: ['openWithBrowser'] });
    const result = await openWithBrowser({ url });
    return result.errCode === 0;
  } catch (e) {
    return false;
  }
};

const trimFormItemValues = (
  obj: Record<string, string>,
): Record<string, string> => {
  const newObj = {};
  Object.keys(obj).forEach((key) => {
    newObj[key] = obj[key]?.trim();
  });
  return newObj;
};

const logoutFn = (env: Env) => {
  const apis = [
    preAuthApiPrefix[env] + '/' + logOutApi,
    authApiPrefix[env] + '/' + authnV3LogOutApi,
  ];
  const requests = apis.map((api) => request(api, {}));
  return Promise.all(requests);
};

const getSamlRequest = async (url: string) => {
  const data = await requestGet(url);
  if (data?.code === 0) {
    return data?.data;
  }
  return {};
};

const getShim = (env: Env) => {
  if (env === 'prod') {
    return Cookies.get('sso_shim');
  }
  const key = `sso_shim_${env}`;
  return Cookies.get(key);
};

const getFlowToken = () => {
  let _flowToken = '';
  subscribe(({ flowToken }) => {
    _flowToken = flowToken;
  }, 'flowToken');
  return _flowToken;
};

const getOtpToken = () => {
  let _otpToken = '';
  subscribe(({ otpToken }) => {
    _otpToken = otpToken;
  }, 'otpToken');
  return _otpToken;
};

const buildUrl = (url: string, params: Record<string, string>) => {
  const query = queryString.stringify(params);
  return query ? `${url}${url.includes('?') ? '&' : '?'}${query}` : url;
};

const delFlowToken = () => {
  let url = new URL(window.location.href);
  if (url.searchParams.has('flow_token')) {
    url.searchParams.delete('flow_token');
    window.history.replaceState({}, document.title, url);
  }
};

const getFlowTokenFromSrv = async (env: Env) => {
  const api = authApiPrefix[env] + '/' + createFollowTokenApi;
  return await request<CreateFlowTokenRes>(api, {});
};

const createOtpLink = async (env: Env) => {
  const api = otpApiPrefix[env] + '/' + createBindOtpLinkApi;
  const data = await request<CreateBindOtpLinkRes>(api, {});
  if (data?.code === 0) {
    return data?.data?.link;
  }
  if (data?.code === REQUIRE_AUTH_CODE) {
    message.error(data?.message);
    reloadLocation();
  }
  return '';
};

const goOtpLink = (url: string) => {
  const isIframe = getIframe();
  if (isIframe) {
    postChangeLocation(url);
    return;
  }
  window.location.href = url;
};

const detectDevice = () => {
  const userAgent = navigator.userAgent;
  if (!userAgent) {
    return 'Android';
  }
  if (/Android/i.test(userAgent)) {
    return 'Android';
  } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
    return 'iOS';
  } else if (/Windows|Macintosh|Linux/i.test(userAgent)) {
    return 'PC';
  } else {
    return 'Android';
  }
};

const setCurrentMethodForProtocol = (method: IV3Method) => {
  dispatch(
    {
      method,
    },
    false,
    CURRENT_METHOD_FOR_PROTOCOL_KEY,
  );
};

const setWaveQuickForProtocol = (visible: boolean) => {
  dispatch(
    {
      visible,
    },
    false,
    QUICK_LOGIN_FOR_PROTOCOL_KEY,
  );
};

const refreshZtTokenInWave = async () => {
  const { errCode, errMsg } = await refreshZTTokenCookie();
  if (errCode !== 0) {
    // 不阻塞后续流程，不需要错误处理，打出日志即可
    console.error(
      'failed to refreshZtTokenInWave:' + `errCode=${errCode},errMsg=${errMsg}`,
    );
  }
  return;
};

const getZtTicketInWave = async () => {
  const { errCode, errMsg, zt_ticket } = await getZTTicket();
  if (errCode !== 0) {
    // 不阻塞后续流程，不需要错误处理，打出日志即可
    console.error(
      'failed to refreshZtTokenInWave:' + `errCode=${errCode},errMsg=${errMsg}`,
    );
    getSentryCapture().sentryCaptureException(new Error(errMsg), {
      level: 'error',
      tags: {
        api: 'getZTTicket',
        api_code: errCode,
      },
      extra: {
        data: {
          api: 'getZTTicket',
          message: errMsg,
        },
      },
    });
    return '';
  }
  return zt_ticket;
};

export {
  getZtTicketInWave,
  refreshZtTokenInWave,
  setCurrentMethodForProtocol,
  setWaveQuickForProtocol,
  V3OpenWithBrowserInWave,
  detectDevice,
  createOtpLink,
  goOtpLink,
  delFlowToken,
  getFlowTokenFromSrv,
  getShim,
  buildUrl,
  checkMFA,
  checkUserMfa,
  checkWxWorkUserInfo,
  checkZtLogin,
  forgetSubmit,
  formatCurMethods,
  formatProps,
  getAppKey,
  getCidFormId,
  getClient,
  getClientId,
  getCrossTenant,
  getEncryptedClientId,
  getFlowToken,
  getOtpToken,
  getEnv,
  getGeeLang,
  getGrey,
  getIdFormCid,
  getIframe,
  getIsWaveEnv,
  getIsWxWorkEnv,
  getIV3MfaConfig,
  getKey,
  getKeyToRegister,
  getKeyV3,
  getLang,
  getLanguageFromUA,
  getLocation,
  getMfaSecondCheck,
  getPageCode,
  getPathname,
  getPreCheck,
  getPreCheckToPersonalCenter,
  getPwd,
  getRemoteLocale,
  getRequestGlobalHeader,
  getSamlRequest,
  getSecondConfig,
  getSecondPwd,
  getSentryCapture,
  getSilentMethods,
  // splitMessageByUrl,
  getTenant,
  getTimeoutMessage,
  getVisitorId,
  getWaveJsTicketAndAppId,
  getWaveMsg,
  getWxLang,
  // getTLD,
  getXRequestId,
  goAccount,
  goSecondPwd,
  goToPasswd,
  goToRegister,
  goTripartiteAuth,
  handleRefresh,
  handleSubmit,
  isSafari,
  loginZtWithWaveClient,
  logoutFn,
  mfaCodeCheck,
  openWithBrowserInWave,
  parseUrl,
  postChangeLocation,
  quickLogin,
  reloadLocation,
  rsa,
  secondAuthSubmit,
  setRemoteLocale,
  setRequestGlobalHeader,
  setVisible,
  sortAreaCode,
  sortUserCenterAreaCode,
  trimFormItemValues,
  UAParser,
  userCenterSecondAuthSubmit,
  waveLogin,
  postDestroy,
};
