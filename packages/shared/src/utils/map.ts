import {
  authnV3EmailVerifyApi,
  authnV3SecondPasswdVerifyApi,
  authnV3SmsVerifyApi,
  authnV3TotpVerifyApi,
  authnV3VerifyAccountPedApi,
  mfaEmailVerify,
  mfaSmsVerify,
  otpAuth,
  personalMfaEmailVerify,
  personalMfaSmsVerify,
  secondPwdAuth,
  v2AuthPwdApi,
  v2EmailVerify,
  v2LdapAuthApi,
  v2PwdAccount,
  v2PwdEmailSend,
  v2PwdSmsVerify,
  v2SmsVerifyApi,
} from '@shared/routes';
import {
  ComponentType,
  IV3Method,
  Method,
  NETWORK_TYPE,
  ZONE,
} from '@shared/types';
import {
  accountMap,
  domainMap,
  getTLD,
  HOYOVERSE_DOMAIN,
  MIHOYO_DOMAIN,
  passwdMap,
  preDomainMap,
  registerMap,
  secondPasswdMap,
} from '@shared/utils/domain';

const tld = getTLD();
export const isMiHoYoDomain = tld === 'mihoyo.com';

const AUTH_API_ROUTE = '/iam/authn';

const PRE_AUTH_API_ROUTE = '/iam/authentication';
const PRE_OTP_API_ROUTE = '/iam/totp/public';
// const AUTH_API_ROUTE = '';

export const authApiPrefix = {
  dev: domainMap.get(tld).get('dev') + AUTH_API_ROUTE,
  test: domainMap.get(tld).get('test') + AUTH_API_ROUTE,
  uat: domainMap.get(tld).get('uat') + AUTH_API_ROUTE,
  prod: domainMap.get(tld).get('prod') + AUTH_API_ROUTE,
  pp: domainMap.get(tld).get('pp') + AUTH_API_ROUTE,
};

export const otpApiPrefix = {
  test: domainMap.get(tld).get('test') + PRE_OTP_API_ROUTE,
  uat: domainMap.get(tld).get('uat') + PRE_OTP_API_ROUTE,
  prod: domainMap.get(tld).get('prod') + PRE_OTP_API_ROUTE,
  pp: domainMap.get(tld).get('pp') + PRE_OTP_API_ROUTE,
};

export const preAuthApiPrefix = {
  dev: preDomainMap.get(tld).get('dev') + PRE_AUTH_API_ROUTE,
  test: preDomainMap.get(tld).get('test') + PRE_AUTH_API_ROUTE,
  uat: preDomainMap.get(tld).get('uat') + PRE_AUTH_API_ROUTE,
  prod: preDomainMap.get(tld).get('prod') + PRE_AUTH_API_ROUTE,
  pp: preDomainMap.get(tld).get('pp') + PRE_AUTH_API_ROUTE,
};

const PERSONAL_CENTER_API_ROUTE = '/iam/personal/center';

export const personalCenterApiPrefix = {
  dev: domainMap.get(tld).get('dev') + PERSONAL_CENTER_API_ROUTE,
  test: domainMap.get(tld).get('test') + PERSONAL_CENTER_API_ROUTE,
  uat: domainMap.get(tld).get('uat') + PERSONAL_CENTER_API_ROUTE,
  prod: domainMap.get(tld).get('prod') + PERSONAL_CENTER_API_ROUTE,
  pp: domainMap.get(tld).get('pp') + PERSONAL_CENTER_API_ROUTE,
};

export const wxCodeLoginUrlMap = {
  dev: `https://weixin-api.oa.mihoyo.com/mhinnerapp/iam/wecom_code/dev/`,
  test: `https://weixin-api.oa.mihoyo.com/mhinnerapp/iam/wecom_code/test/`,
  uat: `https://weixin-api.oa.mihoyo.com/mhinnerapp/iam/wecom_code/uat/`,
  prod: `https://api.openout.mihoyo.com/iam/authentication/out/v1/auth/wecom_code_login`,
  pp: `https://empapipp01.app.mihoyo.com/iam/authentication/out/v1/auth/wecom_code_login`,
};

export const componentTypeToIAmOAuth2UrlPathname = {
  [`${ComponentType.pc}`]: 'iam/login',
  [`${ComponentType.mb}`]: 'm/iam/login',
  [`${ComponentType.general}`]: 'general/iam/login',
};

export const iamToWxPathname = {
  '/iam/login': 'iam',
  '/m/iam/login': 'iam/m',
  '/general/iam/login': 'iam/m',
  '/m/general/iam/login': 'iam/m',
};

export const CUCKOO_API =
  'https://south-gate.mihoyo.com/cuckoo/api/v1/translation/resource';

export const logo = isMiHoYoDomain
  ? 'https://info-static.mihoyo.com/images/sso-logo.png'
  : 'https://static-public.emp.hoyoverse.com/images/sso-logo.png';

export const prefixCls = 'info-iam-login';

export const prefixMbCls = 'info-iam-login-mb';

export const prefixGeneralCls = 'info-iam-login-general';

export const langMap = [
  {
    label: '中文',
    value: 'zh-CN',
    wxValue: 'zh',
  },
  {
    label: 'English',
    value: 'en-US',
    wxValue: 'en',
  },
];

export const submitApiMap = [
  {
    method: Method['otp:sms'],
    api: v2SmsVerifyApi,
  },
  {
    method: Method['otp:email'],
    api: v2EmailVerify,
  },
  {
    method: Method.account,
    api: v2AuthPwdApi,
  },
  {
    method: Method.ldap,
    api: v2LdapAuthApi,
  },
  {
    method: Method['mfa:totp'],
    api: otpAuth,
  },
  {
    method: Method['mfa:2ndPwd'],
    api: secondPwdAuth,
  },
  {
    method: Method['mfa:sms'],
    api: mfaSmsVerify,
  },
  {
    method: Method['mfa:email'],
    api: mfaEmailVerify,
  },
  {
    method: Method['otp:email:forget'],
    api: v2PwdEmailSend,
  },
  {
    method: Method['otp:sms:forget'],
    api: v2PwdSmsVerify,
  },
  {
    method: Method['ldap:forget'],
    api: v2PwdAccount,
  },
  {
    method: Method['identity:email'],
    api: personalMfaEmailVerify,
  },
  {
    method: Method['identity:sms'],
    api: personalMfaSmsVerify,
  },
  {
    method: Method['ldap:account:forget'],
    api: v2PwdAccount,
  },
  {
    method: IV3Method.AccountPwd,
    api: authnV3VerifyAccountPedApi,
  },
  {
    method: IV3Method.Email,
    api: authnV3EmailVerifyApi,
  },
  {
    method: IV3Method.SMS,
    api: authnV3SmsVerifyApi,
  },
  {
    method: IV3Method.SecondPwd,
    api: authnV3SecondPasswdVerifyApi,
  },
  {
    method: IV3Method.TOTP,
    api: authnV3TotpVerifyApi,
  },
];

const MethodToApiMap = new Map();

submitApiMap?.forEach((e) => {
  MethodToApiMap.set(e.method, e.api);
});

export { MethodToApiMap };

export const COOKIE_KEY = 'sso_wx';

export const COOKIE_KEY_V2 = 'wx_iam';
export const TOKEN_KEY = 'Admin-Token';
export const GRAY_COOKIE_KEY = 'infosys-gray'; // 灰度标识
export const LANGUAGE_KEY = 'x-mi-accept-language';
export const TICKET_KEY = 'ticket';
export const MFA_COOKIE_KEY = 'sso_wx_2fa';
export const MFA_SECOND_CHECK_SCENE = 'second_check';

export const DEFAULT_OPTIONS = {
  zone: ZONE.TERRITORY,
  networkType: NETWORK_TYPE.INTRANET,
};

export const enum OSS_TYPE {
  INTERNAL = 0,
  ALI = 1,
  AWS = 2,
}

export const passwdDomainMap = {
  test: passwdMap.get(tld).get('test'),
  uat: passwdMap.get(tld).get('uat'),
  pp: passwdMap.get(tld).get('pp'),
  prod: passwdMap.get(tld).get('prod'),
};

export const accountDomainMap = {
  test: accountMap.get(tld).get('test'),
  uat: accountMap.get(tld).get('uat'),
  pp: accountMap.get(tld).get('pp'),
  prod: accountMap.get(tld).get('prod'),
};

export const secondPasswdDomainMap = {
  test: secondPasswdMap.get(tld).get('test'),
  uat: secondPasswdMap.get(tld).get('uat'),
  pp: secondPasswdMap.get(tld).get('pp'),
  prod: secondPasswdMap.get(tld).get('prod'),
};

export const registerDomainMap = {
  test: registerMap.get(tld).get('test'),
  uat: registerMap.get(tld).get('uat'),
  pp: registerMap.get(tld).get('pp'),
  prod: registerMap.get(tld).get('prod'),
};

export const OTP_PASSWD_PUBLIC_KEY = {
  test: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDW0vtrJ6vEQwOO2DXU+EFoQA+68if8Zu8LWXxxuOpQXQVyPJuPUHLDh171K7VPlv+MLrQb6cQTkpNg0rMu7ikqi7mEbXw+KJ8ON54uloW0EMNj7dLFdzu366By7RXrm2BgLVEr3ZVswKqGoAoTpwJ9aZQXbmDgwFp2rC6pauD64QIDAQAB',
  uat: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDW0vtrJ6vEQwOO2DXU+EFoQA+68if8Zu8LWXxxuOpQXQVyPJuPUHLDh171K7VPlv+MLrQb6cQTkpNg0rMu7ikqi7mEbXw+KJ8ON54uloW0EMNj7dLFdzu366By7RXrm2BgLVEr3ZVswKqGoAoTpwJ9aZQXbmDgwFp2rC6pauD64QIDAQAB',
  pp: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxLMX3zCtqoluY5OWAej+KYMNHPSopGaz0AGsvwgwvx5jPst0Pc7zfQGBX3nMaYzl/ZXLgWDAV8au9xNYzFbp1hv9vwDXVlDV6r55QlTEqIqiTXOIo/GJcmINGL5lw6lfGdD3wk752nNMYwRN00e2+6Xa416C2JTzBbXSlGRfexIAn44ucfZ07nroONTx+99+a60cLdTzxkwWFHt6e1P0wZBrp3GsJGj52UuDBa/mwu/sSsfOOimNV7YlV0XVOhrI5THAISRS+E3bcb1KThjLaoBl43qFG2md8l+JCLGvYJ64joaoSXeswr8Z7njuval50jIba2K9/MfJGsZe2PRHvwIDAQAB',
  prod: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxLMX3zCtqoluY5OWAej+KYMNHPSopGaz0AGsvwgwvx5jPst0Pc7zfQGBX3nMaYzl/ZXLgWDAV8au9xNYzFbp1hv9vwDXVlDV6r55QlTEqIqiTXOIo/GJcmINGL5lw6lfGdD3wk752nNMYwRN00e2+6Xa416C2JTzBbXSlGRfexIAn44ucfZ07nroONTx+99+a60cLdTzxkwWFHt6e1P0wZBrp3GsJGj52UuDBa/mwu/sSsfOOimNV7YlV0XVOhrI5THAISRS+E3bcb1KThjLaoBl43qFG2md8l+JCLGvYJ64joaoSXeswr8Z7njuval50jIba2K9/MfJGsZe2PRHvwIDAQAB',
};

export const loginEnhanceOptions = {
  propTypes: {
    client_id: String,
    sys_login_api: String,
    client_api: String,
    env: String,
    cb: Function,
    lang: String,
    token_key: String,
    show_lang: Boolean,
    general: Boolean,
    handle_lang: Function,
    mask: Boolean,
    ticket_key: String,
    response_type: String,
    login_page_config_code: String,
    hide_sys_header: Boolean,
    magic_ref: Function,
    hidden_on_cb: Boolean,
    wave_otp: String,
  },
};

export const PASSWD_WORK_CODE = 1201003001;
export const REG_WORK_CODE = 2023082106;

export const WAVE_SILENCE_WORK_CODE = 1050703;

export const LOGIN_PAGE_CONFIG_CODE = 1050200;

export const CLIENT_NOT_FOUND_CODE = 1050209;

export const moatClientId = {
  test:
    getTLD() === MIHOYO_DOMAIN || getTLD() === HOYOVERSE_DOMAIN
      ? '91214c81784fb9ec'
      : '094dcd0dbd5c42bd',
  uat:
    getTLD() === MIHOYO_DOMAIN || getTLD() === HOYOVERSE_DOMAIN
      ? '5560ba2d6d8d41e3'
      : '76d0d36b60740b83',
  pp:
    getTLD() === MIHOYO_DOMAIN || getTLD() === HOYOVERSE_DOMAIN
      ? 'e1100f688bfeedfa'
      : '6edc464e19ad849e',
  prod:
    getTLD() === MIHOYO_DOMAIN || getTLD() === HOYOVERSE_DOMAIN
      ? 'e1100f688bfeedfa'
      : '6edc464e19ad849e',
};

export const SDK_ACTION = {
  GET_LOCATION: 'GET_LOCATION',
  CHANGE_LOCATION: 'CHANGE_LOCATION',
  OPEN_LOCATION: 'OPEN_LOCATION',
  RELOAD_LOCATION: 'RELOAD_LOCATION',
  REQUEST_ERROR: 'REQUEST_ERROR',
  DESTROY: 'IAM_DESTROY',
};

// 不需要错误上报的错误码 List
export const V3_IGNORE_CODE = [
  1050202, 1050211, 1050214, 1050215, 1050221, 1050222, 1050223, 1050224,
  1050225, 1050226, 1050401, 1050500, 1050601, 1050801, -3, -5, 1051323,
  **********,
];

export const CROSS_TENANT_PASSWD_WORK_CODE_LIST = [
  **********, **********, **********, **********,
];

// 账号锁定的错误码
export const ACCOUNT_LOCK_CODE = 1050221;

export const BC_NAME = 'IAM_LOGIN_CHANNEL';
export const BC_MESSAGE = 'IAM_LOGIN_SUCCESS';

export const ANTD_PREFIX = 'otakus';

export const MESSAGE_ACTION = 'MESSAGE_ACTION';

export const MESSAGE_DESTROY_ACTION = 'MESSAGE_DESTROY_ACTION';

export const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const CURRENT_METHOD_FOR_PROTOCOL_KEY = 'current-method-for-protocol';

export const QUICK_LOGIN_FOR_PROTOCOL_KEY = 'quick-login-for-protocol';

export const INVALID_TOTP_VERIFY_CODE = 1130004;
export const INVALID_TOTP_VERIFY_CODE_V2 = 1050601;
export const REQUIRE_AUTH_CODE = 1130005;
export const INVALID_TOTP_TOKEN_CODE = 1130006;
export const INVALID_FLOW_TOKEN_CODE = 1130007;
export const INVALID_FLOW_TOKEN_STATE_CODE = 1051301;
export const EXPIRED_SECRET_CODE = 1130008;
export const NOT_EXIT_FLOW_TOKEN = 1051300;
export const MODIFY_PWD_REQUIRE_AUTH_CODE = 1051320;
export const MODIFY_PWD_REQUIRE_TOTP_CODE = 1051321;
export const MISS_FLOW_TOKEN_CODE = 1051305;
export const EXCHANGE_OTP_NEED_IDP = 1130019;
export const EXCHANGE_OTP_ACCOUNT_STOP = 1130020;
export const EXCHANGE_OTP_ACCOUNT_CHANGE = 1130021;
export const EXCHANGE_OTP_FLOW_TOKEN_TIMEOUT = 1130022;
