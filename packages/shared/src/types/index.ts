import zh from '@shared/locales/zh';
import type { CaptureContext } from '@sentry/types';

export type localeKey = typeof zh;

export type Env = 'test' | 'uat' | 'prod' | 'dev' | 'prodGray' | 'i18n' | 'pp';

export type Lang = 'zh-CN' | 'en-US';

export type ResponseType = 'token' | 'ticket';

export enum Method {
  'otp:email' = 'otp:email', // 邮箱验证码登录
  'otp:sms' = 'otp:sms', // 手机验证码登录
  'account' = 'account', // 账密登录
  'ldap' = 'ldap', // 域账号登录
  'wecom:qrcode' = 'wecom:qrcode', // 企微扫码登录
  'mfa:totp' = 'mfa:totp', // opt 认证
  'mfa:2ndPwd' = 'mfa:2ndPwd', // 二级密码认证
  'mfa:sms' = 'mfa:sms', // 二级 短信认证
  'mfa:email' = 'mfa:email', // 二级 邮箱认证
  'mfa:qrcode' = 'mfa:qrcode', // 二级 扫码认证
  'otp:email:forget' = 'otp:email:forget', // 忘记密码 邮箱认证
  'otp:sms:forget' = 'otp:sms:forget', // 忘记密码 短信认证
  'ldap:forget' = 'ldap:forget', // 忘记密码 域账号认证
  'identity:email' = 'identity:email', // 个人中心邮箱二级认证
  'identity:sms' = 'identity:sms', // 个人中心手机二级认证
  'ldap:account:forget' = 'ldap:account:forget', // 账密验证
  'wave:qrcode' = 'wave:qrcode', // wave扫码登录
  'mfa:waveQrcode' = 'mfa:waveQrcode', // wave扫码二级认证
}

export enum Network {
  'intranet' = 'intranet', // 内网
  'extranet' = 'extranet', // 外网
}

export enum SecondMethods {
  'mfa:totp' = 'mfa:totp', // opt 认证
  'mfa:2ndPwd' = 'mfa:2ndPwd', // 二级密码认证
}

export enum ComponentType {
  'pc' = 'pc',
  'mb' = 'mb',
  'general' = 'general',
}

export enum AccountType {
  'EMP_ACCOUNT' = 'EMP_ACCOUNT',
  'PERSONAL_ACCOUNT' = 'PERSONAL_ACCOUNT',
}

export enum UserEmailType {
  'PERSONAL_EMAIL' = 'PERSONAL_EMAIL',
  'ENTERPRISE_EMAIL' = 'ENTERPRISE_EMAIL',
}

export enum UserPhoneNumberTag {
  'PRIMARY_PHONE' = 'PRIMARY_PHONE',
  'BACKUP_PHONE' = 'BACKUP_PHONE',
}

export interface MessageSlot {
  WAVE_CROSS_TENANT: string;
}

export interface LoginProps extends ISentryCapture {
  env: Env;
  lang?: Lang;
  clientId: string;
  cb?: () => void; // 业务认证完成回调
  handleLang?: (lang: Lang) => void;
  general?: boolean; // 是否为弹窗模式 默认false
  showLang?: boolean; // 是否展示语言切换
  mask?: boolean;
  workCode?: number | undefined; // 业务码
  messageSlot?: MessageSlot;
  waveOtp?: string;
}

export interface OtpProps extends ISentryCapture {
  env: Env;
  lang?: Lang;
  redirectUri?: string;
  otpToken?: string;
  followToken?: string;
}

export interface ISentryCapture {
  sentryCaptureMessage?: (message: string, ctx: CaptureContext) => void;
  sentryCaptureException?: (err: any, ctx: CaptureContext) => void;
}

export interface PasswdProps extends ISentryCapture {
  clientId?: string;
  lang?: Lang;
  handleLang?: (lang: Lang) => void;
  env: Env;
  showLang?: boolean;
  pwdScene?: string; // 场景值，区分修改账密以及二级密码
}

export interface UserCenterProps {
  clientId?: string;
  env: Env;
  lang?: Lang;
  sessionExpiredCallback?: () => void; // 会话过期回调
  mfaExpiredCallback?: () => void; // 二次认证过期回调
}

export type PreCheckParams = {
  bizId?: string;
  sig?: string;
  geePublicId?: string;
  isGee?: 1 | 0; // 1 使用 gee 0 不使用
};

export interface Response<T = any> {
  code: number;
  message: string;
  data: T;
  error: boolean;
  success: boolean;
}

export interface AuthMethodConfigs {
  method: Method;
  captcha?: 1 | 0; // 是否需要人机交互
  agentId?: string; // 企业ID
  appId?: string; // 应用ID
  redirectUri?: string;
}

export interface Cid {
  cid: string; // 国家码
  cname: string; // name
}

export interface Multilingual {
  acceptLanguage: Lang;
  language: string;
}

export interface LoginConfig {
  bottomPic: string; // 底图链接
  topPic: string; // 顶图连接
  authMethodConfigs: AuthMethodConfigs[]; // 认证方式
  appIcon: string;
  appName: string;
  network: Network;
  welcome: string; // 欢迎语
  multilingual: Multilingual[]; // 语言枚举
  loginPageProtocol?: LoginPageProtocol; // 隐私协议
  sessionExpiration: string; // 会话有效期语文案
  prompt: string; //提示语多语言
  loginPageConfigCode: string; // 登录页配置码
  useForgetPwdMiId: 0 | 1; // 是否使用忘记miId密码,1-使用;0-不使用
  useForgetPwdDomain: 0 | 1; // 是否使用忘记域账号密码,1-使用;0-不使用
  useRegister?: 1 | 0; // 是否使用注册,1-使用;0-不使用
  thirdPartyAuthMethodConfigs?: ThirdPartyAuthMethodConfigs[]; // 第三方登录配置
  waveQuickLoginConfig?: boolean; // 是否使用wave快捷登录
}

export interface ThirdPartyAuthMethodConfigs {
  method: string;
  captcha: 0;
  thirdPartyAppIcon?: string; // 第三方应用图标
  thirdPartyAppName?: string; // 第三方应用名称
  redirectUri: string; // 第三方登录回调地址
  tenant: string; // 租户
}

export interface RegisterProps {
  env: Env;
  sourceRedirectUrl?: string; // 注册成功后跳转的url
  clientId?: string;
  lang?: Lang;
  handleLang?: (lang: Lang) => void;
  showLang?: boolean;
}

export interface UserBaseInfo {
  accountType: AccountType; // 账号类型
  englishName?: string; // 英文名
  name?: string; // 姓名
  nickname?: string; // 昵称
  profilePhoto?: string; // 头像
  emailOutList?: UserEmailInfo[]; // 邮箱列表
  phoneNumberList?: UserPhoneInfo[]; // 手机号列表
  domain?: string; // 域账号
  tapdEmail?: string; // tapd账号
}

export interface UserEmailInfo {
  email: string; // 邮箱
  emailType: UserEmailType; // 邮箱类型
  id: string; // 邮箱id
}

export interface UserPhoneInfo {
  areaCode: string; // 国家码
  id: string; // 手机号id
  phoneNumber: string; // 手机号
  phoneNumberTag: UserPhoneNumberTag;
}

// 获取 clientId
export type GetSysClientId = Response<{
  clientId: string;
  isLogin: 0 | 1;
}>;

// 获取 code
export type GetCode = Response<{
  loginPageConfigCode: string;
}>;

// 获取 ticket
export type GetTicket = Response<{
  ticket: string;
}>;

// 业务系统登录
export type SysRes = Response<{
  token: string;
}>;

// 登录页配置

export type GetLoginConfig = Response<LoginConfig>;

export type LoginPageProtocol = {
  agreementPrefix: string; //协议前置文案
  agreementList: Protocol[];
};

export type Protocol = {
  id: string;
  protocolName: string; // 协议名称
  protocolContent: string; // 协议名称
};

export type GetAuthKey = Response<{
  publicKey: string; // 公钥
  hash: string; //  hash盐
}>;

export type GetCountryCode = Response<{
  cidList: Cid[];
}>;

export type PhoneAreaCode = {
  cnName: string;
  code: string;
  enName: string;
  id: string;
  value: string;
  sorted: number;
  sortedGroup: number;
};

export type PhoneAreaCodeRes = Response<PhoneAreaCode[]>;

export type GetPreCheck = Response<{
  code: 200 | 400 | 800; // 200 无风险直接通过 400 换取二次验证 800 直接拦截
  bizId: string;
  sig: string;
  geePublicId?: string;
  isGee?: 1 | 0; // 1 使用 gee 0 不使用
}>;

// 二级认证
export type SecondAuth = {
  method: IV3Method;
  secondFAEchoOut?: {
    mfaEmails?: {
      email: string;
      id: string;
    }[];
    mfaPhones?: {
      cid: string;
      phone: string;
      id: string;
    }[];
    weComAuthConfig?: {
      // 构建企微二维码所需信息
      appId: string;
      agentId: string;
      redirectUri: string;
    };
    waveAuthConfig?: {
      appId: string;
    };
    identityEmails?: {
      email: string;
      id: string;
    };
    identityPhones?: {
      cid: string;
      phone: string;
      id: string;
    }[];
    exist2ndPwd: boolean;
  };
  identityEchoOut?: {
    identityEmails?: {
      email: string;
      id: string;
    };
    identityPhones?: {
      cid: string;
      phone: string;
      id: string;
    }[];
  };
};

export type AuthRes = Response<{ '2FAConfigs'?: SecondAuth[] }>;

export type PwdVerify = Response<{
  contextToken?: string; //上下文token. 5分钟内有效,修改密码之后失效
  '2FAConfigs'?: SecondAuth[];
}>;

export type GetPattern = Response<{
  resultList: {
    description: string;
    pass: boolean;
  }[];
}>;

export type Cuckoo = {
  env: Env;
  id: string;
  link: string;
  lang: Lang;
};

export type CuckooRes = Response<Cuckoo[]>;

export type GetSmsVerifyLength = Response<{
  length: number;
}>;

export type AuthPrivacy = {
  id: string;
  name: string;
  content: string;
};

export interface SecondProps extends SecondComponentProps {
  visible: boolean;
  method: SecondAuth[];
  /** 弹窗禁止关闭 */
  disableClose?: boolean;
  closable?: boolean;
  keyboard?: boolean;
  scene?: string; // 场景值，对于单独使用二次认证的情形，需要传入场景值与认证场景区分
  userCode?: string; // 用户标识编码
  extraProps?: {
    [key: string]: any;
  };
}

export type OTPCode = {
  account: string;
  appName: string;
  dCode: string;
  overTime: string;
  priority: number; // 1 个人 2 公共
};

export type LdapStatus = {
  domain: string;
  isLoginUser: boolean;
  passwordExpireTime: number; // 单位毫秒
  status: number; // 0 未锁定 1 锁定
};

export interface SecondComponentProps {
  handleVisible: (visible: boolean) => void;
  cb: (mfaCode?: string) => void;
}

export type AuthPrivacyRes = Response<AuthPrivacy>;

/** 获取当前应用是否需要二次认证 */
export type GetSecondCheck = Response<{
  '2FAConfigs': IV3MfaConfig[];
}>;

export enum ZONE {
  ALL = 'all',
  TERRITORY = 'territory',
  OVERSEAS = 'oversea',
}

export enum NETWORK_TYPE {
  INTRANET = 'intranet',
  WAN = 'wan',
}

export interface AccountLang {
  label: string;
  language: Lang;
}

export type checkMFARes = Response<{
  checked: boolean;
}>;

export type MFARes = Response<{
  mfaCode?: string;
  contextToken?: string;
}>;

export type ForgetMethodRes = Response<{
  authMethodConfigs: {
    method: Method;
    captcha?: 1 | 0; // 是否需要人机交互
  }[];
}>;

export type GetRegistryChainRes = Response<{
  registerSessionId: string;
}>;

export type UserBaseInfoRes = Response<UserBaseInfo>;

export type GetPersonalMfaVerifyRes = {
  code: number;
  message: string;
  data: boolean;
  error: boolean;
  success: boolean;
};

export type GetPersonalMfaVerifyMethodRes = Response<{
  identityConfigs: SecondAuth[];
}>;

export type OTPCodeListRes = Response<OTPCode[]>;

export type LdapStatusRes = Response<LdapStatus>;

export type LangRes = Response<AccountLang>;

export type LangMapRes = Response<{ languages: AccountLang[] }>;

export interface WaveClientResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

export type OtpAndTickets = {
  otp: string;
  ticket: string;
};

export type OtpAndTicketRes = WaveClientResponse<OtpAndTickets>;

export type WaveUserInfo = {
  avatar: string;
  name_cn: string;
  name_en: string;
  nickname: string;
};

export type WaveUserInfoRes = WaveClientResponse<{
  userinfo: WaveUserInfo;
}>;

export type WaveZtTicketRes = WaveClientResponse<{
  ticket: string;
}>;

export type SilentMethods = {
  wave: boolean;
  wecom: boolean;
};

export type SilentMethodsRes = Response<SilentMethods>;

export type GetRegisterPublicKeyRes = Response<string>;

export type MfaCheck = {
  checked: boolean;
  userCode: string;
};

export type MfaCheckRes = Response<MfaCheck>;

export type MfaCodeCheck = {
  checked: boolean;
};

export type MfaCodeCheckRes = Response<MfaCodeCheck>;

export type WaveJsTicketRes = Response<{
  ticket: string;
  appId: string;
}>;

export type IV3WaveJsTicketRes = Response<{
  ticket: string;
  app_id: string;
}>;

export enum ServerDomain {
  'mihoyo.com' = 'mihoyo.com',
  'hoyoverse.com' = 'hoyoverse.com',
  'hoyoverse-inc.com' = 'hoyoverse-inc.com',
}

export enum AssetsService {
  'mihoyo.com' = 'https://info-static.mihoyo.com/images/iam/assets/',
  'hoyoverse.com' = 'https://static-public.emp.hoyoverse.com/images/iam/assets/',
  'hoyoverse-inc.com' = 'https://static-public.emp.hoyoverse.com/images/iam/assets/',
}

export enum AssetsDeviceType {
  'pc' = 'pc',
  'mb' = 'mb',
  'ui' = 'ui',
}

export interface LoginLayerProps
  extends Omit<LoginProps, 'env' | 'cb' | 'handleLang'> {
  protocol?: string; // 协议类型
  redirect_uri?: string; // 重定向地址
  redirectUri?: string; // 重定向地址
  callbackType?: string; // 回调类型
  callback_type?: string; // 回调类型
}

// v3

export interface IV3Agreement {
  id: string;
  protocol_name: string;
  protocol_content: string;
}

export interface IV3LoginPageProtocol {
  agreement_prefix: string; // 协议前置文案
  agreement_list: IV3Agreement[];
}

export enum DeviceType {
  Android = 'Android',
  iOS = 'iOS',
  PC = 'PC',
  Unknown = 'Unknown',
}

export enum IV3Method {
  AccountPwd = 'AccountPwd', // 账密
  SMS = 'SMS', // 短信
  WaveQR = 'WaveQR', // Wave扫码
  Email = 'Email', // 邮箱
  WaveQuickLogin = 'WaveQuickLogin', // Wave快捷登录
  TOTP = 'TOTP', // TOTP
  SecondPwd = 'SecondPwd',
  'identity:email' = 'identity:email', // 个人中心邮箱二级认证
  'identity:sms' = 'identity:sms', // 个人中心手机二级认证
  'iOS' = 'iOS',
  'Android' = 'Android',
}

export interface IV3AuthMethodConfigs {
  method: IV3Method;
  require_captcha: boolean; // 是否需要人机交互
  config?: {
    app_id?: string;
  };
}

export enum IV3NETWORK_TYPE {
  Intranet = 'Intranet',
  Extranet = 'Extranet',
}

export interface IV3Multilingual {
  accept_language: Lang;
  language: string;
}

export interface IV3LoginPageConfig {
  app_icon: string;
  app_name: string;
  bottom_pic: string;
  top_pic: string;
  enable_forgot_pwd: boolean; // 配置忘记密码
  enable_user_register: boolean; // 配置注册
  network: IV3NETWORK_TYPE; // 网络类型
  login_page_protocol: IV3LoginPageProtocol;
  auth_method_configs: IV3AuthMethodConfigs[];
  prefer_login_method?: 'WaveQuickLogin';
  multilingual: IV3Multilingual[];
  session_expiration: string;
  welcome: string;
  prompt?: string;
  third_party_auth_method_configs?: IV3ThirdPartyAuthMethodConfigs[];
  operation_banner_pic?: string; // 运营banner图
  check_agreement_privacy?: boolean; // 是否勾选隐私协议
}

export type IV3ThirdPartyAuthMethodConfigs = {
  app_icon?: string; // 第三方应用图标
  app_name?: string; // 第三方应用名称
  auth_uri: string; // 第三方登录回调地址
};

export type IV3ForgetMethodRes = Response<{
  authMethodConfigs: {
    method: IV3Method;
    require_captcha: boolean; // 是否需要人机交互
  }[];
}>;

export interface IV3PhoneAreaCode {
  cn_name: string;
  code: string;
  en_name: string;
  value: string;
  sorted: number;
  sorted_group: number;
}

export interface IV3CaptchaTicket {
  data: {
    captcha_id: string;
  };
  ticket: string;
  type: string;
}

export interface GeetestResponse {
  lot_number: string;
  captcha_output: string;
  pass_token: string;
  gen_time: string;
  captcha_id: string;
  sign_token: string;
}

export interface IV3PerCheckParams extends Omit<IV3CaptchaTicket, 'data'> {
  data: GeetestResponse;
}

export enum IV3_SCENE_TYPE {
  InitialLogin = 'InitialLogin',
  InitialRestPasswd = 'ResetPwd',
  // InitialRestPasswd = 'InitialLogin',
  SecondCheck = 'second_check',
  ExchangeOtp = 'ExchangeOtp',
}

export type IV3CaptchaTicketRes = Response<IV3CaptchaTicket>;

export type IV3PhoneAreaCodeRes = Response<IV3PhoneAreaCode[]>;

export type IV3LoginConfigOut = Response<IV3LoginPageConfig>;

export type IV3GetAuthKeyRes = Response<{
  public_key: string; // 公钥
  hash: string; //  hash盐
}>;

export interface IV3MfaConfig {
  method: IV3Method;
  config?: {
    exist_2nd_pwd?: boolean;
    need_bind_otp?: boolean;
  };
  secondFAEchoOut?: {
    mfaEmails?: {
      email: string;
      id: string;
    }[];
    mfaPhones?: {
      cid: string;
      phone: string;
      id: string;
    }[];
  };
}

export type IV3AuthRes = Response<{
  methods?: IV3MfaConfig[];
  ticket: string;
  bind_otp?: boolean;
}>;
export type IV3MfaConfigRes = IV3AuthRes;

export interface LogOutProps {
  postLogoutRedirectUri?: string;
  env: Env;
  lang?: Lang;
}

export enum ViewportSize {
  xs = 'xs',
  sm = 'sm',
  md = 'md',
  lg = 'lg',
}

export enum MESSAGE_TYPES {
  SUCCESS = 'success',
  ERROR = 'error',
  INFO = 'info',
  WARNING = 'warning',
  LOADING = 'loading',
  DESTROY = 'destroy',
}

export enum OTP_STEP {
  IDP = 'idp',
  BIND = 'bind',
  CHALLENGE = 'challenge',
  SUCCESS = 'success',
}

export enum FollowAuthMethods {
  SMS = 'sms',
  PWD = 'pwd',
}

export interface CreateFlowToken {
  flow_token: string;
}

export type CreateFlowTokenRes = Response<CreateFlowToken>;

export interface GetFollowAuthMethods {
  candidate_methods: {
    id: number;
    method: FollowAuthMethods;
    // sensitive_message: string; // 废弃
    name: string;
    phone_number: string;
    cid: string;
    email: string;
  }[];
}

export type GetOtpAuthMethodsRes = Response<GetFollowAuthMethods>;

export interface FollowAuthSendCode {
  method_id: number;
}

export type FollowAuthSendCodeRes = Response<FollowAuthSendCode>;

export interface OtpSecret {
  account: string;
  expires_in: number;
  qrcode: string;
  secret: string;
}

export type OtpSecretRes = Response<OtpSecret>;

export enum BindMethod {
  'SCAN' = 'scan',
  'SECRET' = 'secret',
}

export enum OtpErrorType {
  INVALID_TOTP_TOKEN_CODE = 'INVALID_TOTP_TOKEN_CODE',
  EXCHANGE_OTP_ACCOUNT_STOP = 'EXCHANGE_OTP_ACCOUNT_STOP',
  EXCHANGE_OTP_ACCOUNT_CHANGE = 'EXCHANGE_OTP_ACCOUNT_CHANGE',
  EXCHANGE_OTP_FLOW_TOKEN_TIMEOUT = 'EXCHANGE_OTP_FLOW_TOKEN_TIMEOUT',
}

export type IV3GetPattern = {
  description: string;
  pass: boolean;
};

export type IV3GetPatternRes = Response<{
  rules: IV3GetPattern[];
}>;

export type CreateBindOtpLinkRes = Response<{
  link: string;
  expires_in: number;
}>;

export type ExchangePreCheckRes = Response<{
  is_allow_emp_code: boolean;
}>;

export type OtpExchangeApprovalLinkRes = Response<{
  link: string;
}>;
