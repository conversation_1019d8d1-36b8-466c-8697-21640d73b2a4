import { useRequest } from 'ahooks';
import { CreateFlowTokenRes } from '@shared/types';
import { useConfig } from '@shared/hooks/useConfig';
import { getFlowTokenFromSrv } from '@shared/utils/utils';
import { dispatch } from '@shared/utils/store';

const cancelRequest = (cancel: boolean) => {
  dispatch(
    {
      cancel,
    },
    false,
    'flow-token-cancel',
  );
};

const useFlowToken = () => {
  // 创建 flowToken
  const { env, setConfig } = useConfig();
  const { runAsync: getFollowToken, loading: getFollowTokenLoading } =
    useRequest<CreateFlowTokenRes, any>(
      async () => await getFlowTokenFromSrv(env),
      {
        manual: true,
        onSuccess: (data) => {
          if (data?.code === 0) {
            const { flow_token } = data?.data;
            // setConfig({ followToken: flow_token });
            const url = new URL(window.location.href);
            const params = new URLSearchParams(url.search);
            params.set('flow_token', flow_token);
            url.search = params.toString();
            setConfig({
              flowToken: flow_token,
              otpToken: params.get('otp_token'),
            });
            // window.location.href = url.toString();
            // 更改当前页面 url 的 flow_token 参数
            window.history.replaceState(
              {},
              '',
              `${window.location.pathname}${url.search}`,
            );
          }
          return data;
        },
      },
    );

  const _getFollowToken = async () => {
    cancelRequest(true);
    await getFollowToken();
  };
  return {
    getFollowToken: _getFollowToken,
    getFollowTokenLoading,
  };
};

export default useFlowToken;
