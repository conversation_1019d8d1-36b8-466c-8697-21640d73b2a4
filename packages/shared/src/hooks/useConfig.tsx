import {
  createContext,
  FC,
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import {
  IV3_SCENE_TYPE,
  LoginProps,
  MessageSlot,
  SecondAuth,
} from '@shared/types';
import {
  authApiPrefix,
  otpApiPrefix,
  personalCenterApiPrefix,
  preAuthApiPrefix,
} from '@shared/utils/map';
import { dispatch } from '@shared/utils/store';
import { useSetState } from 'ahooks';

export interface ConfigProviderProps {
  env?: LoginProps['env'];
  clientId?: LoginProps['clientId'];
  handleLang?: LoginProps['handleLang'];
  children?: React.ReactNode;
  lang?: LoginProps['lang'];
  showLang?: boolean;
  general?: boolean;
  code?: string;
  errorMessage?: string;
  /** 应用二级认证参数，当有数据时为二级认证阶段 */
  appSecondAuthMethods?: SecondAuth[];
  sourceRedirectUrl?: string; // 注册流程时，来源应用的redirectUrl
  pwdScene?: string; // 场景值，区分修改账密以及二级密码
  workCode?: number | undefined; // 业务码
  messageSlot?: MessageSlot;
  setConfig?: (config: ConfigProviderProps) => void;
  mask?: boolean;
  otpToken?: string;
  flowToken?: string;
  otpSourceRedirectUrl?: string; // otp流程时，来源应用的redirectUrl
  otpSourceType?: OtpSourceType; // otp 流程来源枚举
  messageCenter?: boolean;
  scene?: IV3_SCENE_TYPE; // 区分场景
  resetOtp?: boolean; // 重置otp
}

export enum OtpSourceType {
  PWD = 'pwd',
  INVITATION = 'invitation',
  ACCOUNT = 'account',
}

export interface ConfigContextProps extends ConfigProviderProps {
  authApi?: string;
  clientId?: LoginProps['clientId'];
  personalCenterApi?: string;
  preAuthApi?: string;
  otpApi?: string;
  handlePrivacyChecked?: (checked: boolean) => void;
  privacyChecked?: boolean;
  showPrivacyConfirm?: boolean;
}

const DEFAULT_CONFIG: ConfigContextProps = {
  lang: 'zh-CN',
  authApi: '',
  personalCenterApi: '',
  preAuthApi: '',
  otpApi: '',
  privacyChecked: false,
};

const isEqual = (a: ConfigProviderProps, b: ConfigProviderProps) => {
  if (a === b) return true;
  if (!a || !b) return false;

  const keysA = Object.keys(a);
  const keysB = Object.keys(b);

  if (keysA.length !== keysB.length) return false;

  return keysA.every((key) => {
    const valueA = a[key as keyof ConfigProviderProps];
    const valueB = b[key as keyof ConfigProviderProps];

    if (typeof valueA === 'object' && valueA !== null) {
      return isEqual(
        valueA as ConfigProviderProps,
        valueB as ConfigProviderProps,
      );
    }

    return valueA === valueB;
  });
};

const ConfigContext = createContext<ConfigContextProps>(DEFAULT_CONFIG);

const useApiPrefixes = (env?: LoginProps['env']) => {
  return useMemo(
    () => ({
      authApi: authApiPrefix[env],
      otpApi: otpApiPrefix[env],
      preAuthApi: preAuthApiPrefix[env],
      personalCenterApi: personalCenterApiPrefix[env],
    }),
    [env],
  );
};

const useDispatchConfig = (config: ConfigProviderProps) => {
  useEffect(() => {
    const { lang, clientId, flowToken, otpToken } = config;

    if (lang) {
      dispatch({ lang }, false, 'changeLang');
    }
    if (clientId) {
      dispatch({ clientId }, false, 'clientId');
    }
    if (flowToken) {
      dispatch({ flowToken }, false, 'flowToken');
    }
    if (otpToken) {
      dispatch({ otpToken }, false, 'otpToken');
    }
  }, [config.lang, config.clientId, config.flowToken, config.otpToken]);
};

const ConfigProvider: FC<ConfigProviderProps> = ({ children, ...rest }) => {
  const prevPropsRef = useRef<ConfigProviderProps | null>(null);
  const [config, setConfig] = useSetState<ConfigProviderProps>({ ...rest });

  useEffect(() => {
    const prevProps = prevPropsRef.current;
    if (prevProps && !isEqual(prevProps, rest)) {
      setConfig(rest);
    }
    prevPropsRef.current = rest;
  }, [rest, setConfig]);

  const { env, lang = 'zh-CN' } = config;
  const apiPrefixes = useApiPrefixes(env);
  useDispatchConfig(config);

  const handleSetConfig = useCallback(
    (newConfig: ConfigContextProps) => {
      setConfig(newConfig);
    },
    [setConfig],
  );

  const handlePrivacyChecked = (checked: boolean) => {
    handleSetConfig({
      privacyChecked: checked,
    });
  };

  const contextValue = useMemo(
    () => ({
      ...config,
      lang,
      ...apiPrefixes,
      setConfig: handleSetConfig,
      handlePrivacyChecked,
    }),
    [config, lang, apiPrefixes, handleSetConfig],
  );

  return (
    <ConfigContext.Provider value={contextValue}>
      {children}
    </ConfigContext.Provider>
  );
};

export default memo(ConfigProvider);

const useConfigContext = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfigContext must be used within a ConfigProvider');
  }
  return context;
};

export const useConfig = () => {
  return useConfigContext();
};
