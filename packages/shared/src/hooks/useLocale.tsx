import { Lang, localeKey } from '@shared/types';
import React, { createContext, FC, useContext, useMemo, useState } from 'react';
import { default as baseLocales } from '@shared/locales';
import { useConfig } from '@shared/hooks/useConfig';
import { useAsyncEffect } from 'ahooks';
import i18nCache from '@shared/utils/cacheI18n';
import { subscribe } from '@shared/utils/store';

export interface LocaleProviderProps {
  children?: React.ReactNode;
}

export interface LocaleContextProps {
  locales?: { [key in Lang]: localeKey };
}

const useLocale = () => {
  const { lang = 'zh-CN' } = useConfig();
  const { locales } = useLocaleContext();
  const locale = useMemo(() => {
    return {
      ...baseLocales![lang],
      ...((locales && locales[lang]) ?? {}),
    };
  }, [lang, locales]);
  return (key: keyof localeKey) => {
    return locale[key] || '';
  };
};

const LocaleContext = createContext<LocaleContextProps>(
  {} as unknown as LocaleContextProps,
);

const LocaleProvider: FC<LocaleProviderProps> = ({ children }) => {
  const { lang, env } = useConfig();
  const [locales, setLocales] = useState<{ [key in Lang]: localeKey }>(null);
  useAsyncEffect(async () => {
    new i18nCache(lang, env);
    subscribe((val: { locales: { [key in Lang]: localeKey } }) => {
      setLocales(val.locales);
    }, 'iam-i18n');
  }, [lang, env]);
  return (
    <LocaleContext.Provider value={{ locales }}>
      {children}
    </LocaleContext.Provider>
  );
};

const useLocaleContext = () => {
  return useContext(LocaleContext);
};

export default useLocale;

export { LocaleProvider };
