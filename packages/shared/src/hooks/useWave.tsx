import { useConfig } from '@shared/hooks/useConfig';
import { useAsyncEffect, useSetState } from 'ahooks';
import type { OtpAndTicketRes, WaveUserInfo } from '@shared/types';
import WaveClient from '@shared/utils/wave';

interface WaveInfo {
  port?: number;
  getTicketAndOpenOTPResponse?: () => Promise<OtpAndTicketRes>;
  wave?: boolean;
  userInfo?: WaveUserInfo;
  getZtTicket?: () => Promise<string>;
}

const useWave = (thirdPartyAuthMethodConfigs: boolean) => {
  const { env, clientId } = useConfig();
  const [waveInfo, setWaveInfo] = useSetState<WaveInfo>({
    port: 0,
    wave: false,
  });

  useAsyncEffect(async () => {
    if (thirdPartyAuthMethodConfigs) {
      const instance = new WaveClient(env, clientId);
      const port = await instance.getPort();
      // 代表 与 wave 通讯成功
      if (port > 0) {
        const userInfo = await instance.getUserInfo();
        setWaveInfo({
          getZtTicket: instance.getZtTicket,
          port,
        });
        if (userInfo?.userinfo) {
          setWaveInfo({
            userInfo: userInfo?.userinfo,
            getTicketAndOpenOTPResponse: instance.getTicketAndOpenOTPResponse,
            wave: true,
          });
        }
      }
    }
  }, [env, thirdPartyAuthMethodConfigs, clientId]);
  return {
    ...waveInfo,
  };
};

export default useWave;
