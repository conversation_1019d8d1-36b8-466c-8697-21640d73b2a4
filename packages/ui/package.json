{"name": "@iam/login-ui", "version": "4.0.0-thirdApp.229", "description": "login-component", "author": "chen.qian", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "files": ["dist", "CHANGELOG.md", "dist-css"], "license": "MIT", "scripts": {"build": "vite build && tsc && tsc-alias", "dev": "pnpm run /^dev:.*/", "dev:vite": "vite build --watch", "dev:tsc": "tsc -w", "dev:tsc-alias": "tsc-alias -w"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@types/js-cookie": "^3.0.2", "@types/react": "17.0.2", "@types/react-dom": "17.0.2", "postcss-increase-specificity": "^0.6.0"}, "dependencies": {"@ant-design/cssinjs": "1.20.0", "@babel/runtime": "^7.22.5", "@iam/login-captcha": "^1.1.0", "@iam/login-shared": "workspace:*", "@otakus/design": "0.11.0-beta.18", "@otakus/icons": "^10.3.0", "@react-spring/web": "^9.7.5", "@types/inputmask": "5.0.7", "@types/lodash.groupby": "4.6.9", "@types/react-highlight-words": "0.20.0", "ahooks": "^3.5.2", "antd": "5.17.4", "classnames": "^2.3.1", "clipboard": "^2.0.11", "events": "3.3.0", "inputmask": "5.0.9", "js-cookie": "^3.0.1", "lodash.groupby": "4.6.0", "query-string": "^7.1.1", "react": "17.0.2", "react-circular-progressbar": "^2.1.0", "react-dom": "17.0.2", "react-highlight-words": "0.20.0", "react-player": "^2.16.0", "react-svg": "^16.1.0", "uuid": "^8.3.2"}, "sideEffects": ["**/*.less", "**/*.css"]}