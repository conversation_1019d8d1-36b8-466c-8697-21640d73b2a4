import secondPasswdEmpty from '@ui/assets/second-passwd-empty.svg';
import { ReactSVG } from 'react-svg';
import { goAccount, prefixCls, useConfig, useLocale } from '@iam/login-shared';
import './index.less';
import { Button } from '@otakus/design';
import React from 'react';
import cls from 'classnames';

const Index = () => {
  const __ = useLocale();
  const { env, lang } = useConfig();
  const [pre, after] = __('security:verification:password:empty:desc').split(
    '|',
  );

  return (
    <div className={`${prefixCls}-second-verification-passwd-empty-wrapper`}>
      <div className={`${prefixCls}-second-verification-passwd-empty-head`}>
        <ReactSVG src={secondPasswdEmpty}></ReactSVG>
      </div>
      <div className={`${prefixCls}-second-verification-passwd-empty-title`}>
        {__('security:verification:password:empty:title')}
      </div>
      <div
        className={cls(`${prefixCls}-second-verification-passwd-empty-desc`, {
          [`${prefixCls}-second-verification-passwd-empty-desc-en`]:
            lang === 'en-US',
        })}
      >
        {/*{pre}*/}
        {/*<Button*/}
        {/*  type={'link'}*/}
        {/*  className={`${prefixCls}-second-verification-passwd-empty-desc-link`}*/}
        {/*  onClick={() => goAccount(env, lang)}*/}
        {/*>*/}
        {/*  {__('security:verification:password:empty:link')}*/}
        {/*</Button>*/}
        {/*{after}*/}
        {__('security:verification:password:empty:desc:v2')}
      </div>
      <Button type="outlined" onClick={() => goAccount(env, lang)}>
        {__('security:verification:password:empty:link:v2')}
      </Button>
    </div>
  );
};

export default Index;
