import {
  IV3_SCENE_TYPE,
  IV3MfaConfig,
  prefixCls,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import React from 'react';
import './index.less';
import SecondVerificationForm from './Form';
import { Button, Tooltip, useTheme } from '@otakus/design';
import { LeftOutlined } from '@otakus/icons';
import Title from '@ui/components/Title';
import cls from 'classnames';

export interface SecondVerificationProps {
  visible: boolean;
  method: IV3MfaConfig[];
  handleVisible: (visible: boolean) => void;
  cb: (mfaCode?: string) => void;
  scene?: IV3_SCENE_TYPE;
  back?: boolean;
  closeable?: boolean;
  onCancel?: () => void;
}

const Index = (props: SecondVerificationProps) => {
  const { visible, handleVisible, back = true, closeable, onCancel } = props;
  const { size } = useSize();
  const { mask, scene } = useConfig();
  const __ = useLocale();
  const _scene =
    scene === IV3_SCENE_TYPE.InitialLogin ? IV3_SCENE_TYPE.SecondCheck : scene;
  const token = useTheme();
  if (!visible) {
    return null;
  }

  const _onCancel = () => {
    // 单独使用 mfa 可关闭弹窗
    if (closeable) {
      onCancel?.();
    } else {
      handleVisible?.(false);
    }
  };

  return (
    <div
      className={cls(`${prefixCls}-second-verification-wrapper`, {
        [`${prefixCls}-second-verification-wrapper-xs`]:
          size === ViewportSize.xs,
      })}
    >
      <div className={`${prefixCls}-second-verification-head`}>
        {(back || closeable) && (
          <Tooltip
            title={__('back:btn:text')}
            placement={'top'}
            trigger={['hover']}
          >
            <Button
              className={`${prefixCls}-second-verification-back`}
              icon={<LeftOutlined style={{ color: 'inherit' }} />}
              onClick={_onCancel}
            ></Button>
          </Tooltip>
        )}
        <div
          className={cls({
            [`${prefixCls}-second-verification-title-xs`]:
              size === ViewportSize.xs,
          })}
        >
          <Title title={__('security:verification:title')} />
        </div>
      </div>
      <SecondVerificationForm {...props} scene={_scene} />
    </div>
    // <Drawer
    //   open={visible}
    //   onClose={() => handleVisible(false)}
    //   title={__('security:verification:title')}
    //   getContainer={size === ViewportSize.xs ? 'body' : false}
    //   width={'100%'}
    //   rootClassName={cls(`${prefixCls}-second-verification-drawer`, {
    //     [`${prefixCls}-second-verification-drawer-xs`]:
    //       size === ViewportSize.xs,
    //     [`${prefixCls}-second-verification-drawer-mask`]: mask,
    //   })}
    //   destroyOnClose
    //   headClassName={
    //     size === ViewportSize.xs
    //       ? `${prefixCls}-second-verification-drawer-head-xs`
    //       : `${prefixCls}-second-verification-drawer-head`
    //   }
    // >
    //
    // </Drawer>
  );
};

export default Index;
