@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-second-verification-drawer {
    overflow-x: hidden;
    min-height: 490px;

    .otakus-drawer-content {
      //background: transparent;
    }

    .otakus-drawer-content-wrapper {
      box-shadow: unset;
    }
  }

  &-second-verification-drawer-mask {
    .otakus-drawer-content {
      //border-radius: 24px;
    }

    .otakus-drawer-body {
      padding: 0 !important;
    }
  }

  &-second-verification-drawer-xs {
    .otakus-drawer-content {
      padding-top: 60px;
      background: linear-gradient(
          168deg,
          rgba(78, 95, 246, 0.1) 5.92%,
          rgba(78, 95, 246, 0.05) 11.95%,
          rgba(78, 95, 246, 0) 25.36%
        ),
        #fff;
    }
  }

  &-second-verification-totp-desc {
    color: var(--otakus-color-text-secondary);
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 12px;
    text-align: center;
  }

  &-second-verification-passwd-empty-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-direction: column;
    margin-top: 40px;
  }

  &-second-verification-passwd-empty-head {
    margin-bottom: 12px;
  }

  &-second-verification-passwd-empty-title {
    margin-bottom: 4px;
    color: var(--otakus-color-text-label);
    font-weight: 500;
  }

  &-second-verification-passwd-empty-desc {
    color: var(--otakus-color-text-description);
    margin-bottom: 24px;
  }

  &-second-verification-passwd-empty-desc-en {
    text-align: center;
  }

  &-second-verification-passwd-empty-desc-link {
    color: var(--otakus-color-text-description);
    padding: 0 4px;
    font-weight: 400;

    &:hover {
      color: var(--otakus-color-link-hover);
    }

    &:focus,
    &:focus-within {
      color: var(--otakus-color-link);
    }

    span {
      text-decoration: underline;
    }
  }

  &-second-verification-passwd-reset {
    color: var(--otakus-color-text-description);
    margin-top: 24px;
    width: 100%;

    &:hover {
      color: var(--otakus-color-link-hover);
    }

    &:focus,
    &:focus-within {
      color: var(--otakus-color-link);
    }

    span {
      text-decoration: underline;
    }
  }

  &-second-verification-submit {
    width: 100%;
    height: 54px;
    margin-top: 24px;
    border-radius: 12px;
  }

  &-second-verification-form-wrapper {
    width: 100%;

    .-form-item-explain-error {
      display: none;
    }

    .otakus-form-item {
      margin-top: -1px;
      z-index: 1;
      margin-bottom: 0;
    }

    .otakus-form-item:hover {
      z-index: 4; /* 悬停状态的 z-index */
    }

    .otakus-form-item:focus-within {
      z-index: 5; /* 聚焦状态的 z-index */
    }

    .otakus-form-item:active {
      z-index: 6; /* 激活状态的 z-index */
    }

    .otakus-input-status-error {
      z-index: 3; /* 报错状态的 z-index */
    }

    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }
  }

  &-second-verification-instance-title {
    margin-bottom: 36px;
  }

  &-second-verification-drawer-head {
    margin-bottom: 36px !important;
  }

  &-second-verification-drawer-head-xs {
    margin-bottom: 24px !important;
  }

  &-second-verification-wrapper {
    width: 100%;
    background: #fff;
    //display: flex;
    //align-items: center;
    //justify-content: center;
  }

  &-second-verification-head {
    margin-bottom: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-second-verification-back {
    color: var(--otakus-color-text-description);
    position: absolute;
    left: 0;

    &:hover {
      color: var(--otakus-button-default-hover-border-color);
    }

    &:focus:active:focus-within {
      color: var(--otakus-button-default-active-border-color);
    }
  }

  &-second-verification-wrapper-xs {
    background: transparent;
    margin-top: 36px;
  }

  &-second-passwd-input {
    .otakus-input-affix-wrapper {
      border-radius: 12px;
    }
  }

  &-second-verification-title-xs {
    font-size: 24px;
    line-height: 32px;

    .@{info-iam-login-prefix-cls}-title {
      font-size: var(--otakus-font-size-heading-3);
      line-height: var(--otakus-line-height-heading-3);
    }
  }
}
