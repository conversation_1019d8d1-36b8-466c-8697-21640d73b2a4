import {
  getSecondConfig,
  goSecondPwd,
  IV3_SCENE_TYPE,
  IV3AuthMethodConfigs,
  IV3Method,
  prefixCls,
  secondAuthSubmit,
  ServiceErrorCodes,
  setVisible,
  useConfig,
  useKeyPressSubmit,
  useLocale,
} from '@iam/login-shared';
import Tab from '@ui/components/Tab';
import { Button, Form } from '@otakus/design';
import SettingPwd from '@ui/components/SecondVerification/SettingPwd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { SecondVerificationProps } from './index';
import { useThrottleFn } from 'ahooks';
import Cookies from 'js-cookie';
import { handleFormErrorTooltip } from '@ui/components/LoginForm/helper';
import { OTPRef } from 'antd/es/input/OTP';
import SettingOtp from '@ui/components/SecondVerification/SettingOtp';
import Totp from '@ui/components/SecondVerification/Totp';
import Passwd from '@ui/components/SecondVerification/Passwd';

const Index = (props: SecondVerificationProps) => {
  const {
    visible,
    method,
    handleVisible,
    cb,
    scene = IV3_SCENE_TYPE.SecondCheck,
  } = props;
  const [active, setActive] = useState<IV3Method>('' as unknown as IV3Method);
  const [loading, setLoading] = useState<boolean>(false);
  const { authApi, clientId, lang, env } = useConfig();
  const [submittable, setSubmittable] = React.useState<boolean>(false);
  const __ = useLocale();
  const [form] = Form.useForm();
  const rememberMe = Cookies.get('threeDay') === '1';
  const activeRef = useRef<IV3Method>(null as unknown as IV3Method);
  const codeInputRef = useRef<OTPRef>(null);

  useEffect(() => {
    handleTabChange(method?.[0]?.method);
  }, [method]);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  const handleTabChange = (_active: IV3Method) => {
    if (_active === active) {
      return;
    }
    activeRef.current = _active;
    form.resetFields();
    setActive(_active);
  };

  const secondAuthCallback = async (mfa = '') => {
    setLoading(false);
    form?.resetFields();
    cb(mfa);
  };

  const tooltipErrorMap = useMemo(() => {
    const defaultMap = new Map([
      [ServiceErrorCodes.SecondaryPwdNotMatch, 'password'],
      [ServiceErrorCodes.TotpVerifyFailed, 'code'],
    ]);
    if (active === IV3Method.TOTP) {
      defaultMap.set(ServiceErrorCodes.TotpAuthExpired, 'code');
    } else {
      defaultMap.set(ServiceErrorCodes.SecondaryAuthExpired, 'password');
    }
    return defaultMap;
  }, [active]);

  const { run: submit } = useThrottleFn(
    () => {
      form
        .validateFields()
        .then(async (val: any) => {
          setLoading(true);
          const data = await secondAuthSubmit(
            {
              ...val,
              rememberMe: rememberMe ? 1 : 0,
              otp_scene: scene,
            },
            active,
            authApi,
            clientId,
            secondAuthCallback,
          );
          setLoading(false);
          if (data?.error) {
            handleFormErrorTooltip(form, data.error, tooltipErrorMap, false);
            // 动态口令有误，请重新输入 清空输入框
            if (data?.error?.code === ServiceErrorCodes.TotpVerifyFailed) {
              form.setFieldValue('code', '');
              codeInputRef.current?.focus();
            }
          }
        })
        .catch((e) => {
          console.log(e);
          setLoading(false);
          return null;
        });
    },
    {
      wait: 500,
    },
  );

  const handleFormValuesChange = (values: any) => {
    if (active === IV3Method.TOTP) {
      if (values) {
        Promise.resolve().then(submit);
      }
    }
  };

  const get2ndPwdConfig = () => {
    const config = getSecondConfig(active, method);
    return config?.config;
  };

  const code = Form.useWatch('code', form);

  useEffect(() => {
    if (code && code.length === 6) {
      Promise.resolve().then(submit);
    }
  }, [code]);

  const values = Form.useWatch([], form);

  React.useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => setSubmittable(true))
      .catch(() => setSubmittable(false));
  }, [form, values]);

  useKeyPressSubmit(
    () => {
      if (!loading && submittable && active === IV3Method.SecondPwd) {
        submit();
      }
      return;
    },
    {
      target: document.querySelector('#second_auth'),
    },
  );

  return (
    <>
      <div className={`${prefixCls}-second-verification-tab`}>
        {setVisible({
          visible: method.length > 0,
          component: (
            <Tab
              authMethodConfigs={method as unknown as IV3AuthMethodConfigs[]}
              active={active}
              onChange={handleTabChange}
            />
          ),
        })}
      </div>
      <Form
        form={form}
        name={'secondVerification'}
        className={`${prefixCls}-second-verification-form-wrapper`}
        disabled={loading}
        id={'second_auth'}
      >
        {setVisible({
          visible:
            active === IV3Method.TOTP && scene == IV3_SCENE_TYPE.SecondCheck,
          component: <Totp codeInputRef={codeInputRef} />,
        })}
        {setVisible({
          visible:
            active === IV3Method.TOTP &&
            scene == IV3_SCENE_TYPE.InitialRestPasswd,
          component: !get2ndPwdConfig()?.need_bind_otp ? (
            <Totp />
          ) : (
            <SettingOtp />
          ),
        })}
        {setVisible({
          visible: active === IV3Method.SecondPwd,
          component: get2ndPwdConfig()?.exist_2nd_pwd ? (
            <Passwd form={form} />
          ) : (
            <SettingPwd />
          ),
        })}
      </Form>
      {setVisible({
        visible:
          active === IV3Method.SecondPwd && get2ndPwdConfig()?.exist_2nd_pwd,
        component: (
          <>
            <Button
              type="primary"
              className={`${prefixCls}-second-verification-submit`}
              size="large"
              onClick={submit}
              disabled={!submittable}
              loading={loading}
            >
              {__('security:verification:password:submit')}
            </Button>
            <Button
              type={'link'}
              className={`${prefixCls}-second-verification-passwd-reset`}
              onClick={() => goSecondPwd(env, lang)}
            >
              {__('security:verification:password:reset')}
            </Button>
          </>
        ),
      })}
    </>
  );
};

export default Index;
