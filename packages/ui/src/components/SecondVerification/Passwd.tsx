import { FormItem, FormItems } from '@ui/components/Form/FormItem';
import Input from '@ui/components/Input';
import { Form, FormInstance } from '@otakus/design';
import { prefixCls, useLocale, useSize, ViewportSize } from '@iam/login-shared';

const { Item } = Form;

interface SecondVerificationPasswdProps {
  form: FormInstance;
}

const Index = (props: SecondVerificationPasswdProps) => {
  const __ = useLocale();
  const { size } = useSize();
  const { form } = props;

  const handleClear = () => {
    form.setFields([{ name: 'password', value: '', errors: [] }]);
  };

  return (
    <FormItems>
      <FormItem
        wrap
        name="password"
        label=""
        rules={[
          { whitespace: true, message: __('second:password:message') },
          { required: true, message: __('second:password:message') },
        ]}
      >
        <div
          style={{ width: '100%' }}
          className={`${prefixCls}-second-passwd-input`}
        >
          <Input
            passwd
            placeholder={__('security:verification:password:message')}
            autoFocus={size !== ViewportSize.xs}
            autoComplete={'new-password'}
            onClear={handleClear}
          />
        </div>
      </FormItem>
    </FormItems>
  );
};

export default Index;
