import secondPasswdEmpty from '@ui/assets/second-passwd-empty.svg';
import { ReactSVG } from 'react-svg';
import {
  createOtpLink,
  goOtpLink,
  OtpSourceType,
  prefixCls,
  useConfig,
  useLocale,
} from '@iam/login-shared';
import './index.less';
import React from 'react';
import { parseUrl, stringify } from 'query-string';
import { Button } from '@otakus/design';
import cls from 'classnames';

const Index = () => {
  const __ = useLocale();
  const { env, lang, flowToken } = useConfig();
  const [pre, after] = __('security:verification:totp:empty:desc').split('|');

  const _goOtpLink = async () => {
    const url = await createOtpLink(env);
    if (!url) {
      return;
    }
    const parsedUrl = parseUrl(url);
    const search = stringify({
      lang,
      // flow_token: flowToken,
      otp_source_type: OtpSourceType.PWD,
      ...parsedUrl.query,
    });
    const _url = `${parsedUrl.url}?${search}`;
    goOtpLink(_url);
  };

  return (
    <div className={`${prefixCls}-second-verification-passwd-empty-wrapper`}>
      <div className={`${prefixCls}-second-verification-passwd-empty-head`}>
        <ReactSVG src={secondPasswdEmpty}></ReactSVG>
      </div>
      <div className={`${prefixCls}-second-verification-passwd-empty-title`}>
        {__('security:verification:totp:empty:title')}
      </div>
      <div
        className={cls(`${prefixCls}-second-verification-passwd-empty-desc`, {
          [`${prefixCls}-second-verification-passwd-empty-desc-en`]:
            lang === 'en-US',
        })}
      >
        {__('security:verification:totp:empty:desc:v2')}
      </div>
      <Button type="outlined" onClick={_goOtpLink}>
        {__('security:verification:totp:link:v2')}
      </Button>
    </div>
  );
};

export default Index;
