import {
  getIframe,
  getIV3MfaConfig,
  IV3_SCENE_TYPE,
  postDestroy,
  prefixCls,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { useAsyncEffect, useSetState } from 'ahooks';
import SecondVerification, {
  SecondVerificationProps,
} from '@ui/components/SecondVerification';
import Mask from '@ui/components/Mask';
import SecondVerificationForm from './Form';
import Title from '@ui/components/Title';
import './index.less';
import React from 'react';
import { Wrapper } from '@ui/components/Wrapper';

interface SecondVerificationInstanceProps {
  cb: SecondVerificationProps['cb'];
  showMfaCloseIcon?: boolean;
}

const Index = (props: SecondVerificationInstanceProps) => {
  const { cb, showMfaCloseIcon } = props;
  const { env } = useConfig();
  const [secondAuths, setSecondAuth] = useSetState<SecondVerificationProps>(
    {} as unknown as SecondVerificationProps,
  );

  const __ = useLocale();
  const { size } = useSize();
  useAsyncEffect(async () => {
    const secondAuths = await getIV3MfaConfig(env);
    setSecondAuth({
      method: secondAuths?.methods || [],
      visible: true,
      cb,
    });
  }, [env]);

  const handleCancel = () => {
    setSecondAuth({ visible: false });
    if (getIframe()) {
      postDestroy();
    }
  };

  if (size === ViewportSize.xs) {
    return (
      <Wrapper>
        <SecondVerification
          {...secondAuths}
          back={false}
          scene={IV3_SCENE_TYPE.InitialLogin}
          closeable={showMfaCloseIcon}
          onCancel={handleCancel}
        />
      </Wrapper>
      // <Drawer
      //   open={secondAuths.visible}
      //   title={__('security:verification:title')}
      //   showCloseIcon={false}
      //   getContainer={size === ViewportSize.xs ? 'body' : false}
      //   width={'100%'}
      //   rootClassName={cls(`${prefixCls}-second-verification-drawer`, {
      //     [`${prefixCls}-second-verification-drawer-xs`]:
      //       size === ViewportSize.xs,
      //   })}
      //   destroyOnClose
      // >
      //   <SecondVerificationForm
      //     {...secondAuths}
      //     handleVisible={(visible: boolean) => setSecondAuth({ visible })}
      //   />
      // </Drawer>
    );
  }

  return (
    <Mask
      open={secondAuths.visible}
      closable={showMfaCloseIcon}
      onCancel={handleCancel}
      maskClosable={false}
    >
      <div className={`${prefixCls}-second-verification-instance-title`}>
        <Title title={__('security:verification:title')} />
      </div>
      <SecondVerificationForm
        {...secondAuths}
        handleVisible={(visible: boolean) => setSecondAuth({ visible })}
      ></SecondVerificationForm>
    </Mask>
  );
};

export default Index;
