import { FormItem, FormItems } from '@ui/components/Form/FormItem';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { Form } from '@otakus/design';
import { prefixCls, useLocale, useSize, ViewportSize } from '@iam/login-shared';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';
import { MutableRefObject, useCallback } from 'react';
import { OTPRef } from 'antd/es/input/OTP';

const { Item } = Form;

export type TOTPFormProps = {
  codeInputRef?: MutableRefObject<OTPRef>;
};

const Index = (props: TOTPFormProps) => {
  const __ = useLocale();
  const { codeInputRef } = props;
  const { size } = useSize();
  const form = Form.useFormInstance();

  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: 'code', errors: [] }]);
  }, []);

  return (
    <>
      <div className={`${prefixCls}-second-verification-totp-desc`}>
        {__('security:verification:totp:desc:v2')}
      </div>
      <FormItems>
        <FormItem
          name="code"
          wrap
          validateFirst
          rules={[
            { whitespace: true, message: __('opt:message') },
            { required: true, message: __('opt:message') },
          ]}
        >
          <CodeInput
            codeInputRef={codeInputRef}
            onCharChange={onCodeCharChange}
            autoFocus={size !== ViewportSize.xs}
            onPaste={handleOtpPaste('code', form)}
          />
        </FormItem>
      </FormItems>
    </>
  );
};

export default Index;
