import type { FC, Key, ReactNode } from 'react';
// import { NativeProps } from '../../utils/native-props
import { List } from '@otakus/design';
import { prefixCls } from '@iam/login-shared';

export type PanelItem<T> = {
  /**
   * 如果没有title，认为是自由元素。用于置顶等UI形式
   */
  title?: ReactNode;
  key?: Key;
  index: ReactNode;
  items?: T[];
};

export type IndexBarPanelProps<T> = {
  brief?: ReactNode;
  children?: ReactNode;
  data?: PanelItem<T>;
  renderItem?: (item: T, index: number) => ReactNode;
  onSelect?: (item: T) => void;
};

export function Panel<T>({
  data,
  renderItem,
  onSelect,
}: IndexBarPanelProps<T>) {
  return (
    <>
      <div data-index={data?.index} className={`${prefixCls}-index-bar-anchor`}>
        {data.title ? (
          <div className={`${prefixCls}-index-bar-anchor-title`}>
            {data.title || data.index}
          </div>
        ) : null}
        <div className={`${prefixCls}-index-bar-anchor-list`}>
          <List itemLayout="horizontal">
            {data.items.map((item, index) => {
              return (
                <List.Item
                  key={/** safe ignore */ index}
                  onClick={() => onSelect?.(item)}
                >
                  <div className={`${prefixCls}-index-bar-anchor-list-item`}>
                    {renderItem?.(item, index)}
                  </div>
                </List.Item>
              );
            })}
          </List>
        </div>
      </div>
    </>
  );
}
