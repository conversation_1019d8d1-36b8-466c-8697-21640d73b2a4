import classNames from 'classnames';
import type { MutableRefObject, ReactNode } from 'react';
import { useImperativeHandle, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Sidebar } from './SideBar';
import { Panel, PanelItem } from './Panel';
import { prefixCls } from '@iam/login-shared';
import './index.less';

const classPrefix = `${prefixCls}-index-bar`;

type IndexBarRefHandle = {
  scrollTo?: (index: string) => void;
};

export type IndexItem = { index: string; brief: ReactNode };

export type IndexBarProps<T> = {
  sticky?: boolean;
  onIndexChange?: (index: string) => void;
  // children?: ReactNode;
  items: PanelItem<T>[];

  indexItems?: IndexItem[];

  onSelect?: (item: T) => void;

  raf?: MutableRefObject<IndexBarRefHandle>;

  renderItem?: (item: T, index: number) => ReactNode;
};

export type IndexBarRef = {
  scrollTo: (index: string) => void;
};

function convertPx() {
  return 35;
}

const defaultProps = {
  sticky: true,
};

export function IndexBar<T>(props: IndexBarProps<T>) {
  const {
    items,
    raf,
    onSelect,
    indexItems = [],
    renderItem,
    sticky = true,
  } = props;
  const titleHeight = convertPx();
  const bodyRef = useRef<HTMLDivElement>(null);

  const [activeIndex, setActiveIndex] = useState(() => {
    const firstItem = indexItems[0];
    return firstItem ? firstItem.index : null;
  });

  function scrollTo(index: string) {
    const body = bodyRef.current;
    if (!body) return;

    const children = body.children;
    for (let i = 0; i < children.length; i++) {
      const panel = children.item(i) as HTMLElement;
      if (!panel) continue;
      const panelIndex = panel.dataset['index'];
      if (panelIndex === index) {
        body.scrollTop = panel.offsetTop;
        setActiveIndex(index);
        activeIndex !== index && props.onIndexChange?.(index);
        return;
      }
    }
  }

  useImperativeHandle(raf, () => ({ scrollTo }));

  const { run: checkActiveIndex } = useThrottleFn(
    () => {
      const body = bodyRef.current;
      if (!body) return;
      const scrollTop = body.scrollTop;

      const elements = body.getElementsByClassName(`${classPrefix}-anchor`);
      for (let i = 0; i < elements.length; i++) {
        const panel = elements.item(i) as HTMLElement;
        if (!panel) continue;
        const panelIndex = panel.dataset['index'];
        if (!panelIndex) continue;
        if (panel.offsetTop + panel.clientHeight - titleHeight > scrollTop) {
          setActiveIndex(panelIndex);
          activeIndex !== panelIndex && props.onIndexChange?.(panelIndex);
          return;
        }
      }
    },
    { wait: 50, trailing: true, leading: true },
  );

  return (
    <div
      className={classNames(`${classPrefix}`, {
        [`${classPrefix}-sticky`]: sticky,
      })}
    >
      <Sidebar
        indexItems={indexItems}
        activeIndex={activeIndex}
        onActive={(index) => {
          scrollTo(index);
        }}
      />

      <div
        className={`${classPrefix}-body`}
        ref={bodyRef}
        onScroll={checkActiveIndex}
      >
        {items.map((item) => (
          <Panel
            data={item}
            key={item.key}
            renderItem={renderItem}
            onSelect={onSelect}
          />
        ))}
      </div>
    </div>
  );
}
