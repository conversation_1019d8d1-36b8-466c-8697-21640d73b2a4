import { IndexBar } from './IndexBar';

export function IndexBarDebug() {
  return (
    <IndexBar
      renderItem={() => {
        return 'A';
      }}
      indexItems={[
        { brief: 'A', index: '0' },
        { brief: 'B', index: '1' },
      ]}
      items={[
        {
          items: [{}, {}],
          key: 'A',
          index: 0,
        },
        {
          title: 'A',
          items: [{}, {}],
          key: 'A',
          index: 0,
        },
        {
          title: 'B',
          items: [{}, {}],
          key: 'B',
          index: 1,
        },
      ]}
    />
  );
}
