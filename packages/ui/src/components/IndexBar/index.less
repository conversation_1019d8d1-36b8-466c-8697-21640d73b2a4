@import '../../index.less';

.@{info-iam-login-prefix-cls}-index-bar {
  --color: var(--otakus-color-text);
  overflow: hidden;

  height: 100%;
  position: relative;
  background-color: var(--otakus-color-background);
  --sticky-offset-top: 0;

  &-body {
    overflow-y: scroll;
    height: 100%;
    width: 100%;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  &-anchor {
    &-title {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: var(--otakus-size-xxl, 36px);
      padding: 0 var(--otakus-padding-sm, 12px);
      color: var(--otakus-color-weak, rgba(2, 6, 43, 0.43));
      font-weight: 500;
      font-size: var(--otakus-font-size-main);
      background-color: var(
        --otakus-color-neutral-fill,
        rgba(248, 248, 251, 1)
      );
    }

    &-list-item {
      padding: var(---otakus-padding-sm, 12px) 0;
      border-bottom: solid 1px
        var(--otakus-color-neutral-border, rgba(22, 33, 99, 0.09));
      width: 100%;
    }

    &-list {
      padding: 0 var(--otakus-padding-sm, 12px);

      .otakus-list-item {
        border: 0;
        padding: 0;
      }
    }
  }

  &-sidebar {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    z-index: 910;
    overflow: visible;
    color: var(--otakus-color-neutral-weak, rgba(2, 6, 43, 0.43));
    font-size: var(--otakus-font-size-sm);
    user-select: none;
    touch-action: none;

    &-bubble {
      position: absolute;
      top: 50%;
      right: 60px;
      width: 47px;
      height: 47px;
      color: #fff;
      font-size: 25px;
      line-height: 47px;
      text-align: center;
      background: #dddde5;
      border-radius: 50%;
      transform: translateY(-50%);
    }

    &-row {
      cursor: pointer;
      width: auto;
      text-align: right;
      position: relative;
      padding: 0 12px;
      > * {
        pointer-events: none;
      }
    }

    &-item {
      display: inline-flex;
      width: 16px;
      height: 16px;
      line-height: 16px;
      justify-content: center;
      align-items: center;
    }

    &-item-active {
      color: var(--otakus-color-text-light-solid);
      background-color: var(--otakus-color-primary);
      border-radius: 50%;
    }

    &-interacting {
      // width: 100%;
    }
  }

  &-sticky {
    .@{info-iam-login-prefix-cls}-index-bar-anchor-title {
      position: sticky;
      z-index: 900;
      top: var(--sticky-offset-top);
      left: 0;
    }
  }
}
