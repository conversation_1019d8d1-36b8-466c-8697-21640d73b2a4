import { prefixCls, useAssets, useSize, ViewportSize } from '@iam/login-shared';
import { ReactSVG } from 'react-svg';
// import EmptySvg from '@ui/assets/empty.svg';
import React from 'react';
import './index.less';
import cls from 'classnames';
import { OuterWithoutVisual } from '@ui/components/Wrapper';

interface EmptyProps {
  title: string;
  description: string;
}

const Index = (props: EmptyProps) => {
  const { title, description } = props;
  const { size } = useSize();
  const EmptySvg = useAssets('empty.svg');
  return (
    <OuterWithoutVisual>
      <div
        className={cls(`${prefixCls}-empty-wrapper`, {
          [`${prefixCls}-empty-wrapper-xs`]: size === ViewportSize.xs,
        })}
      >
        <div className={`${prefixCls}-empty-top`}></div>
        <div
          className={cls(`${prefixCls}-empty-content`, {
            [`${prefixCls}-empty-content-xs`]: size === ViewportSize.xs,
          })}
        >
          <div
            className={cls(`${prefixCls}-empty-left`, {
              [`${prefixCls}-empty-left-xs`]: size === ViewportSize.xs,
            })}
          >
            <span
              className={cls(`${prefixCls}-empty-title`, {
                [`${prefixCls}-empty-title-xs`]: size === ViewportSize.xs,
              })}
            >
              {title}
            </span>
            <span
              className={cls(`${prefixCls}-empty-description`, {
                [`${prefixCls}-empty-description-xs`]: size === ViewportSize.xs,
              })}
            >
              {description}
            </span>
          </div>
          <div
            className={cls(`${prefixCls}-empty-right`, {
              [`${prefixCls}-empty-right-xs`]: size === ViewportSize.xs,
            })}
          >
            <ReactSVG
              className={cls(`${prefixCls}-empty-svg`, {
                [`${prefixCls}-empty-svg-xs`]: size === ViewportSize.xs,
              })}
              src={EmptySvg}
            />
          </div>
        </div>
        <div className={`${prefixCls}-empty-bottom`}></div>
      </div>
    </OuterWithoutVisual>
  );
};

export default Index;
