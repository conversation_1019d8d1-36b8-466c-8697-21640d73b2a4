@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-empty-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    //justify-content: center;
    flex-direction: column;
  }

  &-empty-wrapper-xs {
    //flex-direction: column-reverse;
    flex-direction: column;
  }

  &-empty-left {
    display: flex;
    flex-direction: column;
  }

  &-empty-left-xs {
    align-items: center;
  }

  &-empty-right {
    margin-left: 80px;
  }

  &-empty-right-xs {
    margin-left: unset;
  }

  &-empty-title {
    font-size: 48px;
    font-weight: 600;
    color: var(--otakus-color-text-heading);
    margin-bottom: 24px;
  }

  &-empty-title-xs {
    font-size: 18px;
    margin-bottom: 4px;
  }

  &-empty-description {
    font-size: 20px;
    font-weight: 400;
    color: rgba(0, 5, 42, 0.62);
  }

  &-empty-description-xs {
    font-size: 14px;
    color: rgba(0, 5, 42, 0.62);
  }

  &-empty-svg {
    width: 320px;
    height: 320px;
  }

  &-empty-svg-xs {
    width: 198px;
    height: 198px;
  }

  &-empty-top {
    flex: 6;
  }

  &-empty-bottom {
    flex: 10;
  }

  &-empty-content {
    display: flex;
    align-items: center;
  }

  &-empty-content-xs {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
  }
}
