@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-navigation-wrapper {
    width: 100%;
    // height: 52px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    // padding: 20px 24px 0;
    // padding: 0 0 0 20px;
    z-index: 1000;
    // position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  &-navigation-wrapper-xs {
    align-items: center;
    padding: 0 24px 0;
  }

  &-navigation-wrapper-xs &-navigation-left {
    padding: 12px 0;
  }

  &-navigation-left {
    display: flex;
    padding: 20px 24px;
    align-items: flex-end;
    //height: 76px;
  }

  &-navigation-operation-wrapper {
    height: calc(6.25vw - 24px);
    position: relative;
  }

  &-navigation-operation {
    height: 6.25vw;
    // height: calc(6.25vw - 24px);
    // margin-top: -20px;
    // margin-right: -24px;
    width: 50vw;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  &-navigation-operation-switch-lang {
    position: absolute;
    top: 20px;
    right: 32px;
  }

  &-navigation-company-logo {
    height: 36px;
    //width: 102px;
    div {
      height: 36px;
    }

    svg {
      height: 36px;
      //width: 102px;
    }
  }

  //@media (min-width: 1920px) {
  //  &-navigation-company-logo {
  //    //width: calc(102 * (100vw / 1920)); /* 根据当前视口宽度计算宽度 */
  //    height: calc(36 * (100vw / 1920)); /* 根据当前视口宽度计算高度 */
  //
  //    svg {
  //      //width: calc(102 * (100vw / 1920)); /* 根据当前视口宽度计算宽度 */
  //      height: calc(36 * (100vw / 1920)); /* 根据当前视口宽度计算高度 */
  //    }
  //  }
  //}

  &-navigation-company-logo-xs {
    height: 24px;
    //width: 68px;
    div {
      height: 24px;
    }

    svg {
      height: 24px;
      //width: 68px;
      width: auto;
    }
  }

  &-navigation-operation-position-left {
    width: 33%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(
      90deg,
      #fff 0.28%,
      rgba(255, 255, 255, 0.5) 59.94%,
      rgba(255, 255, 255, 0) 99.72%
    );
  }

  &-navigation-operation-position-top {
    width: 100%;
    height: 6px;
    top: 0;
    position: absolute;
    background: rgba(255, 255, 255, 0.32);
    filter: blur(8px);
    backdrop-filter: blur(5px);
  }

  &-navigation-operation-position-bottom {
    width: 100%;
    height: 6px;
    bottom: 0;
    position: absolute;
    background: rgba(255, 255, 255, 0.32);
    filter: blur(8px);
    backdrop-filter: blur(5px);
  }

  &-navigation-operation-navigation-operation-switch-lang {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  &-navigation-switch-lang {
    margin-top: 20px;
    margin-right: 24px;
  }

  &-navigation-switch-lang-xs {
    margin-top: 0;
    margin-right: 0;
  }

  &-navigation-wrapper-lg {
    height: 76px;
  }
}
