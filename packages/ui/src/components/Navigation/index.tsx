import {
  getTLD,
  isMiHoYoDomain,
  IV3_SCENE_TYPE,
  prefixCls,
  ServerDomain,
  useAssets,
  useConfig,
  usePageConfig,
  useScaleStyle,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import './index.less';
import SwitchLang from '../SwitchLang';
import { ReactSVG } from 'react-svg'; // import mihoyoLogo from '@ui/assets/mihoyo-logo.svg';
// import cogLogo from '@ui/assets/cognosphere-logo.svg';
import Mission from '@ui/components/Mission';
import cls from 'classnames';

const Index = () => {
  const tld = getTLD();
  const { width, size } = useSize();
  const { data } = usePageConfig();
  const { operation_banner_pic = '' } = data?.data || {};
  const { scene } = useConfig();
  const mihoyoLogo = useAssets('mihoyo-logo.svg');
  const cogLogo = useAssets('cognosphere-logo.svg');
  const showOperation =
    !!operation_banner_pic &&
    (size === ViewportSize.lg || size === ViewportSize.md) &&
    scene === IV3_SCENE_TYPE.InitialLogin;
  const logo = tld === ServerDomain['mihoyo.com'] ? mihoyoLogo : cogLogo;
  const operationStyle = {
    backgroundImage: `url(${operation_banner_pic})`,
  };

  const operationLangStyle = useScaleStyle('right center');
  const langStyle = useScaleStyle('top right');
  const iconStyle = useScaleStyle('top left');

  return (
    <div
      className={cls(`${prefixCls}-navigation-wrapper`, {
        [`${prefixCls}-navigation-wrapper-xs`]: size === ViewportSize.xs,
        [`${prefixCls}-navigation-wrapper-lg`]: size === ViewportSize.lg,
      })}
    >
      <div className={`${prefixCls}-navigation-left`} style={iconStyle}>
        <ReactSVG
          src={logo}
          className={cls(`${prefixCls}-navigation-company-logo`, {
            [`${prefixCls}-navigation-company-logo-xs`]:
              size === ViewportSize.xs,
          })}
        />
        {width > 1259 && isMiHoYoDomain && <Mission />}
      </div>
      {showOperation ? (
        <div className={`${prefixCls}-navigation-operation-wrapper`}>
          <div
            className={`${prefixCls}-navigation-operation`}
            style={operationStyle}
          >
            <div
              className={`${prefixCls}-navigation-operation-position-left`}
            ></div>
            <div
              className={`${prefixCls}-navigation-operation-position-top`}
            ></div>
            <div
              className={`${prefixCls}-navigation-operation-position-bottom`}
            ></div>
            <div
              className={`${prefixCls}-navigation-operation-navigation-operation-switch-lang`}
              style={operationLangStyle}
            >
              <SwitchLang />
            </div>
          </div>
        </div>
      ) : (
        <div
          className={cls(`${prefixCls}-navigation-switch-lang`, {
            [`${prefixCls}-navigation-switch-lang-xs`]:
              size === ViewportSize.xs,
          })}
          style={langStyle}
        >
          <SwitchLang />
        </div>
      )}
    </div>
  );
};

export default Index;
