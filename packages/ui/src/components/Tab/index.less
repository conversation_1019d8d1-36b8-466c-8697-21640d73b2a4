@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-tab-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    //margin-bottom: var(--otakus-size-lg);
    margin-bottom: 24px;

    .otakus-btn-default {
      background: var(--otakus-color-primary-bg);
      color: var(--otakus-color-primary);
      border-color: transparent;
      box-shadow: none;

      &:active:focus {
        border-color: transparent;
        background: var(--otakus-color-primary-bg);
        color: var(--otakus-color-primary);
      }

      &:hover {
        border-color: transparent;
        background: var(--otakus-color-primary-bg-hover);
        color: var(--otakus-color-primary);
      }
    }

    .otakus-btn-text {
      color: var(--otakus-color-text-description);

      &:hover {
        background: #f1f1f6;
      }
    }
  }

  &-tab-wrapper-xs {
    margin-bottom: 20px;
  }

  &-tab-item {
    min-width: 82px;
    height: 32px;
    font-size: 14px;
    font-weight: 500;
    padding: 0 8px;
  }
}
