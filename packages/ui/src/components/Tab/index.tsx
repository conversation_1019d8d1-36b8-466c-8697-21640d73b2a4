import {
  formatCurMethods,
  IV3_SCENE_TYPE,
  IV3AuthMethodConfigs,
  IV3Method,
  prefixCls,
  useConfig,
  useFirstPaint,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { useMemo } from 'react';
import { Button } from '@otakus/design';
import cls from 'classnames';
import './index.less';

interface TabProps {
  authMethodConfigs?: IV3AuthMethodConfigs[];
  active: IV3Method;
  onChange: (active: IV3Method) => void;
}

const Index = (props: TabProps) => {
  const { authMethodConfigs, active, onChange } = props;
  const __ = useLocale();
  const { scene } = useConfig();
  const { size } = useSize();
  const { firstPaint } = useFirstPaint();
  const _authMethodConfigs = useMemo(() => {
    return formatCurMethods(authMethodConfigs);
  }, [authMethodConfigs]);
  // 默认 active
  // useEffect(() => {
  //   if (_authMethodConfigs && _authMethodConfigs.length) {
  //     const active = _authMethodConfigs[0].method;
  //     onChange(active);
  //   }
  // }, [_authMethodConfigs]);

  const getText = (method: IV3Method) => {
    if (scene === IV3_SCENE_TYPE.InitialLogin) {
      return __(method);
    }
    if (scene === IV3_SCENE_TYPE.InitialRestPasswd) {
      if (method === IV3Method.AccountPwd) {
        return __('passwd:tab:account');
      }
      if (method === IV3Method.SMS) {
        return __('passwd:tab:sms');
      }
      if (method === IV3Method.Email) {
        return __('passwd:tab:email');
      }
      return __(method);
    }
    return __(method);
  };

  const handleClick = (method: IV3Method) => {
    onChange(method);
  };

  if (!_authMethodConfigs || _authMethodConfigs?.length < 2) {
    return null;
  }

  return (
    <div
      className={cls(`${prefixCls}-tab-wrapper`, {
        [`${prefixCls}-tab-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      {_authMethodConfigs.map((item) => {
        const key = item.method;
        const text = getText(item.method);
        return (
          <Button
            key={key}
            onClick={() => handleClick(item.method)}
            shape="round"
            type={active === key ? 'default' : 'text'}
            className={`${prefixCls}-tab-item`}
          >
            {text}
          </Button>
        );
      })}
    </div>
  );
};

export default Index;
