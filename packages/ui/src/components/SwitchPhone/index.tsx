import { prefixCls, useLocale } from '@iam/login-shared';
import { useMemo, useState } from 'react';
import './index.less';
import { Dropdown, MenuProps } from '@otakus/design';
import { ConfigProvider } from 'antd';
import cls from 'classnames';

import downSvg from '@ui/assets/switch-phone-care-down.svg';
import upSvg from '@ui/assets/switch-phone-care-up.svg';
import { ReactSVG } from 'react-svg';

interface SwitchPhoneProps {
  options: {
    cid: string;
    phoneNumber: string;
    value: string;
  }[];
  onChange?: (value: string) => void;
  value?: string;
}

const iconStyle = {
  width: '16px',
  height: '16px',
};

const Index = (props: SwitchPhoneProps) => {
  const __ = useLocale();
  const { options, onChange, value } = props;
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  // 获取表单 disabled 的 状态
  const disabled = ConfigProvider.useConfig().componentDisabled;

  const icon = (
    <ReactSVG src={dropdownVisible ? upSvg : downSvg} style={iconStyle} />
  );

  const cur = useMemo(() => {
    const _cur = options.find((item) => item.value === value);
    return _cur ? `${_cur.cid} ${_cur.phoneNumber}` : '';
  }, [options, value]);

  const items: MenuProps['items'] = options.map((e) => {
    return {
      key: e.value,
      label: (
        <div className={`${prefixCls}-switch-phone-item`}>
          <span className={`${prefixCls}-switch-phone-cid`}>{e.cid}</span>
          <span className={`${prefixCls}-switch-phone-number`}>
            {e.phoneNumber}
          </span>
        </div>
      ),
    };
  });

  items[0].key;

  const handleDropDownOpenChange = (open: boolean) => {
    setDropdownVisible(open);
  };

  const handleMenu: MenuProps['onClick'] = ({ key }) => {
    onChange?.(key);
  };

  return (
    <>
      <Dropdown
        menu={{
          items,
          onClick: handleMenu,
          selectable: true,
          // @ts-ignore
          defaultSelectedKeys: [items[0].key],
        }}
        placement={'bottom'}
        trigger={['click']}
        onOpenChange={handleDropDownOpenChange}
        overlayClassName={`${prefixCls}-switch-phone-overlay`}
      >
        <div
          className={cls(`${prefixCls}-switch-phone-wrapper`, {
            [`${prefixCls}-switch-phone-wrapper-disabled`]: disabled,
          })}
        >
          <div className={`${prefixCls}-switch-phone-left`}>
            <span className={`${prefixCls}-switch-phone-label`}>
              {__('otp:idp:sms:select:label')}
            </span>
            <span className={`${prefixCls}-switch-phone-value`}>{cur}</span>
          </div>
          <div className={`${prefixCls}-switch-phone-right`}>{icon}</div>
        </div>
      </Dropdown>
    </>
  );
};

export default Index;
