@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-switch-phone-wrapper {
    width: 100%;
    background: #fff;
    padding: 9px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid var(--otakus-color-border);
    border-radius: 12px;
    cursor: pointer;
    gap: 12px;

    &:hover {
      border: 1px solid rgba(102, 126, 255, 1);
    }

    &:hover .@{info-iam-login-prefix-cls}-switch-phone-label {
      color: rgba(102, 126, 255, 1);
    }

    &:active .@{info-iam-login-prefix-cls}-switch-phone-label {
      color: rgba(60, 71, 213, 1);
    }

    &:focus &:focus-within .@{info-iam-login-prefix-cls}-switch-phone-label {
      color: rgba(60, 71, 213, 1);
    }

    &:hover .@{info-iam-login-prefix-cls}-switch-phone-value {
      color: rgba(102, 126, 255, 1);
    }

    &:active .@{info-iam-login-prefix-cls}-switch-phone-value {
      color: rgba(60, 71, 213, 1);
    }

    &:focus &:focus-within .@{info-iam-login-prefix-cls}-switch-phone-value {
      color: rgba(60, 71, 213, 1);
    }
  }

  &-switch-phone-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }

  &-switch-phone-label {
    color: var(--otakus-color-text-secondary);
    font-size: 12px;
  }

  &-switch-phone-value {
    font-weight: 500;
    font-size: 16px;
    color: var(--otakus-color-text-heading);
  }

  &-switch-phone-overlay {
    .otakus-dropdown-menu {
      padding: 6px;
      border-radius: 12px;
    }

    .otakus-dropdown-menu-item {
      border-radius: 8px;
    }
  }

  &-switch-phone-wrapper-disabled {
    background: var(--otakus-color-bg-container-disabled);
    border: 1px solid var(--otakus-color-border);
  }

  &-switch-phone-item {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    justify-content: flex-start;
    font-size: 16px;
    font-weight: 500;
  }

  &-switch-phone-cid {
    width: 40px;
  }

  &-switch-phone-number {
  }
}
