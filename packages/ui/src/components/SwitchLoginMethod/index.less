@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-switch-method-wrapper {
    border-radius: var(--otakus-control-interactive-size);
    font-size: 14px;
    //box-shadow: 0 2px 1px 0 rgba(22, 22, 122, 0.03);
    color: var(--otakus-color-text-secondary);
    font-weight: 500;
    padding: 6px 12px;
    background: var(--otakus-color-bg-layout);
    margin: 12px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
    width: fit-content;

    &:hover {
      background: var(--otakus-color-bg-text-hover);
    }

    &:focus,
    &:focus-within {
      background: var(--otakus-color-bg-text-active);
    }
  }

  &-switch-method-text {
    margin-left: var(--otakus-padding-xxs);
  }

  &-switch-method-wrapper-mask {
    position: absolute;
    bottom: -121px;
    left: -48px;
    background: var(--otakus-color-fill-alter);
    box-shadow: 0 2px 1px 0
      var(--palette-alpha-on-light-slate-98, rgba(22, 22, 122, 0.03));
    width: 456px;
    border-radius: 0 0 24px 24px;
    padding: 12px 0;
    margin: unset;
    align-items: flex-end;
    height: 90px;
  }

  &-switch-method-top-mask {
    background: #fff;
    height: 40px;
    width: 456px;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 0 0 24px 24px;
    box-shadow: 0px 12px 12px 0px rgba(1, 2, 11, 0.01),
      0px 8px 8px 0px rgba(1, 2, 11, 0.01), 0px 2px 2px 0px rgba(1, 2, 11, 0.02),
      0px 1px 2px 0px rgba(1, 2, 11, 0.06);
  }

  &-switch-method-content-mask {
    width: 100%;
    text-align: center;
  }
}
