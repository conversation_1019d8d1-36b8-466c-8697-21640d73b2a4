import { prefixCls, useConfig, useLocale } from '@iam/login-shared';
import './index.less';
import { SwapOutlined } from '@otakus/icons';
import cls from 'classnames';

interface SwitchLoginMethodProps {
  handleChange: () => void;
}

const Index = (props: SwitchLoginMethodProps) => {
  const { handleChange } = props;
  const __ = useLocale();
  const { mask } = useConfig();
  return (
    <div
      className={cls(`${prefixCls}-switch-method-wrapper`, {
        [`${prefixCls}-switch-method-wrapper-mask`]: mask,
      })}
      onClick={handleChange}
    >
      {mask && <div className={`${prefixCls}-switch-method-top-mask`}></div>}
      <div
        className={cls({
          [`${prefixCls}-switch-method-content-mask`]: mask,
        })}
      >
        <SwapOutlined style={{ fontSize: '16px' }} />
        <span className={`${prefixCls}-switch-method-text`}>
          {__('switch:login:method')}
        </span>
      </div>
    </div>
  );
};

export default Index;
