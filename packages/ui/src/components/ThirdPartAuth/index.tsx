import {
  getIframe,
  goTripartiteAuth,
  IV3ThirdPartyAuthMethodConfigs,
  postChangeLocation,
  prefixCls,
  useLocale,
  useSize,
  useWave,
  ViewportSize,
} from '@iam/login-shared';
import { Button, Divider } from '@otakus/design';
import './index.less';
import cls from 'classnames';

interface IThirdPartAuthProps {
  thirdPartyAuthMethodConfigs: IV3ThirdPartyAuthMethodConfigs[];
}

const Index = (props: IThirdPartAuthProps) => {
  const { thirdPartyAuthMethodConfigs } = props;
  if (
    !thirdPartyAuthMethodConfigs ||
    thirdPartyAuthMethodConfigs.length === 0
  ) {
    return null;
  }
  const __ = useLocale();
  const { size } = useSize();
  const { getZtTicket } = useWave(!!thirdPartyAuthMethodConfigs);

  const _goTripartiteAuth = async (redirectUri: string) => {
    const ticket = await getZtTicket?.();

    const url = await goTripartiteAuth(redirectUri, ticket);
    const isIframe = getIframe();
    if (isIframe) {
      postChangeLocation(url);
    } else {
      window.location.href = url;
    }
  };

  return (
    <>
      <Divider
        plain
        className={cls(`${prefixCls}-third-wrapper`, {
          [`${prefixCls}-third-wrapper-xs`]: size === ViewportSize.xs,
        })}
      >
        {__('third:login"title')}
      </Divider>
      {thirdPartyAuthMethodConfigs.map((item) => {
        return (
          <Button
            key={item.auth_uri}
            className={`${prefixCls}-third-methods-btn`}
            onClick={() => _goTripartiteAuth(item?.auth_uri)}
            size={'large'}
          >
            {__('third:login:prefix') +
              item?.app_name +
              __('third:login:suffix')}
          </Button>
        );
      })}
    </>
  );
};

export default Index;
