import {
  getTLD,
  MIHOYO_DOMAIN,
  prefixCls,
  useCopyright,
  useScaleStyle,
} from '@iam/login-shared';
import './index.less';

const Index = () => {
  const { copyRightArr, copyRightSymbol } = useCopyright();
  const [pre, next] = copyRightArr;
  const scaleStyle = useScaleStyle();
  const tld = getTLD();
  const letterSpacing = tld === MIHOYO_DOMAIN ? '20px' : '16px';
  return (
    <div
      className={`${prefixCls}-copyright-wrapper`}
      style={{ ...scaleStyle, letterSpacing }}
    >
      <span className={`${prefixCls}-copyright-text`}>{pre}</span>
      <span className={`${prefixCls}-copyright-symbol`}>{copyRightSymbol}</span>
      <span className={`${prefixCls}-copyright-next`}>{next}</span>
    </div>
  );
};

export default Index;
