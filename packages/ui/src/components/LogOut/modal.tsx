import { useLayoutEffect } from 'react';
import {
  logoutFn,
  prefixCls,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Modal } from '@otakus/design';
import './index.less';

const Index = ({
  postLogoutRedirectUri,
}: {
  postLogoutRedirectUri: string;
}) => {
  const { env } = useConfig();
  const __ = useLocale();
  const { size } = useSize();
  const [modal, contextHolder] = Modal.useModal();
  useLayoutEffect(() => {
    Modal.destroyAll();
    modal.confirm({
      title: __('logout:title'),
      content: __('logout:content'),
      okText: __('logout:okText'),
      icon: null,
      cancelButtonProps: { style: { display: 'none' } },
      className: size === ViewportSize.xs ? `${prefixCls}-logout-modal-xs` : '',
      width: size === ViewportSize.xs ? 360 : '',
      onOk: async () => {
        await logoutFn(env);
        if (postLogoutRedirectUri) {
          window.location.replace(postLogoutRedirectUri);
        } else {
          modal.success({
            title: __('logout:result:title'),
            content: __('logout:result:content'),
            icon: null,
            okButtonProps: { style: { display: 'none' } },
            cancelButtonProps: { style: { display: 'none' } },
            className:
              size === ViewportSize.xs ? `${prefixCls}-logout-modal-xs` : '',
            width: size === ViewportSize.xs ? 360 : '',
          });
        }
      },
    });
  }, [postLogoutRedirectUri, env, __, modal]);
  return <>{contextHolder}</>;
};

export default Index;
