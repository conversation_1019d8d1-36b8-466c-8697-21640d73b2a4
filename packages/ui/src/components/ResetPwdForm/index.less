@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-reset-form-title {
    margin-bottom: 36px;
    padding-top: 24px;
  }

  &-reset-form-title-xs {
    margin-bottom: 24px;
  }

  &-reset-wrapper {
    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }

    .otakus-form-item {
      //margin-bottom: 12px;
    }
  }

  &-reset-submit {
    width: 100%;
    height: 54px;
    border-radius: 12px;
  }

  &-reset-success-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-reset-success-wrapper-icon {
    width: 100px;
    height: 100px;

    svg {
      width: 100px;
      height: 100px;
    }
  }

  &-reset-success-text {
    color: var(--otakus-color-text-label);
    margin-top: 8px;
    font-size: 14px;
  }

  &-passwd-passwd-input {
  }

  &-passwd-form-passwd-item-pattern-tool-tips {
    margin-bottom: 12px;
  }

  &-passwd-form-passwd-item-second-pattern-tool-tips {
    margin-bottom: 12px;
  }

  &-reset-success-wrapper-xs {
    margin-top: 72px;
  }
}
