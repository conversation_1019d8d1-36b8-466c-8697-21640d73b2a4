import {
  delFlowToken,
  detectPasswdApi,
  getKeyV3,
  IV3GetPattern,
  IV3GetPatternRes,
  message,
  MODIFY_PWD_REQUIRE_AUTH_CODE,
  MODIFY_PWD_REQUIRE_TOTP_CODE,
  prefixCls,
  reloadLocation,
  request,
  resetPasswdApi,
  rsa,
  useAssets,
  useConfig,
  useKeyPressSubmit,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import cls from 'classnames';
import Title from '@ui/components/Title';
import { useEffect, useMemo, useState } from 'react';
import { Button, Form } from '@otakus/design';
import { FormItem } from '@ui/components/Form/FormItem';
import './index.less';
import PatternToolTips from '@ui/components/PatternToolTips';
import Input from '@ui/components/Input';
import { ReactSVG } from 'react-svg';
import { useThrottleFn } from 'ahooks';
import Accordion from '@ui/components/Accordion';

const Index = () => {
  const { size } = useSize();
  const { authApi, clientId, lang } = useConfig();
  const [rules, setRules] = useState<IV3GetPattern[]>([]);
  const __ = useLocale();
  const [form] = Form.useForm();
  const values = Form.useWatch([], form);
  const [success, setSuccess] = useState<boolean>(false);
  const [hiddenPatternToolTips, setHiddenPatternToolTips] =
    useState<boolean>(false);
  const [visibleSecondPasswordPattern, setVisibleSecondPasswordPattern] =
    useState<boolean>(false);
  const ResetSuccessSvg = useAssets('reset-success.svg');

  const detectPasswd = async () => {
    const passwd = form.getFieldValue('passwd');
    let params = {
      password: passwd ? await rsaPassword(passwd) : '',
    };
    const data = await request<IV3GetPatternRes>(
      `${authApi}/${detectPasswdApi}`,
      { ...params },
    );
    if (data?.code === 0) {
      setRules(data?.data?.rules);
    }
    if (
      data?.code === MODIFY_PWD_REQUIRE_AUTH_CODE ||
      data?.code === MODIFY_PWD_REQUIRE_TOTP_CODE
    ) {
      setRules([]);
      setHiddenPatternToolTips(true);
      message.error(data?.message, 2, null, null, '', null, () => {
        reloadLocation();
      });
    }
  };

  const { run: getPattern } = useThrottleFn(detectPasswd, { wait: 500 });

  // 初始化请求规则
  useEffect(() => {
    detectPasswd().then();
  }, []);

  useEffect(() => {
    getPattern();
  }, [lang]);

  // 密码变更请求规则
  const handleValuesChange = (values: any) => {
    if (!values?.secondPassword && values?.secondPassword !== '') {
      getPattern();
    }
  };

  const rsaPassword = async (password: string) => {
    const authKey = await getKeyV3(authApi, clientId);
    const { public_key = '', hash = '' } = authKey || {};
    return rsa(public_key, password + hash);
  };

  const passwordSameResult = useMemo(() => {
    if (values) {
      const { passwd = '', secondPassword = '' } = values;
      form.setFields([
        {
          name: 'passwd',
          errors: [],
        },
        {
          name: 'secondPassword',
          errors: [],
        },
      ]);
      return {
        description: __('passwd:verify:different'),
        pass: passwd === secondPassword,
      };
    }
    return {
      description: '',
      pass: false,
    };
  }, [values, __]);

  const handleVisibleSecondPasswordPattern = (visible: boolean) => {
    if (!values?.passwd) {
      return;
    }
    setVisibleSecondPasswordPattern(visible);
  };

  const submitDisabled = useMemo(() => {
    if (values) {
      const { passwd = '', secondPassword = '' } = values;
      const allPass = rules?.every((e) => e.pass);
      if (passwd === secondPassword && allPass && passwd && secondPassword) {
        return true;
      }
    }
    return false;
  }, [values, rules]);

  useKeyPressSubmit(
    () => {
      if (submitDisabled) {
        submit().then();
      }
      return;
    },
    {
      target: document.querySelector('#reset'),
    },
  );

  const submit = async () => {
    form?.validateFields().then(async (val: any) => {
      if (!submitDisabled) {
        return;
      }
      const password = val?.passwd;
      const _password = await rsaPassword(password);
      const data = await request(`${authApi}/${resetPasswdApi}`, {
        password: _password,
      });
      if (data?.code === 0) {
        setSuccess(true);
      } else if (
        data?.code === MODIFY_PWD_REQUIRE_AUTH_CODE ||
        data?.code === MODIFY_PWD_REQUIRE_TOTP_CODE
      ) {
        message.error(data?.message, 2, null, null, '', null, () => {
          reloadLocation();
        });
      } else {
        message.error(data?.message);
      }
    });
  };

  const handleClear = () => {
    form.setFields([{ name: 'passwd', value: '', errors: [] }]);
  };

  const handleClearSecondPassword = () => {
    form.setFields([{ name: 'secondPassword', value: '', errors: [] }]);
  };

  const handleVisiblePasswordPattern = (visible: boolean) => {
    const allPass = rules?.every((e) => e.pass);
    if (!visible && allPass) {
      setHiddenPatternToolTips(true);
    } else {
      setHiddenPatternToolTips(false);
    }
  };

  // 修改成功后清楚当前的 flowToken
  useEffect(() => {
    if (success) {
      delFlowToken();
    }
  }, [success]);

  if (success) {
    return (
      <div
        className={cls(`${prefixCls}-reset-success-wrapper`, {
          [`${prefixCls}-reset-success-wrapper-xs`]: size === ViewportSize.xs,
        })}
      >
        <ReactSVG
          src={ResetSuccessSvg}
          className={`${prefixCls}-reset-success-wrapper-icon`}
        />
        <span className={`${prefixCls}-reset-success-text`}>
          {__('passwd:reset:success')}
        </span>
      </div>
    );
  }

  return (
    <div className={`${prefixCls}-reset-wrapper`}>
      <div
        className={cls(`${prefixCls}-reset-form-title`, {
          [`${prefixCls}-reset-form-title`]: size === ViewportSize.xs,
        })}
      >
        {<Title title={__('reset:form:title')} />}
      </div>
      <Form form={form} id={'reset'} onValuesChange={handleValuesChange}>
        <FormItem
          name={'passwd'}
          className={cls({
            [`${prefixCls}-passwd-form-passwd-item-pattern-tool-tips`]:
              !hiddenPatternToolTips,
          })}
          wrap
        >
          <Input
            className={`${prefixCls}-passwd-passwd-input`}
            passwd
            normal
            autoFocus={size !== ViewportSize.xs}
            placeholder={__('reset:form:first:passwd:input:placeholder')}
            autoComplete={'new-password'}
            onClear={handleClear}
            onFocus={() => handleVisiblePasswordPattern(true)}
            onBlur={() => handleVisiblePasswordPattern(false)}
          />
        </FormItem>
        <Accordion
          open={!hiddenPatternToolTips}
          height={lang === 'zh-CN' ? 146 : 252}
        >
          <PatternToolTips
            pattern={rules}
            extraDescription={__('passwd:verify:tips')}
          />
        </Accordion>

        <FormItem
          name={'secondPassword'}
          className={cls({
            [`${prefixCls}-passwd-form-passwd-item-second-pattern-tool-tips`]:
              visibleSecondPasswordPattern,
          })}
        >
          <Input
            passwd
            normal
            onClear={handleClearSecondPassword}
            placeholder={__('reset:form:second:passwd:input:placeholder')}
            onFocus={() => handleVisibleSecondPasswordPattern(true)}
            onBlur={() => handleVisibleSecondPasswordPattern(false)}
            autoComplete={'new-password'}
          />
        </FormItem>
        <Accordion open={visibleSecondPasswordPattern} height={50}>
          <PatternToolTips pattern={[passwordSameResult]} />
        </Accordion>
        <Button
          className={`${prefixCls}-reset-submit`}
          type={'primary'}
          disabled={!submitDisabled}
          onClick={submit}
        >
          {__('reset:form:submit')}
        </Button>
      </Form>
    </div>
  );
};

export default Index;
