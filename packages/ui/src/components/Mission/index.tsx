import { useEffect, useRef, useState } from 'react';
import './index.less';
import missionSvg from '@ui/assets/mission.svg';
import { ReactSVG } from 'react-svg';
import { prefixCls, useScaleStyle } from '@iam/login-shared';
import { useHover } from 'ahooks';

const AnimatedText = () => {
  const firstLine = 'TECH OTAKUS'.split('');
  const secondLine = 'SAVE THE WORLD'.split('');
  const totalLength = firstLine.length + secondLine.length;
  const [states, setStates] = useState([]);
  const ref = useRef(null);
  const isHovering = useHover(ref);
  const timeouts = [];
  const scaleStyle = useScaleStyle('left top');

  // 定义动画函数
  const textAnimation = () => {
    let stateArray = [];
    for (let i = 0; i < totalLength; i++) {
      stateArray.push(i);
    }
    const shuffled = shuffle(stateArray);

    shuffled.forEach((index: number) => {
      const stateTime = Math.round(Math.random() * 2000);
      const id = setTimeout(() => {
        firstStages(index);
      }, stateTime);
      timeouts.push(id);
    });
  };

  const firstStages = (index: number) => {
    setStates((prevStates) => {
      const newStates = [...prevStates];
      if (newStates[index] === 'state-2') {
        newStates[index] = 'state-3';
      } else if (newStates[index] === 'state-1') {
        newStates[index] = 'state-2';
      } else {
        newStates[index] = 'state-1';
        const id = setTimeout(() => secondStages(index), 200);
        timeouts.push(id);
      }
      return newStates;
    });
  };

  const secondStages = (index: number) => {
    setStates((prevStates) => {
      const newStates = [...prevStates];
      if (newStates[index] === 'state-1') {
        newStates[index] = 'state-2';
        const id = setTimeout(() => thirdStages(index), 200);
      } else {
        newStates[index] = 'state-1';
      }
      return newStates;
    });
  };

  const thirdStages = (index: number) => {
    setStates((prevStates) => {
      const newStates = [...prevStates];
      if (newStates[index] === 'state-2') {
        newStates[index] = 'state-3';
      }
      return newStates;
    });
  };

  // 洗牌函数
  const shuffle = (array: number[]) => {
    let currentIndex = array.length,
      temporaryValue: number,
      randomIndex: number;
    while (0 !== currentIndex) {
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex -= 1;

      temporaryValue = array[currentIndex];
      array[currentIndex] = array[randomIndex];
      array[randomIndex] = temporaryValue;
    }
    return array;
  };

  useEffect(() => {
    if (isHovering) {
      textAnimation();
    } else {
      timeouts.forEach((id) => clearTimeout(id));
      setStates(new Array(totalLength).fill(''));
    }

    return () => timeouts.forEach((id) => clearTimeout(id));
  }, [totalLength, isHovering]);

  return (
    <div
      className={`${prefixCls}-mission-wrapper`}
      ref={ref}
      // style={scaleStyle}
    >
      {/* 第一行 */}
      <div>
        {firstLine.map((char, index) => (
          <div key={index} className={`alphabet ${states[index]}`}>
            {char === ' ' ? '\u00A0' : char}
          </div>
        ))}
      </div>
      {/* 第二行：SVG 和 "SAVE THE WORLD" */}
      <div className={`${prefixCls}-mission-bottom`}>
        {/* SVG 插入位置 */}
        <div className={`${prefixCls}-mission-svg`}>
          <ReactSVG src={missionSvg} />
        </div>
        {secondLine.map((char, index) => (
          <div
            key={index + firstLine.length}
            className={`alphabet ${states[index + firstLine.length]}`}
          >
            {char === ' ' ? '\u00A0' : char}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnimatedText;
