@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-mission-wrapper {
    display: flex;
    flex-direction: column;
    margin-left: 26px;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 100%; /* 10px */
    letter-spacing: 2px;
    text-transform: uppercase;
    color: var(--otakus-color-text-description);
  }

  &-mission-line {
    margin-right: 4px;
  }

  &-mission-bottom {
    display: flex;
    margin-top: 6px;
  }

  &-mission-svg {
    margin-right: 4px;
  }
}

/* 定义颜色变量 */
:root {
  --color-blue-1: #3c47d5;
  --color-blue-2: #a3b7fe;
  --color-black-1: #959595;
}

.line {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px 0;
}

.alphabet {
  margin: 0;
  display: inline-block;
  transition: color 0.2s;
}

.alphabet.state-1 {
  color: var(--color-blue-1);
}

.alphabet.state-2 {
  color: var(--color-blue-2);
}

.alphabet.state-3 {
  color: var(--color-black-1);
}
