import { Button, Tooltip } from '@otakus/design';
import { prefixCls, useLocale, useSize, ViewportSize } from '@iam/login-shared';
import { LeftOutlined } from '@otakus/icons';
import React, { useMemo } from 'react';
import cls from 'classnames';
import './index.less';

interface BackButtonProps {
  handleBack: () => void;
}

const Index = (props: BackButtonProps) => {
  const { handleBack } = props;
  const __ = useLocale();
  const { size } = useSize();
  const title = useMemo(() => {
    if (size === ViewportSize.xs) {
      return '';
    }
    return __('back:btn:text');
  }, [size, __]);

  return (
    <Tooltip title={title} placement={'top'} trigger={['hover']}>
      <Button
        className={cls(`${prefixCls}-back-btn`, {
          [`${prefixCls}-back-btn-xs`]: size === ViewportSize.xs,
        })}
        icon={<LeftOutlined style={{ color: 'inherit' }} />}
        onClick={handleBack}
      ></Button>
    </Tooltip>
  );
};

export default Index;
