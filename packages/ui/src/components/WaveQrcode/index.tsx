import {
  authnV3WaveQrVerifyApi,
  IV3AuthRes,
  prefixCls,
  request,
  subscribe,
  useConfig,
  useWaveScan,
} from '@iam/login-shared';
import { useCallback } from 'react';
import './index.less';

interface WaveQrcodeProps {
  app_id?: string;
  documentId: string;
  type: 'default';
  cb: (mfaCode?: string) => void;
  extraProps?: Record<string, unknown>;
}

// const STYLE =
//   'data:text/css;base64,I2FwcHtiYWNrZ3JvdW5kLWNvbG9yOiB1bnNldDt9LnFyY29kZS1sb2dpbntoZWlnaHQ6IHVuc2V0ICFpbXBvcnRhbnQ7fQ==';

const STYLE = 'data:text/css;base64,I2FwcHtiYWNrZ3JvdW5kLWNvbG9yOiB1bnNldDt9';

const Index = (props: WaveQrcodeProps) => {
  const { app_id, documentId, type, cb, extraProps = {} } = props;
  const { authApi } = useConfig();
  const callBack = useCallback(
    async (verify_code: string) => {
      let rememberMe = false;
      subscribe((val) => {
        rememberMe = val.rememberMe;
      }, 'login-form-remember-checkbox');
      const data = await request<IV3AuthRes>(
        `${authApi}/${authnV3WaveQrVerifyApi}`,
        {
          ...extraProps,
          verify_code,
          remember_me: rememberMe,
        },
      );
      if (data?.code === 0) {
        cb?.(data?.data?.ticket);
      }
    },
    [extraProps],
  );

  useWaveScan(documentId, app_id, callBack, 330, 270, STYLE);

  return (
    <div>
      <div className={`${prefixCls}-wave-qrcode-wrapper`}>
        <div
          className={`${prefixCls}-wave-qrcode-content`}
          id={documentId}
        ></div>
      </div>
    </div>
  );
};

export default Index;
