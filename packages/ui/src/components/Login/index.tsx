import Wrapper from '@ui/components/Wrapper';
import Block from '@ui/components/Block';
import Mask from '@ui/components/Mask';
import { useConfig, useSize, ViewportSize } from '@iam/login-shared';

interface LoginComponentProps {
  cb: () => void;
}

const Index = (props: LoginComponentProps) => {
  const { size } = useSize();
  const { mask } = useConfig();
  if (mask && size !== ViewportSize.xs) {
    return (
      <div>
        <Mask open={true}>
          <Block {...props} />
        </Mask>
      </div>
    );
  }
  return (
    <Wrapper>
      <Block {...props} />
    </Wrapper>
  );
};

export default Index;
