import { FC, useEffect, useRef, useState } from 'react';
import { animated, useSpring } from '@react-spring/web';

interface AccordionProps {
  open: boolean;
  height: number;
}

const Accordion: FC<AccordionProps> = (props) => {
  const { open, children, height } = props;
  const [isHeightAnimation, setIsHeightAnimation] = useState(false);
  const [isFirstRender, setIsFirstRender] = useState(true); // 标记初次渲染

  const contentRef = useRef(null);
  const contentHeight = contentRef.current
    ? contentRef.current.scrollHeight
    : 0;

  useEffect(() => {
    // 在首次渲染完成后将 isFirstRender 设为 false，启用后续动画
    setIsFirstRender(false);
  }, []);

  const opacityStyles = useSpring({
    opacity: open ? 1 : 0,
    immediate: isFirstRender,
    onRest: () => {
      // 当透明度动画完成后再开始高度动画
      if (!open) {
        setIsHeightAnimation(true);
      }
    },
    config: { duration: 400 },
  });

  const heightStyles = useSpring({
    height: open ? height : 0,
    overflow: 'hidden',
    immediate: isFirstRender, // 首次渲染时立即设置，无动画
    config: { duration: 400 },
  });

  return (
    <div>
      <animated.div style={{ ...heightStyles, ...opacityStyles }}>
        <div ref={contentRef} style={{ minHeight: height }}>
          {children}
        </div>
      </animated.div>
    </div>
  );
};

export default Accordion;
