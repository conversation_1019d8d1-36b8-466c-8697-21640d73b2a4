import { useEffect, useRef, useState } from 'react';
import { MailInputState, UseMailInputParams } from './types';
import { IV3PerCheckParams, message, useLocale } from '@iam/login-shared';
import { useDebounceFn } from 'ahooks';

export function useMailInput({
  onCancelCaptcha,
  captcha,
  interval = 60,
  onSendCode,
  value,
  onPrepareCaptcha,
}: UseMailInputParams) {
  const [countdown, setCountdown] = useState(interval);
  const [state, stateMailInputState] = useState(MailInputState.Initial);
  const timer = useRef<ReturnType<typeof setTimeout>>();
  const __ = useLocale();
  /**
   * 发送次数标记
   */
  const count = useRef(0);

  /**
   * 准备captcha验证
   */
  const prepareCaptchaInternal = useDebounceFn(
    () => {
      // 如果需要captcha验证，唤起
      if (captcha) {
        stateMailInputState(MailInputState.WaitForCaptcha);
        onPrepareCaptcha?.()
          .then((params) => {
            requestForCode(params);
          })
          .catch(() => {
            reinitialize();
          });
      } else {
        // 否则直接发送请求
        requestForCode();
      }
    },
    { leading: true, trailing: false, wait: 300 },
  );

  /**
   *
   */
  function cancelCaptcha() {
    onCancelCaptcha?.();
    stateMailInputState(MailInputState.Initial);
  }

  function startTimer() {
    // if (state !== MailInputState.Initial) return;
    count.current += 1;

    function queueNext() {
      return setTimeout(() => {
        setCountdown((prev) => {
          let next = prev - 1;
          if (next >= 1) {
            timer.current = queueNext();
            return next;
          } else {
            reinitialize();
          }
        });
      }, 1000);
    }

    stateMailInputState(MailInputState.CodeSend);
    queueNext();
  }

  function reinitialize() {
    stateMailInputState(MailInputState.Initial);
    setCountdown(interval);
    endTimer();
  }

  function endTimer() {
    clearTimeout(timer.current);
  }

  /**
   * 请求发送验证码
   */
  function requestForCode(captcha?: IV3PerCheckParams) {
    stateMailInputState(MailInputState.RequestCode);
    onSendCode?.(captcha, { email: (value as string).trim?.() })
      .then(() => {
        message.success(__('sms:success'));
        startTimer();
      })
      .catch((e) => {
        console.log(e);
        reinitialize();
      });
  }

  useEffect(() => {
    return () => {
      endTimer();
    };
  }, []);

  return {
    countdown,
    count,
    state,
    prepareCaptcha: prepareCaptchaInternal.run as () => void,
    cancelCaptcha,
    startTimer,
  };
}
