import { IV3PerCheckParams } from '@iam/login-shared';
import { InputProps, InputRef } from '@otakus/design';
import { RegionNumberSelectProps } from '@ui/components/Select/RegionNumber/types';
import { RefObject } from 'react';

export interface MailInputProps extends InputProps {}

export enum MailInputState {
  Initial,
  Pending,
  RequestCode,
  CodeSend,
  WaitForCaptcha,
}

export interface MailInputProps extends InputProps {
  /**
   * 验证码发送间隔
   */
  interval?: number;

  /**
   * TODO: use this api
   */
  locales?: {
    getCode: string;
    RetryCountdown: string;
  };

  onPrepareCaptcha?(): Promise<IV3PerCheckParams>;

  onCancelCaptcha?(): void;

  /**
   * 是否有captcha步骤
   */
  captcha?: boolean;

  /**
   * 获取验证码请求
   */
  onSendCode?: (
    captcha?: IV3PerCheckParams,
    data?: { email?: string },
  ) => Promise<void>;

  /**
   * 暂时不需要使用
   */
  selectProps?: RegionNumberSelectProps;
  emailInputRef?: RefObject<InputRef>;
}

export interface UseMailInputParams extends MailInputProps {}
