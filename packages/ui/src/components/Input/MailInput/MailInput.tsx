import Inputmask from 'inputmask/lib/inputmask.js';
import './MailInput.less';

import { Button, Form } from '@otakus/design';
import { MailInputProps, MailInputState } from './types';
import { useEffect, useMemo, useRef } from 'react';
import classNames from 'classnames';
import { useMailInput } from './useMailInput';
import { useLocale } from '@iam/login-shared';
import Input from '../index';
import { ConfigProvider } from 'antd';

let idx = 0;

/**
 * 存在各种兼容性问题，不一定使用
 */
export function MailInput(props: MailInputProps) {
  const {
    value,
    className,
    captcha,
    onSendCode,
    onPrepareCaptcha,
    onCancelCaptcha,
    emailInputRef,
    ...rest
  } = props;
  const idRef = useRef<number>(idx++);
  const locale = useLocale();
  const maskRef = useRef<Inputmask.Instance>(null);
  const { errors } = Form.Item.useStatus();

  const { count, state, countdown, prepareCaptcha } = useMailInput({
    ...props,
    value,
  });

  const maskClassName = useMemo(
    () => 'mail-mask-input-' + idRef.current,
    [idRef.current],
  );

  const componentDisabled = ConfigProvider.useConfig().componentDisabled;

  useEffect(() => {
    // maskRef.current = ColorMask('*{0,}@(mihoyo.com|cognosphere.com)', {
    //   insertMode: false,
    //   insertModeVisual: false,
    //   // greedy: false,
    //   // jitMasking: true,
    //   // keepStatic: true,
    //   showMaskOnFocus: true,
    //   showMaskOnHover: false,
    //   // tabThrough: true,
    //   definitions: {
    //     '*': {
    //       validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~-]",
    //     },
    //   },
    // });
    // // TODO: 支持无addon设置
    // maskRef.current.mask(`.${maskClassName} input`);
  }, []);

  useEffect(() => {
    // if ((value as string)?.indexOf?.('@m') > -1) {
    //   maskRef?.current.setValue('*{0,}@mihoyo.com');
    // } else if ((value as string)?.indexOf?.('@c') > -1) {
    //   maskRef?.current.setValue('*{0,}@cognosphere.com');
    // } else {
    //   // maskRef?.current.remove();
    // }
  }, [value]);

  const addonAfter = (() => {
    if (
      state === MailInputState.Initial ||
      state === MailInputState.WaitForCaptcha ||
      state === MailInputState.RequestCode
    ) {
      return (
        <Button
          type="link"
          onClick={prepareCaptcha}
          size="small"
          style={{ padding: 0 }}
          disabled={
            !value ||
            errors.length > 0 ||
            componentDisabled ||
            state === MailInputState.RequestCode
          }
        >
          {locale(count.current > 0 ? 'email:code:resend' : 'email:submit')}
        </Button>
      );
    } else if (state === MailInputState.CodeSend) {
      return (
        <Button type="link" disabled style={{ padding: 0, width: '72px' }}>
          {countdown + locale('sms:delay')}
        </Button>
      );
    }
  })();

  return (
    <Input
      {...rest}
      placeholder={locale('email:placeholder')}
      value={value}
      ref={emailInputRef}
      suffix={addonAfter}
      allowClear
      className={classNames(maskClassName, className)}
    />
  );
}
