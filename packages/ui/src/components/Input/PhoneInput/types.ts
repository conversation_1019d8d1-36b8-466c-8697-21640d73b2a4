import { IV3PerCheckParams } from '@iam/login-shared';
import { InputProps, InputRef } from '@otakus/design';
import { RegionNumberSelectProps } from '@ui/components/Select/RegionNumber/types';
import { RefObject } from 'react';

export enum PhoneInputState {
  Initial,
  Pending,
  RequestCode,
  CodeSend,
  WaitForCaptcha,
}

export interface PhoneInputProps extends InputProps {
  /**
   * 验证码发送间隔
   */
  interval?: number;

  locales?: {
    getCode: string;
    RetryCountdown: string;
  };

  onPrepareCaptcha?(): Promise<IV3PerCheckParams>;

  onCancelCaptcha?(): void;

  /**
   * 是否有captcha步骤
   */
  captcha?: boolean;

  /**
   * 获取验证码请求
   */
  onSendCode?: (
    captcha?: IV3PerCheckParams,
    data?: { phone: string; cid: string },
  ) => Promise<void | false>;

  selectProps?: RegionNumberSelectProps;
  phoneInputRef?: RefObject<InputRef>;
}

export interface UsePhoneInputParams extends PhoneInputProps {}
