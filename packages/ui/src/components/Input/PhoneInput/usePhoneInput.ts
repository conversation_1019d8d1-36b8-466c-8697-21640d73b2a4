import { useEffect, useMemo, useRef, useState } from 'react';
import { PhoneInputState, UsePhoneInputParams } from './types';
import { IV3PerCheckParams, message, useLocale } from '@iam/login-shared';
import { parseCidFromValue } from '@ui/components/Select/RegionNumber/utils';
import { useDebounceFn } from 'ahooks';

export function usePhoneInput({
  onCancelCaptcha,
  captcha,
  interval = 60,
  onSendCode,
  value,
  selectProps,
  onPrepareCaptcha,
}: UsePhoneInputParams) {
  const [countdown, setCountdown] = useState(interval);
  const [state, statePhoneInputState] = useState(PhoneInputState.Initial);
  const timer = useRef<ReturnType<typeof setTimeout>>();
  const locale = useLocale();
  /**
   * 发送次数标记
   */
  const count = useRef(0);

  /**
   * 区号
   */
  const cid = useMemo(
    () =>
      selectProps?.value
        ? parseCidFromValue(selectProps.value)
        : // ? selectProps.value.split('|')?.[0]
          selectProps?.value,
    [selectProps.value],
  );

  /**
   * 准备captcha验证
   */
  const prepareCaptchaInternal = useDebounceFn(
    () => {
      // 如果需要captcha验证，唤起
      if (captcha) {
        statePhoneInputState(PhoneInputState.WaitForCaptcha);
        onPrepareCaptcha?.()
          .then((params) => {
            requestForCode(params);
          })
          .catch(() => {
            /**
             * 1. 手机号客户端验证错误
             * 2. 接口其他错误
             */
            reinitialize();
          });
      } else {
        // 否则直接发送请求
        requestForCode();
      }
    },
    { leading: true, trailing: false, wait: 300 },
  );

  /**
   *
   */
  function cancelCaptcha() {
    onCancelCaptcha?.();
    statePhoneInputState(PhoneInputState.Initial);
  }

  function startTimer() {
    // if (state !== PhoneInputState.Initial) return;
    count.current += 1;

    function queueNext() {
      return setTimeout(() => {
        setCountdown((prev) => {
          let next = prev - 1;
          if (next >= 1) {
            timer.current = queueNext();
            return next;
          } else {
            reinitialize();
          }
        });
      }, 1000);
    }

    statePhoneInputState(PhoneInputState.CodeSend);
    queueNext();
  }

  function reinitialize() {
    statePhoneInputState(PhoneInputState.Initial);
    setCountdown(interval);
    endTimer();
  }

  function endTimer() {
    clearTimeout(timer.current);
  }

  /**
   * 请求发送验证码
   */
  function requestForCode(captcha?: IV3PerCheckParams) {
    statePhoneInputState(PhoneInputState.RequestCode);
    onSendCode?.(captcha, { phone: (value as string).trim?.(), cid: cid })
      .then(() => {
        message.success(locale('sms:success'));
        startTimer();
      })
      .catch((e) => {
        console.log(e);
        reinitialize();
      });
  }

  useEffect(() => {
    return () => {
      endTimer();
    };
  }, []);

  return {
    count,
    countdown,
    state,
    prepareCaptcha: prepareCaptchaInternal.run as () => void,
    cancelCaptcha,
    // phone,
    startTimer,
    cid,
  };
}
