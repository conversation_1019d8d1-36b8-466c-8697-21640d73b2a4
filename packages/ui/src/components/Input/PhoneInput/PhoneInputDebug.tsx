import { PhoneInput } from './PhoneInput';
import { PhoneInputProps } from './types';

export function PhoneInputDebug({ ...rest }: PhoneInputProps) {
  return (
    <div>
      <PhoneInput
        {...rest}
        selectProps={{
          // popupMatchSelectWidth: false,
          // 根据实际情况设置
          getPopupContainer: (e) =>
            e.parentElement.parentElement.parentElement.parentElement,
          dropdownAlign: { offset: [-11, 16] },
          // dropdownStyle: {}
        }}
        onSendCode={() => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve();
            }, 100);
          });
        }}
      />
    </div>
  );
}
