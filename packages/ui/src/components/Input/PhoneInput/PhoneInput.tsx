import { PhoneInputProps, PhoneInputState } from './types';
import { Button, Form, InputProps } from '@otakus/design';
import { useMemo, useState } from 'react';
import { usePhoneInput } from './usePhoneInput';
import { prefixCls, useLocale } from '@iam/login-shared';
import { RegionNumberSelect } from '@ui/components/Select/RegionNumber/RegionNumberSelect';
import Input from '../index';
import { ConfigProvider } from 'antd';

/**
 * 手机号输入
 */

export const PhoneInput = (props: PhoneInputProps) => {
  const {
    onPrepareCaptcha,
    onCancelCaptcha,
    interval,
    captcha,
    onSendCode,
    onChange: onChangeProp,
    value: valueProp,
    selectProps,
    phoneInputRef,
    ...rest
  } = props;
  const { errors } = Form.Item.useStatus();
  const [valueInternal, setValueInternal] = useState('');
  const locale = useLocale();

  const value = useMemo(() => {
    return onChangeProp ? valueProp : valueInternal;
  }, [valueProp, onChangeProp, valueInternal]);

  const { state, count, countdown, prepareCaptcha } = usePhoneInput({
    ...props,
    value,
  });

  const onChange: InputProps['onChange'] = (e) => {
    onChangeProp ? onChangeProp(e) : setValueInternal(e.target.value);
  };

  const componentDisabled = ConfigProvider.useConfig().componentDisabled;

  const addonAfter = (() => {
    if (
      state === PhoneInputState.Initial ||
      state === PhoneInputState.WaitForCaptcha ||
      state === PhoneInputState.RequestCode
    ) {
      return (
        <Button
          type="link"
          onClick={prepareCaptcha}
          style={{ padding: 0 }}
          size="small"
          disabled={
            !value ||
            errors.length > 0 ||
            componentDisabled ||
            state === PhoneInputState.RequestCode
          }
        >
          {locale(count.current > 0 ? 'sms:code:resend' : 'sms:submit')}
        </Button>
      );
    } else if (state === PhoneInputState.CodeSend) {
      return (
        <Button type="link" disabled style={{ padding: 0, width: '72px' }}>
          {countdown + locale('sms:delay')}
        </Button>
      );
    }
  })();

  return (
    <div
      style={{ position: 'relative' }}
      className={prefixCls + '-phone-input'}
    >
      <Input
        ref={phoneInputRef}
        onChange={onChange}
        prefix={<RegionNumberSelect {...selectProps} />}
        type={'tel'}
        value={value}
        allowClear
        {...rest}
        suffix={addonAfter}
      />
    </div>
  );
};
