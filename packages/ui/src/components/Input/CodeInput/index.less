@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-input {
    .otakus-otp {
      column-gap: 0;
    }

    input::-webkit-input-placeholder {
      color: var(--otakus-color-fill-tertiary);
      font-size: 12px;
    }

    input:-moz-placeholder {
      color: var(--otakus-color-fill-tertiary);
      font-size: 12px;
    }

    input::-moz-placeholder {
      color: var(--otakus-color-fill-tertiary);
      font-size: 12px;
    }

    input:-ms-input-placeholder {
      color: var(--otakus-color-fill-tertiary);
      font-size: 12px;
    }

    // api 可能会变
    border: solid 1px var(--otakus-color-border);
    background: var(--otakus-color-bg-container);

    &--error:not(&--disabled) {
      background: var(--otakus-color-bg-container);
      border-width: var(--otakus-line-width);
      border-style: var(--otakus-line-type);
      border-color: var(--otakus-color-error);

      input {
        caret-color: var(--otakus-color-error);
      }
    }

    &--error:not(&--disabled):hover {
      border-color: var(--otakus-color-error-border-hover);
      background-color: var(--otakus-input-hover-bg);
    }

    &--error:not(&--disabled):focus,
    &--error:not(&--disabled):focus-within {
      border-color: var(--otakus-color-error);
      box-shadow: var(--otakus-input-error-active-shadow);
      outline: 0;
      background-color: var(--otakus-input-active-bg);
    }

    &:focus,
    &:focus-within {
      border-color: var(--otakus-input-active-border-color);
      box-shadow: var(--otakus-input-active-shadow);
      //   outline: 0;
      background-color: var(--otakus-input-active-bg);
    }

    &:hover {
      border-color: var(--otakus-input-hover-border-color);
      background-color: var(--otakus-input-hover-bg);
    }

    &[disabled] {
      color: var(--otakus-color-text-disabled);
      background-color: var(--otakus-color-bg-container-disabled);
      border-color: var(--otakus-color-border);
      box-shadow: none;
      cursor: not-allowed;
      opacity: 1;

      input {
        cursor: not-allowed;
      }
    }

    border-radius: var(--otakus-border-radius);

    > div {
      display: flex;
      justify-content: space-between;
      height: 100%;
      padding: 0 12px;
    }

    input {
      border: none;
      height: 100%;
      //   line-height: var(--otakus-control-height-lg, 40px);
      box-shadow: none;
      outline: none;
    }
  }
}
