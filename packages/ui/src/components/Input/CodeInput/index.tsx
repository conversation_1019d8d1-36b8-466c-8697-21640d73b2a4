import { Form, Input } from '@otakus/design';
import { CodeInputProps } from './types';
import { prefixCls } from '@iam/login-shared';
import classNames from 'classnames';
import './index.less';
import { useEffect, useRef } from 'react';
import { ConfigProvider } from 'antd';

/**
 * TODO: 调整样式
 */
export const CodeInput = (props: CodeInputProps) => {
  const {
    className,
    onChange,
    length = 6,
    autoFocus,
    status,
    onCharChange,
    disabled: disabledProp,
    codeInputRef,
    ...rest
  } = props;
  const formContextStatus = Form.Item.useStatus();
  const innerRef = useRef<HTMLDivElement>(null);
  const disabled = disabledProp ?? ConfigProvider.useConfig().componentDisabled;
  const once = useRef(false);
  const inputsRefs = useRef<string[]>([]);

  const form = Form.useFormInstance();

  // @ts-ignore
  const code = Form.useWatch(rest?.name, form);

  useEffect(() => {
    const inputs = innerRef.current.querySelectorAll('input');
    if (!code) {
      for (let [idx, input] of inputs.entries()) {
        if (!once.current) {
          input.disabled = idx !== 0;
          inputsRefs.current = [];
        }
      }
    }
  }, [code]);

  function onChangeInternal(value: string) {
    onChange?.(value);
  }

  useEffect(() => {
    function onFocus(event: FocusEvent) {
      let target = event.target as HTMLElement;
      if (target.tagName === 'INPUT') {
        (target as HTMLInputElement).placeholder = '';
      }
    }

    function onBlur(event: FocusEvent) {
      let target = event.target as HTMLElement;
      if (target.tagName === 'INPUT') {
        (target as HTMLInputElement).placeholder = '●';
      }
    }

    innerRef.current?.addEventListener?.('focusin', onFocus);
    innerRef.current?.addEventListener?.('focusout', onBlur);

    return () => {
      innerRef.current?.removeEventListener?.('focusin', onFocus);
      innerRef.current?.removeEventListener?.('focusout', onBlur);
    };
  }, []);

  useEffect(() => {
    const inputs = innerRef.current.querySelectorAll('input');

    function onCharChangeInternal(
      input: HTMLInputElement,
      _: Event,
      idx: number,
    ) {
      if (/[0-9]/.test(input.value)) {
        onCharChange();
        inputsRefs.current[idx] = input.value;
      } else if (input.value === '') {
        inputsRefs.current.splice(idx, 1);
      } else {
        Promise.resolve().then(() => {
          input.focus();
        });
      }
    }

    function onInputInternal(input: HTMLInputElement, _: Event, idx: number) {
      if (input.value.trim() !== '' && idx < inputs.length - 1) {
        inputs[idx + 1].disabled = false;
      } else if (input.value.trim() === '') {
        // 如果当前输入框的值被清空，下一个输入框应再次被禁用
        for (let i = idx + 1; i < inputs.length; i++) {
          inputs[i].disabled = true;
          inputs[i].value = ''; // 清空后面输入框的值
        }
      }
    }

    function onDeleteInternal(input: HTMLInputElement, _: Event, idx: number) {
      if (input.value === '') {
        inputsRefs.current.splice(idx - 1, 1);
        form.setFieldsValue({
          // @ts-ignore
          [`${rest?.name}`]: inputsRefs.current.join(''),
        });
        if (idx <= 1) {
          inputs[0].focus();
        }
      }
    }

    if (inputs.length > 0) {
      for (let [idx, input] of inputs.entries()) {
        if (!once.current) {
          input.placeholder = '●';
          input.type = 'tel';
          // TODO: 兼容
          // chrome 上可用此禁止auto fill
          // @ts-ignore
          input.autocomplete = 'one-time-code';
          input.disabled = idx !== 0;
          // input.onclick = handleWrapperClick;
          input.style.pointerEvents = 'none';
        }

        input.addEventListener('change', (e) => {
          onCharChangeInternal(input, e, idx);
        });
        input.addEventListener('input', (e) => {
          onInputInternal(input, e, idx);
        });

        input.addEventListener('keydown', (e) => {
          if (e.key === 'Backspace' || e.key === 'Delete') {
            onDeleteInternal(input, e, idx);
          }
        });
      }
      if (autoFocus) {
        inputs[0].focus();
      }
    }

    return () => {
      for (let [idx, input] of inputs.entries()) {
        input.removeEventListener('change', (e) => {
          onCharChangeInternal(input, e, idx);
        });
        input.removeEventListener('input', (e) => {
          onInputInternal(input, e, idx);
        });
        input.removeEventListener('keydown', (e) => {
          if (e.key === 'Backspace' || e.key === 'Delete') {
            onDeleteInternal(input, e, idx);
          }
        });
        input.removeEventListener('keydown', (e) => {
          if (e.key === 'Backspace' || e.key === 'Delete') {
            onDeleteInternal(input, e, idx);
          }
        });
      }
    };
  }, []);

  const handleWrapperClick = () => {
    const inputs = innerRef.current.querySelectorAll('input');
    const len = inputsRefs.current.length;
    if (len === 0) {
      inputs[0]?.focus();
    } else if (len === 6) {
      inputs[len - 1]?.focus();
    } else {
      inputs[len]?.focus();
    }
  };

  return (
    // Input.OTP不支持className属性
    <div
      ref={innerRef}
      onClick={handleWrapperClick}
      // @ts-ignore
      disabled={disabled}
      className={classNames(
        'otakus-input-css-var otakus css-var-',
        prefixCls + '-otp-input',
        prefixCls +
          '-otp-input--' +
          (disabled ? 'disabled' : formContextStatus.status),
        className,
      )}
    >
      <Input.OTP
        length={length}
        // mask={'●'}
        formatter={(v) => {
          // antd 如果替换了字符中间空格，光标顺序会错乱
          const next = v.replaceAll(/[^0-9\s]/g, '');
          return next;
        }}
        disabled={disabled}
        {...rest}
        onChange={onChangeInternal}
        variant="borderless"
        placeholder="*"
        ref={codeInputRef}
        // @ts-ignore
        type={'number'}
      />
    </div>
  );
};
