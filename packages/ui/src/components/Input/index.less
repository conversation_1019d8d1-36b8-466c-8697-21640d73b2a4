@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-input {
    height: 54px;
    padding: 0 16px;

    .otakus-input-password-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
      color: var(--otakus-color-text-tertiary);

      svg {
        width: 20px;
        height: 20px;
        color: var(--otakus-color-text-tertiary);
      }
    }

    .otakus-input-password-icon:hover {
      color: var(--otakus-color-text-secondary);

      svg {
        color: var(--otakus-color-text-secondary);
      }
    }

    .otakus-input-clear-icon {
      width: 20px;
      height: 20px;

      svg {
        width: 20px;
        height: 20px;
      }
    }

    .otakus-input-clear-icon-has-suffix {
      width: 20px;
      height: 20px;
    }
  }

  &-input-top-radius {
    border-radius: var(--otakus-border-radius-xl) var(--otakus-border-radius-xl)
      0 0;
  }

  &-input-bottom-radius {
    border-radius: 0 0 var(--otakus-border-radius-xl)
      var(--otakus-border-radius-xl);
  }

  &-input-border-top-color-transparent {
    .otaku-input,
    .otaku-input-affix-wrapper {
      border-top-color: transparent;
    }

    .otaku-input-outlined,
    .otaku-input-affix-wrapper {
      &:hover {
        border-top-color: transparent;
      }

      &:focus {
        border-top-color: transparent;
      }
    }
  }

  &-input-border-bottom-color-transparent {
    .otaku-input,
    .otaku-input-affix-wrapper {
      border-bottom-color: transparent;
    }

    .otaku-input-outlined,
    .otaku-input-affix-wrapper {
      &:hover {
        border-bottom-color: transparent;
      }

      &:focus {
        border-bottom-color: transparent;
      }
    }
  }

  &-input-normal {
    border-radius: 12px;
  }

  &-input-icon {
    width: 20px;
    height: 20px;
    color: var(--otakus-color-text-quaternary);
    cursor: pointer;

    svg {
      height: 20px;
      width: 20px;
      color: var(--otakus-color-text-tertiary);
    }
  }

  &-input-icon:hover {
    color: var(--otakus-color-text-secondary);

    svg {
      color: var(--otakus-color-text-secondary);
    }
  }
}
