import { GetProps, Input, InputRef } from '@otakus/design';
import { prefixCls } from '@iam/login-shared';
import cls from 'classnames';
import './index.less';
import { forwardRef, Ref } from 'react';
import clearSvg from '@ui/assets/input-clear.svg';
import passwdShowSvg from '@ui/assets/input-passwd-show.svg';
import passwdHideSvg from '@ui/assets/input-passwd-hidden.svg';
import { ReactSVG } from 'react-svg';

type InputType = GetProps<typeof Input>;

const { Password } = Input;

interface InputProps extends InputType {
  topRadius?: boolean;
  bottomRadius?: boolean;
  borderTopColorTransparent?: boolean;
  borderBottomColorTransparent?: boolean;
  passwd?: boolean;
  normal?: boolean;
  onClear?: () => void;
}

const ClearIcon = ({ onClear }: { onClear?: () => void }) => (
  <ReactSVG
    src={clearSvg}
    className={`${prefixCls}-input-icon`}
    onClick={onClear}
  />
);

const passwdVisibilityIconRender = (visible: boolean) => {
  if (visible) {
    return (
      <ReactSVG src={passwdShowSvg} className={`${prefixCls}-input-icon`} />
    );
  }
  return <ReactSVG src={passwdHideSvg} className={`${prefixCls}-input-icon`} />;
};

const Index = forwardRef((props: InputProps, ref: Ref<InputRef>) => {
  const {
    topRadius,
    bottomRadius,
    borderTopColorTransparent,
    borderBottomColorTransparent,
    passwd,
    autoComplete = 'off',
    className,
    normal,
    onClear,
    ...rest
  } = props;
  const _className = cls(`${prefixCls}-input`, className, {
    [`${prefixCls}-input-top-radius`]: topRadius,
    [`${prefixCls}-input-bottom-radius`]: bottomRadius,
    [`${prefixCls}-input-border-top-color-transparent`]:
      borderTopColorTransparent,
    [`${prefixCls}-input-border-bottom-color-transparent`]:
      borderBottomColorTransparent,
    [`${prefixCls}-input-normal`]: normal,
  });

  if (passwd) {
    return (
      <Password
        {...rest}
        autoComplete={autoComplete}
        className={_className}
        ref={ref}
        maxLength={30}
        allowClear={{ clearIcon: <ClearIcon onClear={onClear} /> }}
        iconRender={passwdVisibilityIconRender}
      />
    );
  }

  return (
    <Input
      {...rest}
      autoComplete={autoComplete}
      className={_className}
      ref={ref}
      allowClear={{ clearIcon: <ClearIcon onClear={onClear} /> }}
    />
  );
});

export default Index;
