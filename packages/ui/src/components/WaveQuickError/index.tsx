import {
  prefixCls,
  useAssets,
  useLocale,
  usePageConfig,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { ReactSVG } from 'react-svg';
// import waveQuickErrorSvg from '@ui/assets/wave-quick-login-error.svg';
import './index.less';
import cls from 'classnames';

const Index = () => {
  const { data, loading } = usePageConfig();
  const __ = useLocale();
  const { size } = useSize();
  const waveQuickErrorSvg = useAssets('wave-quick-login-error.svg');
  if (loading) {
    return null;
  }
  const app_name = data?.data?.app_name;
  return (
    <div
      className={cls(`${prefixCls}-quick-login-error-wrapper`, {
        [`${prefixCls}-quick-login-error-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <div className={`${prefixCls}-quick-login-error-svg-wrapper`}>
        <ReactSVG src={waveQuickErrorSvg}></ReactSVG>
      </div>
      <span className={`${prefixCls}-quick-login-error-title`}>
        {__('wave:quick:login:access:restricted:title')}
      </span>
      <span className={`${prefixCls}-quick-login-error-desc`}>
        {__('wave:quick:login:access:restricted:desc').replace('%s', app_name)}
      </span>
    </div>
  );
};

export default Index;
