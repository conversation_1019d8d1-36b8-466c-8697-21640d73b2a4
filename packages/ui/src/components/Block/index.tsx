import {
  getIsWaveEnv,
  IV3_SCENE_TYPE,
  IV3Method,
  message,
  prefixCls,
  setWaveQuickForProtocol,
  useConfig,
  useFirstPaint,
  useLocale,
  usePageConfig,
  useRestPasswd,
  useSize,
  useWave,
  ViewportSize,
} from '@iam/login-shared';
import { Spin } from '@otakus/design';
import './index.less';
import { useEffect, useMemo, useState } from 'react';
import WaveQuickError from '@ui/components/WaveQuickError';
import WaveQuickLogin from '@ui/components/WaveQuickLogin';
import SwitchLoginMethod from '@ui/components/SwitchLoginMethod';
import LoginForm from '@ui/components/LoginForm';
import ResetPwdForm from '@ui/components/ResetPwdForm';
import { useUpdateEffect } from 'ahooks';
import cls from 'classnames';
import ThirdPartAuth from '@ui/components/ThirdPartAuth';

interface LoginComponentProps {
  cb?: () => void;
}

const Index = (props: LoginComponentProps) => {
  const { cb } = props;
  const { loading, data, refreshAsync } = usePageConfig();
  const { size } = useSize();
  const __ = useLocale();
  const { firstPaint } = useFirstPaint();
  const { lang, mask, scene } = useConfig();
  const [langLoading, setLangLoading] = useState<boolean>(false);
  const [formType, setFormType] = useState<string>('login');
  const loginConfig = useMemo(() => {
    if (loading && firstPaint) {
      return {};
    }
    return {
      authMethodConfigs: data?.data?.auth_method_configs,
      login_page_protocol: data?.data?.login_page_protocol,
      sessionExpiration: data?.data?.session_expiration,
      prompt: data?.data?.prompt,
      enableForgetPwd: data?.data?.enable_forgot_pwd,
      useRegister: data?.data?.enable_user_register,
      third_party_auth_method_configs:
        data?.data?.third_party_auth_method_configs,
      waveQuickLogin: data?.data?.auth_method_configs?.some(
        (e) => e.method === IV3Method.WaveQuickLogin,
      ),
    };
  }, [data, loading, firstPaint]);

  useUpdateEffect(() => {
    if (lang) {
      setLangLoading(true);
      message.loading(__('lang:loading:text'), 10);
      refreshAsync().then(() => {
        setLangLoading(false);
        message.destroy();
      });
    }
  }, [lang]);

  // toggle 的指向
  const [showQuickLogin, setShowQuickLogin] = useState<boolean>(false);

  const { userInfo, getTicketAndOpenOTPResponse } = useWave(
    !!loginConfig?.waveQuickLogin && scene === IV3_SCENE_TYPE.InitialLogin,
  );

  const resetPwdCb = () => {
    setFormType('resetPwd');
  };
  // // 重置密码前置流程
  useRestPasswd({
    handleRestPasswd: resetPwdCb,
  });

  const _cb = scene === IV3_SCENE_TYPE.InitialLogin ? cb : resetPwdCb;

  const handleShowQuickLogin = (visible: boolean) => {
    setShowQuickLogin(visible);
    setWaveQuickForProtocol(visible);
  };

  useEffect(() => {
    const isInWave = getIsWaveEnv();
    if (
      !!userInfo &&
      loginConfig?.waveQuickLogin &&
      scene === IV3_SCENE_TYPE.InitialLogin &&
      !isInWave
    ) {
      handleShowQuickLogin(true);
    }
  }, [userInfo, loginConfig?.waveQuickLogin, scene]);

  // 是否展示 toggle
  const showToggle = useMemo(() => {
    return (
      loginConfig?.authMethodConfigs?.length > 1 &&
      loginConfig?.waveQuickLogin &&
      !!userInfo &&
      scene === IV3_SCENE_TYPE.InitialLogin
    );
  }, [loginConfig, userInfo, scene]);

  // 有 快速登录，与wave 通讯失败且无其他登录方式
  const showWaveError = useMemo(() => {
    return (
      !userInfo &&
      loginConfig?.authMethodConfigs?.length === 1 &&
      loginConfig?.waveQuickLogin &&
      scene === IV3_SCENE_TYPE.InitialLogin
    );
  }, [userInfo, loginConfig, scene]);

  if (loading && !langLoading) {
    return (
      <Spin
        size={'large'}
        spinning={loading && !langLoading}
        delay={100}
        className={cls(`${prefixCls}-block-spin`, {
          [`${prefixCls}-block-spin-xs`]: size === ViewportSize.xs,
        })}
      />
    );
  }

  return (
    <Spin size={'large'} spinning={loading && !langLoading} delay={50}>
      <div
        className={cls(`${prefixCls}-block-content`, {
          [`${prefixCls}-block-content-mask`]: mask,
        })}
        hidden={loading}
      >
        <div
          hidden={showQuickLogin || showWaveError}
          className={`${prefixCls}-block-content-wrapper`}
        >
          {formType === 'login' && (
            <LoginForm
              authMethodConfigs={loginConfig?.authMethodConfigs}
              cb={_cb}
              sessionExpiration={loginConfig?.sessionExpiration}
              prompt={loginConfig?.prompt}
              enableForgetPwd={loginConfig?.enableForgetPwd}
              useRegister={loginConfig?.useRegister}
              third_party_auth_method_configs={
                loginConfig?.third_party_auth_method_configs
              }
              login_page_protocol={loginConfig?.login_page_protocol}
              appName={data?.data?.app_name}
            />
          )}
          {formType === 'resetPwd' && <ResetPwdForm />}
        </div>
        <div
          hidden={!showQuickLogin}
          className={`${prefixCls}-block-content-wrapper`}
        >
          <WaveQuickLogin
            cb={cb}
            userInfo={userInfo}
            getTicketAndOpenOTPResponse={getTicketAndOpenOTPResponse}
          />
          <ThirdPartAuth
            thirdPartyAuthMethodConfigs={
              loginConfig?.third_party_auth_method_configs
            }
          />
        </div>
        {showToggle && (
          <div className={`${prefixCls}-block-switch`}>
            <SwitchLoginMethod
              handleChange={() => handleShowQuickLogin(!showQuickLogin)}
            />
          </div>
        )}
        {showWaveError && <WaveQuickError />}
      </div>
    </Spin>
  );
};
export default Index;
