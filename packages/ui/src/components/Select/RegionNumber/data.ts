export async function getRegionNumbers() {
  return [
    {
      code: '3300',
      value: '+86',
      cn_name: '中国大陆',
      en_name: 'China Mainland',
      icon_uri: '',
      sorted: 999,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3402',
      value: '+65',
      cn_name: '新加坡',
      en_name: 'Singapore',
      icon_uri: '',
      sorted: 998,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3304',
      value: '+1',
      cn_name: '美国',
      en_name: 'United States',
      icon_uri: '',
      sorted: 997,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3529',
      value: '+1',
      cn_name: '加拿大',
      en_name: 'Canada',
      icon_uri: '',
      sorted: 996,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3390',
      value: '+81',
      cn_name: '日本',
      en_name: 'Japan',
      icon_uri: '',
      sorted: 995,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3392',
      value: '+82',
      cn_name: '韩国',
      en_name: 'Korea',
      icon_uri: '',
      sorted: 994,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3365',
      value: '+7',
      cn_name: '俄罗斯联邦',
      en_name: 'Russian Federation',
      icon_uri: '',
      sorted: 993,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3532',
      value: '+7',
      cn_name: '哈萨克斯坦',
      en_name: 'Kazakhstan',
      icon_uri: '',
      sorted: 992,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3398',
      value: '+62',
      cn_name: '印度尼西亚',
      en_name: 'Indonesia',
      icon_uri: '',
      sorted: 991,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3403',
      value: '+66',
      cn_name: '泰国',
      en_name: 'Thailand',
      icon_uri: '',
      sorted: 990,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3470',
      value: '+261',
      cn_name: '马达加斯加',
      en_name: 'Madagascar',
      icon_uri: '',
      sorted: 989,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3466',
      value: '+27',
      cn_name: '南非',
      en_name: 'South Africa',
      icon_uri: '',
      sorted: 987,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3378',
      value: '+30',
      cn_name: '希腊',
      en_name: 'Greece',
      icon_uri: '',
      sorted: 986,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3354',
      value: '+31',
      cn_name: '荷兰',
      en_name: 'Netherlands',
      icon_uri: '',
      sorted: 985,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3349',
      value: '+32',
      cn_name: '比利时',
      en_name: 'Belgium',
      icon_uri: '',
      sorted: 984,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3350',
      value: '+33',
      cn_name: '法国',
      en_name: 'France',
      icon_uri: '',
      sorted: 983,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3388',
      value: '+34',
      cn_name: '西班牙',
      en_name: 'Spain',
      icon_uri: '',
      sorted: 982,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3383',
      value: '+351',
      cn_name: '葡萄牙',
      en_name: 'Portuguese',
      icon_uri: '',
      sorted: 981,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3381',
      value: '+356',
      cn_name: '马耳他',
      en_name: 'Malta',
      icon_uri: '',
      sorted: 980,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3361',
      value: '+358',
      cn_name: '芬兰',
      en_name: 'Finland',
      icon_uri: '',
      sorted: 979,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3371',
      value: '+380',
      cn_name: '乌克兰',
      en_name: 'Ukraine',
      icon_uri: '',
      sorted: 978,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3386',
      value: '+381',
      cn_name: '塞尔维亚',
      en_name: 'Serbia',
      icon_uri: '',
      sorted: 977,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3379',
      value: '+39',
      cn_name: '意大利',
      en_name: 'Italy',
      icon_uri: '',
      sorted: 976,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3359',
      value: '+41',
      cn_name: '瑞士',
      en_name: 'Switzerland',
      icon_uri: '',
      sorted: 975,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3356',
      value: '+43',
      cn_name: '奥地利',
      en_name: 'Austria',
      icon_uri: '',
      sorted: 974,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3355',
      value: '+44',
      cn_name: '英国',
      en_name: 'United Kingdom',
      icon_uri: '',
      sorted: 973,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3602',
      value: '+44',
      cn_name: '英国属地曼岛',
      en_name: 'Isle of Man',
      icon_uri: '',
      sorted: 972,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3603',
      value: '+44',
      cn_name: '泽西岛',
      en_name: 'Jersey',
      icon_uri: '',
      sorted: 971,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3604',
      value: '+44',
      cn_name: '格恩西岛',
      en_name: 'Guernsey',
      icon_uri: '',
      sorted: 970,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3357',
      value: '+49',
      cn_name: '德国',
      en_name: 'Germany',
      icon_uri: '',
      sorted: 969,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3323',
      value: '+51',
      cn_name: '秘鲁',
      en_name: 'Peru',
      icon_uri: '',
      sorted: 968,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3305',
      value: '+52',
      cn_name: '墨西哥',
      en_name: 'Mexico',
      icon_uri: '',
      sorted: 967,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3319',
      value: '+54',
      cn_name: '阿根廷',
      en_name: 'Argentina',
      icon_uri: '',
      sorted: 966,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3320',
      value: '+55',
      cn_name: '巴西',
      en_name: 'Brazil',
      icon_uri: '',
      sorted: 965,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3400',
      value: '+60',
      cn_name: '马来西亚',
      en_name: 'Malaysia',
      icon_uri: '',
      sorted: 964,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3488',
      value: '+61',
      cn_name: '澳大利亚',
      en_name: 'Australia',
      icon_uri: '',
      sorted: 963,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3605',
      value: '+61',
      cn_name: '科科斯（基林）群岛',
      en_name: 'Cocos (Keeling) Islands',
      icon_uri: '',
      sorted: 962,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3606',
      value: '+61',
      cn_name: '圣诞岛',
      en_name: 'Christmas Island',
      icon_uri: '',
      sorted: 961,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3501',
      value: '+618',
      cn_name: '圣诞岛',
      en_name: 'Christmas Island',
      icon_uri: '',
      sorted: 961,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3401',
      value: '+63',
      cn_name: '菲律宾',
      en_name: 'Philippines',
      icon_uri: '',
      sorted: 960,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3507',
      value: '+64',
      cn_name: '新西兰',
      en_name: 'New Zealand',
      icon_uri: '',
      sorted: 959,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3533',
      value: '+64',
      cn_name: '皮特凯恩',
      en_name: 'Pitcairn Islands',
      icon_uri: '',
      sorted: 958,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3404',
      value: '+84',
      cn_name: '越南',
      en_name: 'Vietnam',
      icon_uri: '',
      sorted: 957,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3423',
      value: '+90',
      cn_name: '土耳其',
      en_name: 'Turkey',
      icon_uri: '',
      sorted: 956,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3301',
      value: '+886',
      cn_name: '中国台湾',
      en_name: 'Taiwan,Province of China',
      icon_uri: '',
      sorted: 955,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3435',
      value: '+971',
      cn_name: '阿联酋',
      en_name: 'United Arab Emirates',
      icon_uri: '',
      sorted: 954,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3422',
      value: '+995',
      cn_name: '格鲁吉亚',
      en_name: 'Georgia',
      icon_uri: '',
      sorted: 953,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3302',
      value: '+852',
      cn_name: '中国香港',
      en_name: 'Hong Kong,China',
      icon_uri: '',
      sorted: 952,
      sorted_group: 0,
      is_hide: false,
    },
    {
      code: '3303',
      value: '+853',
      cn_name: '中国澳门',
      en_name: 'Macao,China',
      icon_uri: '',
      sorted: 951,
      sorted_group: 0,
      is_hide: false,
    },
  ];
}
