import { getRegionNumbers } from './data';
import { RegionNumberSelectDrawer } from './RegionNumberSelectDrawer';
import { RegionNumberSelectLite } from './RegionNumberSelectLite';
import {
  RegionNumberDrawerSelectProps,
  RegionNumberSelectProps,
} from './types';

/**
 * /playground?name=RegionNumberSelectLiteDebug
 */
export function RegionNumberSelectLiteDebug(props: RegionNumberSelectProps) {
  return (
    <RegionNumberSelectLite
      {...props}
      style={{ width: 80 }}
      request={getRegionNumbers}
    />
  );
}

/**
 *  playground?name=RegionNumberSelectDrawerDebug
 */
export function RegionNumberSelectDrawerDebug(
  props: RegionNumberDrawerSelectProps,
) {
  return (
    <RegionNumberSelectDrawer
      title="选择国家或地区"
      {...props}
      height={'95%'}
      request={getRegionNumbers}
      open
      placement="bottom"
    />
  );
}
