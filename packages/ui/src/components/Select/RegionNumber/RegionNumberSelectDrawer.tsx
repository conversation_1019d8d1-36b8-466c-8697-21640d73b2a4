import {
  AdvanceIV3PhoneAreaCode,
  RegionNumberDrawerSelectProps,
  RegionNumberSelectProps,
} from './types';
import { Drawer } from '@otakus/design';
import { useRegionNumber } from './useRegionNumber';
import { IndexBar } from '@ui/components/IndexBar';
import { useMemo } from 'react';
import groupBy from 'lodash.groupby';
import { PanelItem } from '@ui/components/IndexBar/Panel';
import { IndexItem } from '@ui/components/IndexBar/IndexBar';
import { parseCidFromValue, parseCodeFromValue } from './utils';

export function RegionNumberSelectDrawer({
  request,
  locale,
  onChange,
  dispose,
  data: newData,
  dataMap,
  open,
  ...rest
}: RegionNumberDrawerSelectProps) {
  // const { newData, dataMap } = useRegionNumber({ locale, request });

  // 如果后端提供接口，此处理可能为后端接口字段转换
  const [groupData, indexItems] = useMemo(() => {
    // 固定4个员工数大的国家
    const [a, b, c, d, ...rest] = newData;
    const groupKey = locale === 'en_US' ? 'en_name' : 'pin_yin';
    // 安全处理
    const restData = rest || [];
    const fixed = [a, b, c, d].filter((v) => v);
    const groupMap = groupBy(restData, (item) => item?.[groupKey].charAt(0));
    let indexItems: IndexItem[] = [];
    let indices = (Reflect.ownKeys(groupMap) as string[]).sort();

    const groupData = indices.reduce((acc, key, index) => {
      indexItems.push({ brief: key.toUpperCase(), index: index + 1 + '' });
      let groupItems = groupMap[key as keyof typeof groupMap];
      // 按字母索引后排序
      groupItems.sort((z, a) => {
        return dataMap[a.code].sorted - dataMap[z.code].sorted;
      });
      acc.push({
        key: key.toUpperCase(),
        title: key.toUpperCase(),
        index: index + 1,
        items: groupItems,
      });
      return acc;
    }, [] as PanelItem<AdvanceIV3PhoneAreaCode>[]);

    groupData.unshift({
      key: '0',
      title: '',
      index: 0,
      items: fixed,
    });

    return [groupData, indexItems];
  }, [newData, dataMap]);

  return (
    <Drawer
      onClose={() => dispose()}
      open={open}
      onClick={(event) => event.stopPropagation}
      {...rest}
      styles={{
        body: { padding: 0 },
        header: {
          textAlign: 'center',
        },
      }}
    >
      <IndexBar<AdvanceIV3PhoneAreaCode>
        items={groupData}
        indexItems={indexItems}
        onSelect={(item) => {
          /**
           * @see useRegionNumber value 被格式化后拼接
           */
          onChange?.(item.value);
          dispose?.();
        }}
        renderItem={(data) => {
          return (
            <div key={data.code}>
              <span style={{ width: 40, display: 'inline-block' }}>
                {dataMap?.[parseCodeFromValue(data.value)]?.n}
              </span>
              <span style={{ marginLeft: 8 }}>
                {locale === 'en_US' ? data.en_name : data.cn_name}
              </span>
            </div>
          );
        }}
      ></IndexBar>
    </Drawer>
  );
}
