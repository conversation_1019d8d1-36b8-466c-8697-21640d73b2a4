import { BaseSelectRef, RegionNumberSelectProps } from './types';
import { prefixCls, useLocale } from '@iam/login-shared';
import {
  Empty,
  Form,
  Input,
  InputRef,
  Modal,
  Select,
  SelectProps,
} from '@otakus/design';
import { useRegionNumber } from './useRegionNumber';
import { useRequest } from 'ahooks';
import { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import './RegionNumberSelectLite.less';
import { SearchOutlined } from '@otakus/icons';
import { RegionNumberSelectDrawer } from './RegionNumberSelectDrawer';
import { parseCodeFromValue } from './utils';
import Highlighter from 'react-highlight-words';
import { ConfigProvider } from 'antd';

const baseClassName = prefixCls + '-region-number-select-lite';

/**
 * 区号选择器 lite
 */
export function RegionNumberSelectLite({
  request,
  locale,
  placeholder = '请选择',
  className,
  value,
  uiMode = 'dropdown',
  style,
  onChange,
  ...rest
}: RegionNumberSelectProps) {
  const ref = useRef<BaseSelectRef>();
  const { dataMap, newData, searchKey, setSearchKey, fieldNames } =
    useRegionNumber({
      request,
      locale,
    });

  const __ = useLocale();
  const searchInputRef = useRef<InputRef>(null);
  const dropdownVisibleRef = useRef<boolean>(false);
  const drawerVisibleRef = useRef<boolean>(false);
  const uiModeRef = useRef<string>(uiMode);
  uiModeRef.current = uiMode;

  const [drawerVisible, setDrawerVisible] = useState(false);
  drawerVisibleRef.current = drawerVisible;

  useEffect(() => {
    // 从dropdown的open状态切换uiMode时，保留打开状态
    if (uiMode === 'drawer' && dropdownVisibleRef.current) {
      setDrawerVisible(true);
    } else if (uiMode === 'dropdown') {
      const prevDrawerVisible = drawerVisibleRef.current;
      setDrawerVisible(false);
      if (prevDrawerVisible) {
        // 通过事件触发
        ref.current.nativeElement.click();
      } else {
      }
    }
  }, [uiMode]);

  return (
    <ConfigProvider
      renderEmpty={() => {
        return (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span
                style={{ marginTop: -16, display: 'block', paddingBottom: 8 }}
              >
                {__('search.empty')}
              </span>
            }
          />
        );
      }}
    >
      <Select
        ref={(r) => {
          ref.current = r;
        }}
        virtual={false}
        value={value}
        onClick={(e) => e.stopPropagation()}
        className={classNames(
          baseClassName,
          className,
          // magic: drawer上的点击等事件会触发select失去焦点，导致样式变为 select close状态, 通过类名强制样式
          uiMode === 'drawer' && drawerVisible ? 'otakus-select-open' : '',
        )}
        dropdownStyle={{
          width: '100%',
          // select 返回空仍然会有一小部分的dom占据空间，并在动画结束后消失，强制隐藏
          visibility: uiMode === 'drawer' ? 'hidden' : undefined,
        }}
        popupMatchSelectWidth={false}
        labelRender={(_v) => {
          // MARK: _v 参数有bug
          // 优化label的闪烁展示
          if (typeof value === 'string' && value.indexOf('|') > -1) {
            const key = parseCodeFromValue(value);
            return dataMap?.[key]?.n ?? '';
          }
          return '';
        }}
        style={{
          ...style,
        }}
        onDropdownVisibleChange={(open) => {
          setSearchKey('');
          dropdownVisibleRef.current = open;
          if (open && uiMode === 'drawer') {
            setDrawerVisible(true);
          }
        }}
        dropdownRender={(menu) => {
          if (uiMode === 'drawer') return null;
          return (
            <div style={{ padding: 2 }}>
              <span></span>
              {/* 添加formItem 防止嵌套时获取到上层error、warning反馈 */}
              <Form.Item>
                <Input
                  ref={(r) => {
                    r?.focus?.();
                  }}
                  autoFocus
                  className="secret-class"
                  prefix={<SearchOutlined style={{ color: '#9294A3' }} />}
                  style={{ marginBottom: 6 }}
                  value={searchKey}
                  onChange={(e) => setSearchKey(e.target.value)}
                />
              </Form.Item>
              {menu}
            </div>
          );
        }}
        listHeight={240}
        optionRender={(option) => {
          return (
            <div key={option.data.code}>
              <span style={{ width: 40, display: 'inline-block' }}>
                <Highlighter
                  highlightStyle={{
                    color: 'var(--otakus-color-primary)',
                    background: 'none',
                  }}
                  searchWords={[searchKey]}
                  autoEscape={true}
                  textToHighlight={dataMap?.[option.key].n as string}
                ></Highlighter>
              </span>
              <span style={{ marginLeft: 8 }}>
                <Highlighter
                  highlightStyle={{
                    color: 'var(--otakus-color-primary)',
                    background: 'none',
                  }}
                  searchWords={[searchKey]}
                  autoEscape={true}
                  textToHighlight={option.label as string}
                ></Highlighter>
              </span>
            </div>
          );
        }}
        onChange={onChange}
        fieldNames={fieldNames}
        options={newData}
        placeholder={placeholder}
        {...rest}
      ></Select>
      {uiMode === 'drawer' ? (
        <RegionNumberSelectDrawer
          // size="large"
          destroyOnClose
          placement="bottom"
          request={request}
          locale={locale}
          open={drawerVisible}
          onChange={(value) => onChange(value, dataMap[value])}
          data={newData}
          dataMap={dataMap}
          dispose={() => {
            setDrawerVisible(false);
            // ref.current.blur();
          }}
        />
      ) : null}
    </ConfigProvider>
  );
}
