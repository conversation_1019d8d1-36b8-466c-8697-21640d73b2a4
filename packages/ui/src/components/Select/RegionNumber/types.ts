import { IV3PhoneAreaCode } from '@iam/login-shared';
import { DrawerProps, SelectProps } from '@otakus/design';

export type AdvanceIV3PhoneAreaCode = IV3PhoneAreaCode & {
  /**
   * 虚拟字段
   */
  n?: string;
};

export type RegionNumberSelectBase = {
  locale?: 'zh_CN' | 'en_US';
  request?: () => Promise<IV3PhoneAreaCode[]>;
  uiMode?: 'drawer' | 'dropdown';
};

export interface RegionNumberSelectProps
  extends SelectProps<string>,
    RegionNumberSelectBase {
  //
}

export interface RegionNumberDrawerSelectProps
  extends DrawerProps,
    RegionNumberSelectBase {
  /**
   * 暂不支持
   */
  value?: string;
  /**
   * @param value 新选择的区号代码（code)
   * @returns
   */
  onChange?: (value?: string) => void;

  data?: AdvanceIV3PhoneAreaCode[];

  dataMap?: Record<string, AdvanceIV3PhoneAreaCode>;

  dispose?: () => void;
}

export type UseRegionNumberParams = RegionNumberSelectBase;

/**
 * copy from : rc-select
 */
export interface BaseSelectRef {
  focus: (options?: FocusOptions) => void;
  blur: () => void;
  // scrollTo: ScrollTo;
  nativeElement: HTMLElement;
}
