import { useRequest } from 'ahooks';
import { AdvanceIV3PhoneAreaCode, UseRegionNumberParams } from './types';
import { useMemo, useRef, useState } from 'react';
import { IV3PhoneAreaCode } from '@iam/login-shared';
import { formatValue } from './utils';

export function useRegionNumber({ request, locale }: UseRegionNumberParams) {
  const { data } = useRequest(request);
  // const container = useRef<HTMLDivElement | null>(null);
  const [searchKey, setSearchKey] = useState('');

  const fieldNames = useMemo(() => {
    if (locale === 'zh_CN') {
      return {
        label: 'cn_name',
        // value: 'code',
      };
    } else {
      return { label: 'en_name' };
    }
  }, [locale]);

  const [newData, dataMap] = useMemo(() => {
    const dataMap: Record<string, AdvanceIV3PhoneAreaCode> = {};
    const newData: AdvanceIV3PhoneAreaCode[] = [];

    if (data) {
      for (let item of data) {
        const newItem = {
          ...item,
          // 隐藏拼接逻辑
          // value: item.value + '|' + item.code,
          value: formatValue(item.value, item.code),
          key: item.code,
          n: item.value,
        };
        dataMap[item.code] = newItem;
        const trimedSearchKey = searchKey.trim().toLowerCase();
        if (trimedSearchKey) {
          if (
            newItem[fieldNames.label].toLowerCase().includes(trimedSearchKey) ||
            newItem.n.includes(trimedSearchKey)
          ) {
            newData.push(newItem);
          }
        } else {
          newData.push(newItem);
        }
      }
    }
    return [newData, dataMap];
  }, [data, searchKey]);

  return {
    newData,
    dataMap,
    setSearchKey,
    fieldNames,
    searchKey,
  };
}
