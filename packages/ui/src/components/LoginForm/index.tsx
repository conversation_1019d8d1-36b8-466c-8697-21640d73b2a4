import Gee, { GeetestResponse } from '@iam/login-captcha';
import {
  dispatch,
  EMAIL_PATTERN,
  formatCurMethods,
  getGeeLang,
  getKeyV3,
  getPreCheck,
  goToPasswd,
  handleSubmit,
  IV3_SCENE_TYPE,
  IV3AuthMethodConfigs,
  IV3LoginPageConfig,
  IV3Method,
  IV3PerCheckParams,
  IV3ThirdPartyAuthMethodConfigs,
  message,
  message as messageToast,
  prefixCls,
  rsa,
  ServiceErrorCodes,
  setCurrentMethodForProtocol,
  setVisible,
  trimFormItemValues,
  useConfig,
  useKeyPressSubmit,
  useLocale,
  usePageConfig,
  usePolicy,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Button, Checkbox, Form, InputRef, Modal } from '@otakus/design';
import { QuestionCircleOutlined } from '@otakus/icons';
import AccountForm, {
  ACCOUNT_FORM_ITEM,
} from '@ui/components/Form/AccountForm';
import Tooltip from '@ui/components/LoginForm/ToolTip';
import SecondVerification, {
  SecondVerificationProps,
} from '@ui/components/SecondVerification';
import Tab from '@ui/components/Tab';
import ThirdPartAuth from '@ui/components/ThirdPartAuth';
import Title from '@ui/components/Title';
import WaveQrcode from '@ui/components/WaveQrcode';
import { useSetState, useThrottleFn } from 'ahooks';
import cls from 'classnames';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { MailForm } from '../Form/MailForm';
import { SMSForm } from '../Form/SMSForm';
import { handleFormErrorTooltip } from './helper';
import './index.less';
import { OTPRef } from 'antd/es/input/OTP';
import PrivacyDetailModal from '@ui/components/Footer/PrivacyDetailModal';

interface LoginFormProps {
  authMethodConfigs?: IV3AuthMethodConfigs[];
  cb?: (ticket?: string) => void;
  sessionExpiration: IV3LoginPageConfig['session_expiration'];
  prompt: IV3LoginPageConfig['prompt'];
  enableForgetPwd: IV3LoginPageConfig['enable_forgot_pwd'];
  useRegister?: IV3LoginPageConfig['enable_user_register'];
  third_party_auth_method_configs?: IV3ThirdPartyAuthMethodConfigs[];
  appName: IV3LoginPageConfig['app_name'];
  login_page_protocol: IV3LoginPageConfig['login_page_protocol'];
}

const Index = (props: LoginFormProps) => {
  const {
    authMethodConfigs,
    sessionExpiration,
    enableForgetPwd,
    third_party_auth_method_configs,
    appName,
    cb,
    login_page_protocol,
  } = props;
  const [active, setActive] = useState<IV3Method>('' as unknown as IV3Method);
  const __ = useLocale();
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const {
    clientId,
    authApi,
    env,
    lang,
    scene,
    privacyChecked,
    handlePrivacyChecked,
  } = useConfig();
  const [rememberMe, setRemember] = useState<boolean>(false);
  const [secondAuthProps, setSecondAuth] =
    useSetState<SecondVerificationProps>(null);
  const [submittable, setSubmittable] = React.useState<boolean>(false);
  const { size } = useSize();
  const phoneInputRef = useRef<InputRef>(null);
  const phoneCodeInputRef = useRef<OTPRef>(null);
  const emailInputRef = useRef<InputRef>(null);
  const emailCodeInputRef = useRef<OTPRef>(null);
  const [gee, showGee] = useState<boolean>(false);
  const activeRef = useRef<IV3Method>(null as unknown as IV3Method);
  const autofillRef = useRef<number>(0);
  const [modal, contextHolder] = Modal.useModal();
  const { handleShowPolicy, handleCancel, visible, privacy } = usePolicy();

  const { data } = usePageConfig();
  const check_agreement_privacy = data?.data?.check_agreement_privacy;
  const _appName =
    scene === IV3_SCENE_TYPE.InitialRestPasswd
      ? __('reset:title')
      : __('otk:title') + appName;

  // 为了监控到 chrome 账密自动填充，此时 按钮 可以点击
  useEffect(() => {
    document.addEventListener(
      'animationstart',
      function (event) {
        if (event.animationName === 'autofillOne') {
          autofillRef.current += 1;
          if (autofillRef.current === 2) {
            setSubmittable(true);
          }
        }
      },
      false,
    );
  }, []);

  useEffect(() => {
    if (!activeRef.current) {
      const _authMethodConfigs = formatCurMethods(authMethodConfigs);
      handleTabChange(_authMethodConfigs?.[0]?.method);
    } else {
      handleTabChange(activeRef.current);
    }
  }, [authMethodConfigs]);

  const tooltipErrorMap = useMemo(() => {
    return new Map<ServiceErrorCodes, string | [string, () => void]>([
      [ServiceErrorCodes.SmsOtpInvalid, 'verify_code'],
      [ServiceErrorCodes.EmailOtpInvalid, 'verify_code'],
      [ServiceErrorCodes.InvalidPhoneFormat, 'phone_number'],
      [
        ServiceErrorCodes.PhoneNotInSendWhiteList,
        ['phone_number', () => phoneInputRef.current.focus()],
      ],
      [ServiceErrorCodes.UserNotFoundOrPasswordNotMatch, 'password'],
      [ServiceErrorCodes.TryToLoginWithPersonEmail, 'email'],
      [
        ServiceErrorCodes.EmailNotInSendWhiteList,
        ['email', () => emailInputRef.current.focus()],
      ],
    ]);
  }, []);

  const values = Form.useWatch([], form);

  // TODO 此处冗余了很多表单项交互逻辑，过两天优化
  useEffect(() => {
    // 账密表单
    if (active === IV3Method.AccountPwd) {
      // 对任意一个输入框内容做出编辑后，两输入框解除 error 态
      if (values.user_account || values.password) {
        form.setFields([
          {
            name: 'user_account',
            errors: [],
          },
          {
            name: 'password',
            errors: [],
          },
        ]);
      }
    }

    // 短信表单
    if (active === IV3Method.SMS) {
      if (values.verify_code) {
        // 短信登录otp发生变化，且phone_number cid 都有值，触发自动提交
        if (
          values.cid &&
          values.phone_number &&
          values.verify_code?.length === 6
        ) {
          Promise.resolve().then(() => submit());
        } else if (values.verify_code?.length !== 6) {
        } else {
          // 无获取验证码行为
          // 1. focus 到手机输入框
          // 2. 验证码输入框清空
          // 3. message 清先获取验证码
          form.setFields([
            {
              name: 'verify_code',
              value: '',
              errors: [],
            },
          ]);
          phoneInputRef.current.focus();
          messageToast.warning(__('sms:code:first:message'));
        }
      } else {
      }
    }

    // 邮箱表单
    if (active === IV3Method.Email) {
      if (values.verify_code && values.verify_code?.length === 6) {
        // 通过 正则校验 即可提交
        const email = values?.email?.trim() || '';
        if (!email) {
          form.setFields([
            {
              name: 'verify_code',
              value: '',
            },
          ]);
          emailInputRef.current.focus();
          message.warning(__('email:code:first:message'));
        } else if (
          EMAIL_PATTERN.test(email) &&
          values.verify_code?.length === 6
        ) {
          Promise.resolve().then(() => submit());
        } else if (values.verify_code?.length !== 6) {
        } else {
          // 邮箱有效性校验不通过时，[验证码] 输入框自动清空
          // 邮箱有效性校验不通过时，输入框呈现出 error_focus 态
          form.setFields([
            {
              name: 'email',
              errors: [__('email:verify:message')],
            },
            {
              name: 'verify_code',
              value: '',
            },
          ]);
          emailInputRef.current.focus();
        }
      }
    }
  }, [active, values]);

  const handleTabChange = (_active: IV3Method) => {
    if (_active === active) {
      return;
    }
    activeRef.current = _active;
    form?.resetFields();
    setActive(_active);
    setCurrentMethodForProtocol(_active);
  };
  const getCurrentMethod = () => {
    return authMethodConfigs?.filter((e) => e.method === active)[0];
  };

  const currentMethodConfig = useMemo(() => {
    return getCurrentMethod();
  }, [active]);

  const rsaPassword = async (password: string) => {
    const authKey = await getKeyV3(authApi, clientId);
    const { public_key = '', hash = '' } = authKey || {};
    return rsa(public_key, password + hash);
  };

  const getParams = async (captcha?: IV3PerCheckParams) => {
    setLoading(true);
    let value = form.getFieldsValue();
    const { method } = getCurrentMethod();
    if (method === IV3Method.AccountPwd) {
      value = {
        ...value,
        password: await rsaPassword(value.password),
      };
    }
    if (method === IV3Method['SMS']) {
      value = {
        ...value,
      };
    }
    const _value = trimFormItemValues(value);
    const data = await handleSubmit(
      {
        captcha,
        ..._value,
        remember_me: rememberMe,
        otp_scene: scene,
      },
      authApi,
      active,
    );
    setLoading(false);
    // 登陆成功 回调
    if (data.isLogin) {
      cb && cb(data?.ticket);
      return;
    }
    if (data.mfaConfig) {
      setSecondAuth({
        visible: true,
        method: data.mfaConfig,
      });
      if (method === IV3Method.SMS || method === IV3Method.Email) {
        form?.setFields([
          {
            name: 'verify_code',
            value: '',
          },
        ]);
      }
      return;
    }

    if (data.error) {
      handleFormErrorTooltip(form, data.error, tooltipErrorMap);
      // 下列逻辑为一些错误情况下更改表单状态
      // TODO 此处冗余了很多表单项交互逻辑，后续可以考虑优化
      if (method === IV3Method['AccountPwd']) {
        // 账密 验证失败,账密输入框 error 状态
        if (
          data?.error?.code === ServiceErrorCodes.UserNotFoundOrPasswordNotMatch
        ) {
          form.setFields([
            {
              name: 'user_account',
              errors: [''],
            },
          ]);
        }
      }
      if (method === IV3Method.SMS) {
        if (data?.error?.code === ServiceErrorCodes.SmsOtpInvalid) {
          // 验证码校验失败，清空验证码输入框
          form.setFields([
            {
              name: 'verify_code',
              value: '',
            },
          ]);
          phoneCodeInputRef.current.focus();
        }
        // 手机号有效性校验不通过时，[验证码] 输入框自动清空
        if (data?.error?.code === ServiceErrorCodes.InvalidPhoneFormat) {
          form.setFields([
            {
              name: 'verify_code',
              value: '',
            },
          ]);
          phoneInputRef?.current?.focus();
        }
      }
      if (method === IV3Method.Email) {
        if (data?.error?.code === ServiceErrorCodes.EmailOtpInvalid) {
          // 验证码校验失败，清空验证码输入框
          form.setFields([
            {
              name: 'verify_code',
              value: '',
            },
          ]);
          emailCodeInputRef.current.focus();
        }
      }
    }
  };

  // 滑块订阅异步获取 sig
  const handleSlider = (captcha_id: string) => {
    if (captcha_id) {
      showGee(true);
      return new Promise<GeetestResponse>((resolve) => {
        const instance = new Gee({
          container: document.getElementById('iam-root'),
          captchaId: captcha_id,
          product: 'bind',
          language: getGeeLang(lang),
          protocol: 'https://',
          nextWidth: '500',
          onSuccess: async (res: GeetestResponse) => {
            showGee(false);
            resolve(res);
          },
          onError: () => {
            showGee(false);
            setLoading(false);
          },
          onFail: () => {},
          onClose: () => {
            showGee(false);
            setLoading(false);
          },
        });
        instance.showGee();
      });
    }
  };

  const onFinish = () => {
    const realSubmit = () => {
      form
        .validateFields()
        .then(async () => {
          setLoading(true);
          form.setFields([
            {
              name: ACCOUNT_FORM_ITEM.USER_ACCOUNT,
              errors: [],
            },
            {
              name: ACCOUNT_FORM_ITEM.PASSWD,
              errors: [],
            },
          ]);
          const { method, require_captcha } = getCurrentMethod();
          // 此判断逻辑为 邮箱 短信验证 获取验证码的时候会人机验证 而不是 submit 的时候
          if (method !== IV3Method['Email'] && method !== IV3Method['SMS']) {
            await getPreCheck(
              env,
              require_captcha,
              {
                auth_method: method,
                scene_type: scene,
              },
              getParams,
              handleSlider,
              () => {},
            );
          } else {
            await getParams();
          }
          setLoading(false);
        })
        .catch((e) => {
          console.log(e);
          return null;
        });
    };
    if (
      check_agreement_privacy &&
      active !== IV3Method.WaveQR &&
      active !== IV3Method.WaveQuickLogin
    ) {
      if (privacyChecked) {
        realSubmit();
      } else {
        modal.confirm({
          className:
            size === ViewportSize.xs ? `${prefixCls}-protocol-modal` : '',
          icon: null,
          title: __('footer:privacy:confirm:modal:title'),
          content: (
            <div>
              <span>{login_page_protocol.agreement_prefix}</span>
              {login_page_protocol?.agreement_list?.map((protocol, index) => (
                <span
                  key={index}
                  className={`${prefixCls}-policy-title`}
                  onClick={() => handleShowPolicy(protocol.id)}
                >
                  {protocol?.protocol_name}
                </span>
              ))}
            </div>
          ),
          onOk: () => {
            handlePrivacyChecked(true);
            realSubmit();
          },
          onCancel: () => {
            if (active === IV3Method.Email || active === IV3Method.SMS) {
              form.setFields([
                {
                  name: 'verify_code',
                  value: '',
                },
              ]);
            }
          },
          okText: __('footer:privacy:confirm:modal:ok:text'),
          cancelText: __('footer:privacy:confirm:modal:cancel:text'),
        });
      }
    } else {
      realSubmit();
    }
  };
  const { run: submit } = useThrottleFn(onFinish, {
    wait: 500,
  });

  const waveQrcodeCb = async (ticket: string) => {
    cb?.(ticket);
  };

  useKeyPressSubmit(
    () => {
      if (!loading && submittable && active === IV3Method.AccountPwd) {
        submit();
      }
      return;
    },
    {
      target: document.querySelector('#login'),
    },
  );

  useEffect(() => {
    if (active === IV3Method.AccountPwd) {
      const { user_account, password } = values;
      if (user_account && password) {
        setSubmittable(true);
      } else {
        setSubmittable(false);
      }
    }
  }, [active, values]);

  useEffect(() => {
    if (active === IV3Method.Email || active === IV3Method.SMS) {
      if (loading) {
        messageToast.loading(__('verification:code:message'));
      } else {
        messageToast.destroy();
      }
    }
  }, [loading, active]);

  const formDisabled = useMemo(() => {
    if (gee) {
      return gee;
    }
    return loading && (active === IV3Method.Email || active === IV3Method.SMS);
  }, [gee, loading, active]);

  const handleValueChange = (_: any) => {
    if (active === IV3Method.SMS) {
      form.setFields([{ name: 'phone_number', errors: [] }]);
    }
    if (active === IV3Method.Email) {
      form.setFields([{ name: 'email', errors: [] }]);
    }
  };

  const toolTipsPlacement =
    scene === IV3_SCENE_TYPE.InitialRestPasswd
      ? 'bottom'
      : lang === 'en-US'
      ? 'bottom'
      : 'bottomRight';

  return (
    <>
      <div hidden={secondAuthProps?.visible}>
        <div
          className={cls(`${prefixCls}-login-form-title`, {
            [`${prefixCls}-login-form-title-xs`]: size === ViewportSize.xs,
          })}
        >
          {<Title title={_appName} />}
        </div>
        <Tab
          authMethodConfigs={authMethodConfigs}
          active={active}
          onChange={handleTabChange}
        />
        {
          <Form
            form={form}
            name={'login'}
            className={`${prefixCls}-login-form-wrapper`}
            disabled={formDisabled}
            onValuesChange={handleValueChange}
          >
            {setVisible({
              visible: active === IV3Method.AccountPwd,
              component: <AccountForm enableForgetPwd={enableForgetPwd} />,
            })}
            {setVisible({
              visible: active === IV3Method.SMS,
              component: (
                <SMSForm
                  onFormError={(error) =>
                    handleFormErrorTooltip(form, error, tooltipErrorMap)
                  }
                  form={form}
                  captcha={currentMethodConfig?.require_captcha}
                  onSlider={handleSlider}
                  phoneInputRef={phoneInputRef}
                  codeInputRef={phoneCodeInputRef}
                />
              ),
            })}
            {setVisible({
              visible: active === IV3Method.Email,
              component: (
                <MailForm
                  form={form}
                  onFormError={(error) =>
                    handleFormErrorTooltip(form, error, tooltipErrorMap)
                  }
                  captcha={currentMethodConfig?.require_captcha}
                  onSlider={handleSlider}
                  emailInputRef={emailInputRef}
                  codeInputRef={emailCodeInputRef}
                />
              ),
            })}
            {setVisible({
              visible: active === IV3Method['WaveQR'],
              component: (
                <WaveQrcode
                  {...getCurrentMethod()?.config}
                  documentId={'iam-wave-qrcode-default'}
                  type={'default'}
                  cb={waveQrcodeCb}
                  extraProps={{
                    otp_scene: scene,
                  }}
                />
              ),
            })}
            <div
              className={cls(`${prefixCls}-login-form-extra-wrapper`, {
                [`${prefixCls}-login-form-wave-qrcode-extra-wrapper`]:
                  active === IV3Method['WaveQR'],
                [`${prefixCls}-login-form-extra-wrapper-xs`]:
                  size === ViewportSize.xs,
                [`${prefixCls}-login-form-extra-wrapper-en`]: lang === 'en-US',
              })}
            >
              {setVisible({
                visible:
                  !!sessionExpiration && scene === IV3_SCENE_TYPE.InitialLogin,
                component: (
                  <Checkbox
                    checked={rememberMe}
                    onChange={(e) => {
                      dispatch(
                        {
                          rememberMe: e.target.checked,
                        },
                        false,
                        'login-form-remember-checkbox',
                      );
                      setRemember(e.target.checked);
                    }}
                    className={`${prefixCls}-login-form-remember-checkbox`}
                  >
                    {sessionExpiration}
                  </Checkbox>
                ),
              })}
              {/*重置密码*/}
              {setVisible({
                visible:
                  !!enableForgetPwd &&
                  active === IV3Method.AccountPwd &&
                  scene === IV3_SCENE_TYPE.InitialLogin,
                component: (
                  <Button
                    type={'link'}
                    className={`${prefixCls}-login-form-extra-forget`}
                    onClick={() => goToPasswd(env, lang)}
                  >
                    {__('enable:forget:pwd')}
                  </Button>
                ),
              })}
              {/*前往账号中心*/}
              {setVisible({
                visible: active === IV3Method.SMS,
                component: (
                  <div className={`${prefixCls}-login-form-tips`}>
                    {__('sms:captcha:not:received')}
                    <Tooltip
                      placement={toolTipsPlacement}
                      title={<span>{__('sms:login:code:tips:v2')}</span>}
                    >
                      <div className={`${prefixCls}-login-form-question-icon`}>
                        <QuestionCircleOutlined />
                      </div>
                    </Tooltip>
                  </div>
                ),
              })}
              {/*前往账号中心*/}
              {setVisible({
                visible: active === IV3Method.Email,
                component: (
                  <div className={`${prefixCls}-login-form-tips`}>
                    {__('email:captcha:not:received')}
                    <Tooltip
                      placement={toolTipsPlacement}
                      title={<span>{__('email:login:code:tips:v2')}</span>}
                    >
                      <div className={`${prefixCls}-login-form-question-icon`}>
                        <QuestionCircleOutlined />
                      </div>
                    </Tooltip>
                  </div>
                ),
              })}
            </div>
            {setVisible({
              visible: active === IV3Method.AccountPwd,
              component: (
                <Button
                  type="primary"
                  className={`${prefixCls}-login-form-submit`}
                  size="large"
                  onClick={submit}
                  disabled={!submittable}
                  loading={loading}
                >
                  {scene === IV3_SCENE_TYPE.InitialLogin
                    ? __('submit')
                    : __('passwd:idp:submit')}
                </Button>
              ),
            })}
          </Form>
        }
        {setVisible({
          visible: scene === IV3_SCENE_TYPE.InitialLogin,
          component: (
            <ThirdPartAuth
              thirdPartyAuthMethodConfigs={third_party_auth_method_configs}
            />
          ),
        })}
      </div>
      <SecondVerification
        {...secondAuthProps}
        cb={cb}
        scene={scene}
        handleVisible={(visible: boolean) => setSecondAuth({ visible })}
      />
      <PrivacyDetailModal
        privacy={privacy}
        handleCancel={handleCancel}
        visible={visible}
      />
      {contextHolder}
    </>
  );
};

export default Index;
