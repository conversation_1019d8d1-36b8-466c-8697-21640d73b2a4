@import '../../index.less';

@keyframes autofillOne {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
}

input:-webkit-autofill {
  animation: autofillOne 0.1s infinite;
  //-webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  //-webkit-text-fill-color: inherit;
  //border: none;
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: inherit;
  background-clip: content-box !important; /* 限制背景渲染区域 */
  border: none !important; /* 移除边框 */
}

input:-webkit-autofill:focus:active:hover {
  animation: autofillOne 0.1s infinite;
  -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  -webkit-text-fill-color: inherit;
  border: none;
}

.otakus-input-disabled {
  input {
    -webkit-box-shadow: 0 0 0 1000px var(--otakus-color-bg-container-disabled)
      inset !important;
    -webkit-text-fill-color: var(--otakus-color-text-disabled);
    border: none;
  }
}

.@{info-iam-login-prefix-cls} {
  &-login-form-submit {
    width: 100%;
    height: 54px;
    margin-top: 12px;
    border-radius: 12px;
  }

  &-login-form-wrapper {
    width: 100%;

    .-form-item-explain-error {
      display: none;
    }

    .otakus-form-item {
      margin-top: -1px;
      // z-index: 1;
      margin-bottom: 0;
    }

    .otakus-form-item:hover {
      z-index: 4; /* 悬停状态的 z-index */
    }

    .otakus-form-item:focus-within {
      z-index: 5; /* 聚焦状态的 z-index */
    }

    .otakus-form-item:active {
      z-index: 6; /* 激活状态的 z-index */
    }

    .otakus-input-status-error,
    .otakus-form-item-has-error {
      z-index: 3; /* 报错状态的 z-index */
    }

    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }
  }

  &-login-form-extra-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 0 0;
    color: var(--otakus-color-text-description);

    & > :only-child {
      margin: 0 auto;
    }

    & > :nth-child(2) {
      /* 当有两个子元素时，第一个靠左，第二个靠右 */
      margin-left: 0;
      margin-right: 0;
    }
  }

  &-login-form-extra-wrapper-en {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  &-login-form-wave-qrcode-extra-wrapper {
    margin: 0 0 12px;
  }

  &-login-form-extra-forget {
    color: var(--otakus-color-text-description);
    padding: 0;
    font-weight: 400;

    &:hover {
      color: var(--otakus-color-link-hover);
    }

    &:focus,
    &:focus-within {
      color: var(--otakus-color-link);
    }

    span {
      text-decoration: underline;
    }
  }

  &-login-form-tips-btn {
    color: rgba(146, 168, 254, 1);
    padding: 0;

    span {
      text-decoration: underline;
    }
  }

  &-login-form-tips-btn:hover {
    color: rgba(180, 197, 255, 1);
  }

  &-login-form-question-icon {
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: var(--otakus-border-radius);
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-login-form-question-icon:hover {
    background: var(--otakus-color-bg-text-hover);
  }

  &-login-form-question-icon:active {
    background: var(--otakus-color-bg-text-active);
  }

  &-login-form-tips-tooltip {
    width: 280px;
    max-width: 280px;
  }

  &-login-form-tips {
    display: flex;
    align-items: center;
  }

  &-login-form-remember-checkbox {
    color: var(--otakus-color-text-description);
  }

  &-login-form-title {
    margin-bottom: 36px;
  }

  &-login-form-title-xs {
    margin-bottom: 24px;
    font-size: 24px;
    line-height: 32px;
    padding-top: 36px;

    .@{info-iam-login-prefix-cls}-title {
      //font-size: var(--otakus-font-size-heading-3);
      line-height: var(--otakus-line-height-heading-3);
    }
  }

  &-login-form-extra-wrapper-xs {
    margin: 20px 0 0;
  }

  &-protocol-modal {
    .otakus-modal-confirm-title {
      text-align: center;
    }

    .otakus-modal-confirm-content {
      text-align: center;
    }

    .otakus-modal-confirm-btns {
      font-size: 16px;
      text-align: center;

      .otakus-btn {
        height: 40px;
        width: 140px;
      }
    }
  }
}
