import { ServiceErrorCodes, message as messageToast } from '@iam/login-shared';
import { FormInstance } from '@otakus/design';
import { FormItem } from '../Form/FormItem';

/**
 * 表单字段名
 */
type FormItem =
  | string
  | { name: string; variant: 'error' | 'warning' }
  | [string, () => void];

/**
 * 处理登录表单上的tooltip错误提示
 * @param form
 * @param error
 * @param config
 * @param fallbackToMessage 是否降级成message提示
 */
export function handleFormErrorTooltip(
  form: FormInstance,
  error: {
    code?: ServiceErrorCodes;
    message: string;
  },
  config: Map<ServiceErrorCodes, FormItem>,
  fallbackToMessage: boolean = true,
) {
  const { code: errorCode, message } = error;

  if (config.has(errorCode)) {
    const formItem = config.get(errorCode);
    form.setFields([
      {
        name:
          typeof formItem === 'string'
            ? formItem
            : Array.isArray(formItem)
            ? formItem[0]
            : formItem.name,
        // message 服务端已处理
        errors: [message],
      },
    ]);
    if (Array.isArray(formItem) && typeof formItem?.[1] === 'function') {
      formItem[1]();
    }
  } else if (fallbackToMessage) {
    // TODO: 支持 warning
    messageToast.error(message);
  }
}
