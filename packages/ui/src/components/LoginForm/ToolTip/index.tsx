import { GetProps, Tooltip } from '@otakus/design';
import './index.less';
import { prefixCls, useConfig, useSize, ViewportSize } from '@iam/login-shared';
import React from 'react';
import cls from 'classnames';

type TooltipType = GetProps<typeof Tooltip>;
const Index = (props: TooltipType) => {
  const { children, ...rest } = props;
  const { size } = useSize();
  const { lang } = useConfig();
  return (
    <Tooltip
      overlayClassName={cls({
        [`${prefixCls}-login-form-tips-tooltip`]: size !== ViewportSize.xs,
        [`${prefixCls}-login-form-tips-tooltip-xs`]: size === ViewportSize.xs,
      })}
      arrow={{ pointAtCenter: true }}
      trigger={['hover', 'click']}
      {...rest}
    >
      {children}
    </Tooltip>
  );
};

export default Index;
