import {
  But<PERSON>,
  Confi<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er,
  Get<PERSON>rops,
  useTheme,
} from '@otakus/design';
import { LeftOutlined } from '@otakus/icons';
import { prefixCls, useSize, ViewportSize } from '@iam/login-shared';
import Title from '@ui/components/Title';
import './index.less';
import cls from 'classnames';
import React from 'react';

type DrawerType = GetProps<typeof Drawer>;

interface DrawerProps extends DrawerType {
  title: string;
  showCloseIcon?: boolean;
  titleClassName?: string;
  headClassName?: string;
}

const Index = (props: DrawerProps) => {
  const {
    title,
    children,
    placement = 'right',
    zIndex = 101,
    width = '100vw',
    mask = false,
    className,
    onClose,
    showCloseIcon = true,
    titleClassName,
    headClassName,
    ...rest
  } = props;

  const token = useTheme();
  const { size } = useSize();
  return (
    <div id={'iam-drawer'}>
      <ConfigProvider
        // hasDefaultApp={false}
        theme={{
          token: {
            motion: false,
          },
          components: {
            Drawer: {
              motion: false,
            },
          },
        }}
      >
        <Drawer
          title={null}
          placement={placement}
          zIndex={zIndex}
          width={width}
          mask={mask}
          className={cls(`${prefixCls}-drawer ${className}`, {
            [`${prefixCls}-drawer-xs`]: size === ViewportSize.xs,
          })}
          onClose={onClose}
          destroyOnClose
          {...rest}
        >
          <div className={cls(`${prefixCls}-drawer-wrapper`)}>
            <div className={`${prefixCls}-drawer-head ${headClassName}`}>
              {showCloseIcon && (
                <div className={`${prefixCls}-drawer-back-btn`}>
                  <Button
                    icon={<LeftOutlined style={{ color: 'inherit' }} />}
                    onClick={(e) => onClose(e)}
                  ></Button>
                </div>
              )}
              <div
                className={cls(
                  `${prefixCls}-drawer-title`,
                  `${titleClassName}`,
                  {
                    [`${prefixCls}-drawer-title-noBack`]: !showCloseIcon,
                  },
                )}
              >
                <Title title={title} />
              </div>
            </div>
            <div className={`${prefixCls}-drawer-content`}>{children}</div>
          </div>
        </Drawer>
      </ConfigProvider>
    </div>
  );
};

export default Index;
