@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-drawer-head {
    margin-bottom: 26px;
    display: flex;
    align-items: center;
  }

  &-drawer-title {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.4px;
    color: #18253d;
    text-align: center;
    flex: 1;
    margin-left: -32px;
  }

  &-drawer-title-noBack {
    margin-left: 0;
  }

  &-drawer-back-btn {
    color: var(--otakus-color-text-description);

    &:hover {
      color: var(--otakus-button-default-hover-border-color);
    }

    &:focus:active:focus-within {
      color: var(--otakus-button-default-active-border-color);
    }
  }

  &-drawer {
    .otakus-drawer-header {
      display: none;
    }

    .otakus-drawer-body {
      padding: 0;
    }
  }

  &-drawer-xs {
    .otakus-drawer-header {
      display: none;
    }

    .otakus-drawer-body {
      padding: var(--otakus-padding-md);
    }
  }
}
