@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-message {
    margin-top: calc(50vh - 60px);
    transform: translateY(-50%);

    .otakus-message-notice-content {
      background: var(--otakus-color-bg-spotlight) !important;
      color: var(--otakus-color-text-light-solid) !important;
      max-width: 360px;
    }
  }

  @media (min-width: 1280px) {
    &-message {
      margin-left: 50vw;
    }
  }

  @media (max-width: 1279px) {
    &-message {
      .otakus-message-notice-content {
        max-width: calc(100vw - 40px) !important;
      }
    }

    &-message-center {
      .otakus-message-notice-content {
        max-width: calc(100vw - 40px) !important;
      }
    }
  }

  &-message-center {
    margin-top: calc(50vh - 60px);

    .otakus-message-notice-content {
      background: var(--otakus-color-bg-spotlight) !important;
      color: var(--otakus-color-text-light-solid) !important;
      max-width: 360px;
    }
  }
}
