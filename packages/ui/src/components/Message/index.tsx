import { ConfigProvider, message, useTheme } from '@otakus/design';
import {
  MESSAGE_ACTION,
  MESSAGE_DESTROY_ACTION,
  prefixCls,
  subscribe,
} from '@iam/login-shared';
import { useEffect } from 'react';
import './index.less';
import {
  CheckCircleFilled,
  CloseCircleFilled,
  ExclamationCircleFilled,
  InfoCircleFilled,
  LoadingOutlined,
} from '@otakus/icons';

interface MessageProps {
  center?: boolean;
}

const Icon = (type: string) => {
  const marginRight = '8px';
  if (type === 'loading') {
    return (
      <LoadingOutlined
        style={{ color: 'rgba(146, 168, 255, 1)', marginRight }}
      />
    );
  }
  if (type === 'success') {
    return (
      <CheckCircleFilled
        style={{ color: 'rgba(75, 210, 122, 1)', marginRight }}
      />
    );
  }
  if (type === 'warning') {
    return (
      <ExclamationCircleFilled
        style={{ color: 'rgba(253, 166, 64, 1)', marginRight }}
      />
    );
  }
  if (type === 'info') {
    return (
      <InfoCircleFilled
        style={{ color: 'rgba(146, 168, 255, 1)', marginRight }}
      />
    );
  }
  if (type === 'error') {
    return (
      <CloseCircleFilled
        style={{ color: 'rgba(255, 141, 129, 1)', marginRight }}
      />
    );
  }
};

const Index = (props: MessageProps) => {
  const [api, contextHolder] = message.useMessage();
  const { center = false } = props;
  const token = useTheme();
  const messageClassName = center
    ? `${prefixCls}-message-center`
    : `${prefixCls}-message`;
  useEffect(() => {
    const subDestroy = subscribe(() => {
      api.destroy();
    }, MESSAGE_DESTROY_ACTION);
    const sub = subscribe(({ type, props }) => {
      const { resolveCallback, ...rest } = props;
      api.destroy();
      api[type]({
        ...rest,
        icon: Icon(type),
        duration: 2,
        className: messageClassName,
      }).then(resolveCallback);
    }, MESSAGE_ACTION);
    return () => {
      sub.unsubscribe();
      subDestroy.unsubscribe();
    };
  }, [api, messageClassName]);
  return (
    <ConfigProvider theme={{ token: { motion: false } }}>
      {contextHolder}
    </ConfigProvider>
  );
};

export default Index;
