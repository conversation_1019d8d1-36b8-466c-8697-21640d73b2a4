@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-switch-lang-wrapper {
    width: 72px;
    height: 32px;
    padding: 4px;
    border-radius: 8px;
    border: 1px solid #e0e0e7;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--otakus-color-bg-container);
  }

  &-switch-lang-item {
    width: 32px;
    height: 24px;
    color: var(--otakus-color-text-description);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
  }

  &-switch-lang-item:first-child {
    margin-right: 4px;
  }

  &-switch-lang-item:hover {
    border-radius: 6px;
    background: var(--otakus-color-bg-container-disabled);
  }

  &-switch-lang-item-active {
    color: var(--otakus-color-text-heading);
    border-radius: 6px;
    background: var(--otakus-color-bg-container-disabled);
  }

  &-switch-lang-item-active:hover {
    background: var(--otakus-color-bg-text-active);
    color: var(--otakus-color-text-heading);
  }

  &-switch-lang-dropdown {
    .otakus-dropdown-menu {
      border-radius: 12px;
      padding: 0;

      .otakus-dropdown-menu-item {
        width: 150px;
        padding: 12px;
      }
    }
  }

  &-switch-lang-item-active-xs {
    color: var(--otakus-color-primary);
  }

  &-switch-lang-item-label {
    margin-right: 6px;
    font-size: 14px;
    font-weight: 500;
  }

  &-switch-lang-menu-item-xs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
  }
}
