import './index.less';
import {
  type Lang,
  prefixCls,
  useConfig,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Dropdown, useTheme } from '@otakus/design';
import cls from 'classnames';
import { useState } from 'react';
import {
  CaretDownOutlined,
  CaretUpOutlined,
  CheckOutlined,
} from '@otakus/icons';

const enumLang = [
  {
    label: '中',
    labelXs: '中文',
    value: 'zh-CN',
  },
  {
    label: 'EN',
    labelXs: 'English',
    value: 'en-US',
  },
];

// 此处不需要多语言
const Index = () => {
  const { handleLang, lang, showLang, setConfig } = useConfig();
  const { size } = useSize();
  const [menuVisible, setMenuVisible] = useState(false);
  const token = useTheme();
  // const style = useScaleStyle('right center');
  const handleChange = (val: Lang) => {
    handleLang && handleLang(val);
    setConfig({
      lang: val,
    });
  };

  const enumLangForXs = enumLang.map((item, index) => {
    return {
      key: index + '',
      label: (
        <div className={`${prefixCls}-switch-lang-menu-item-xs`}>
          {item.labelXs}
          {lang === item.value ? (
            <CheckOutlined style={{ color: token.colorPrimary }} />
          ) : null}
        </div>
      ),
      labelXs: item.labelXs,
      value: item.value,
    };
  });

  const Icon = menuVisible ? (
    <CaretUpOutlined />
  ) : (
    <CaretDownOutlined style={{ color: 'rgba(2, 6, 43, 0.43)' }} />
  );

  const onOpenChange = (open: boolean) => {
    setMenuVisible(open);
  };

  const handleMenuClick = ({ key }) => {
    const lang = enumLangForXs.filter((e) => e.key === key)[0]
      .value as unknown as Lang;
    handleChange(lang);
  };

  if (!showLang) {
    return <div style={{ width: '72px' }} />;
  }
  if (size === ViewportSize.xs) {
    return (
      <div className={`${prefixCls}-switch-lang-wrapper-xs`}>
        <Dropdown
          menu={{ items: enumLangForXs, onClick: handleMenuClick }}
          overlayClassName={`${prefixCls}-switch-lang-dropdown`}
          onOpenChange={onOpenChange}
        >
          <span
            className={cls(`${prefixCls}-switch-lang-item-xs`, {
              [`${prefixCls}-switch-lang-item-active-xs`]: menuVisible,
            })}
          >
            <span className={`${prefixCls}-switch-lang-item-label`}>
              {enumLangForXs.filter((e) => e.value === lang)[0]?.labelXs}
            </span>
            {Icon}
          </span>
        </Dropdown>
      </div>
    );
  }
  return (
    <div className={`${prefixCls}-switch-lang-wrapper`}>
      {enumLang.map((item) => (
        <div
          className={cls(`${prefixCls}-switch-lang-item`, {
            [`${prefixCls}-switch-lang-item-active`]: lang === item.value,
          })}
          key={item.value}
          onClick={() => handleChange(item.value as Lang)}
        >
          {item.label}
        </div>
      ))}
    </div>
  );
};

export default Index;
