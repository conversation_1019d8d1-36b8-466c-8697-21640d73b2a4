@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-step-wrapper {
    margin-bottom: 20px;
  }

  &-otp-title {
    width: 100%;
  }

  &-otp-title-wrapper {
    margin-bottom: 36px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  &-otp-title-wrapper-xs {
    margin-bottom: 24px;
  }

  &-otp-block {
    width: 360px;
    margin-bottom: 24px;
    margin-top: -52px;
  }

  &-otp-block-xs {
    //padding: 0 20px;
    width: 100%;
    margin-top: unset;
  }

  &-otp-spin {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-otp-root {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-otp-root-xs {
    width: 100%;
    height: 100%;
    margin-top: 55px;
    display: block;
  }

  //&-otp-spin-xs {
  //  width: 100%;
  //  height: 100%;
  //  margin-top: 55px;
  //
  //  .otakus-spin-container {
  //    width: 100%;
  //    height: 100%;
  //    display: block;
  //  }
  //}

  &-otp-title-xs {
    font-size: 24px;
    line-height: 32px;

    .@{info-iam-login-prefix-cls}-title {
      //font-size: var(--otakus-font-size-heading-3);
      line-height: var(--otakus-line-height-heading-3);
    }
  }

  &-otp-step-wrapper-xs {
    margin-bottom: 12px;
  }
}
