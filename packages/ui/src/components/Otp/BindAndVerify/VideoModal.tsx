import { prefixCls, useLocale, useSize, ViewportSize } from '@iam/login-shared';
import { useBoolean } from 'ahooks';
import { Modal } from '@otakus/design';
import './index.less';

const Index = () => {
  const __ = useLocale();
  const [open, { toggle }] = useBoolean(true);
  const { size } = useSize();

  if (size === ViewportSize.xs) {
    return null;
  }

  return (
    <Modal
      open={open}
      onCancel={toggle}
      okText={__('otp:bind:verify:video:submit:text')}
      onOk={() =>
        window.open('https://km.mihoyo.com/doc/mh0fv7i22pd0', '_blank')
      }
      cancelText={__('otp:bind:verify:video:cancel:text')}
      width={400}
      centered={false}
      className={`${prefixCls}-bind-verify-video-modal`}
      mask={false}
      title={__('otp:bind:verify:video:title')}
      keyboard={false}
      maskClosable={false}
      style={{
        position: 'fixed',
        top: 'auto',
        bottom: '24px',
        right: '24px',
        left: 'auto',
        margin: 0,
        paddingBottom: 0,
      }}
      getContainer={() => document.body}
      wrapClassName={`${prefixCls}-bind-verify-video-modal-wrap`}
    >
      <div>
        <video
          src={'https://info-static.mihoyo.com/videos/otp-bind-guide.mp4'}
          width={352}
          height={198}
          autoPlay={true}
          loop={true}
          style={{
            borderRadius: 8,
          }}
        ></video>
      </div>
    </Modal>
  );
};

export default Index;
