import { CopyOutlined } from '@otakus/icons';
import ClipboardJS from 'clipboard';
import { useEffect, useRef } from 'react';
import { message, prefixCls, useLocale } from '@iam/login-shared';
import './index.less';

interface OtpSecretBindItemProps {
  label: string;
  value: string;
  copyId?: string;
}

const Index = (props: OtpSecretBindItemProps) => {
  const { label, value, copyId } = props;
  const clipboardRef = useRef<ClipboardJS>();
  const __ = useLocale();
  useEffect(() => {
    clipboardRef.current = new ClipboardJS(`#${copyId}`);
    clipboardRef.current.on('success', function (e) {
      message.success(__('otp:copy:success'));
      e.clearSelection();
    });

    clipboardRef.current.on('error', function () {
      message.error(__('otp:copy:fail'));
    });
    return () => {
      clipboardRef.current?.destroy();
      clipboardRef.current = null;
    };
  }, [copyId]);
  return (
    <div className={`${prefixCls}-otp-secret-bind-item`}>
      <div className={`${prefixCls}-otp-secret-bind-item-content`}>
        <span className={`${prefixCls}-otp-secret-bind-item-label`}>
          {label}
        </span>
        <span className={`${prefixCls}-otp-secret-bind-item-value`}>
          {value}
        </span>
      </div>
      <div
        id={copyId}
        data-clipboard-text={value}
        className={`${prefixCls}-otp-bind-copy`}
      >
        <CopyOutlined />
      </div>
    </div>
  );
};

export default Index;
