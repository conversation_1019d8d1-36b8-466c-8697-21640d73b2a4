import {
  BindMethod,
  getIsWaveEnv,
  getTLD,
  INVALID_TOTP_VERIFY_CODE,
  message,
  MIHOYO_DOMAIN,
  OtpSecret,
  prefixCls,
  Response,
  ServiceErrorCodes,
  useAssets,
  useConfig,
  useLocale,
  useSize,
  V3OpenWithBrowserInWave,
  ViewportSize,
} from '@iam/login-shared';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { Button, Form, QRCode } from '@otakus/design';
import Secret from './Secret';
import './index.less';
import qrWrapper from '@ui/assets/qrcode-wrapper.webp';
import StepTag from './StepTag';
import { FormItem, FormItems } from '@ui/components/Form/FormItem';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';
import { OTPRef } from 'antd/es/input/OTP';
import { handleFormErrorTooltip } from '@ui/components/LoginForm/helper';
import classNames from 'classnames';
import cls from 'classnames';
import VideoModal from './VideoModal';
import { FileExportOutlined } from '@otakus/icons';

const qrCodeWrapperStyle = {
  backgroundImage: `url(${qrWrapper})`,
};

interface OtpBindProps {
  bindMethod: BindMethod;
  handleBindMethod: (bindMethod: BindMethod) => void;
  otpSecret: OtpSecret;
  handleStep: () => void;
  bindOtp: (dcode: string) => Promise<Response>;
  bindOtpLoading: boolean;
}

const Index = (props: OtpBindProps) => {
  const {
    bindMethod,
    handleBindMethod,
    otpSecret,
    handleStep,
    bindOtp,
    bindOtpLoading,
  } = props;
  const __ = useLocale();
  const { size } = useSize();
  const { env } = useConfig();
  const codeInputRef = useRef<OTPRef>(null);
  const switchPreFix = useMemo(() => {
    if (bindMethod === BindMethod.SCAN) {
      return __('otp:scan:bind:switch:prefix');
    } else {
      return __('otp:secret:bind:switch:prefix');
    }
  }, [bindMethod, __]);

  const switchText = useMemo(() => {
    if (bindMethod === BindMethod.SCAN) {
      return __('otp:scan:bind:switch');
    } else {
      return __('otp:secret:bind:switch');
    }
  }, [bindMethod, __]);

  const desc = useMemo(() => {
    return bindMethod === BindMethod.SCAN
      ? __('otp:scan:bind:desc')
      : __('otp:secret:bind:desc');
  }, [bindMethod, __]);

  const switchBindMethod = () => {
    handleBindMethod(
      bindMethod === BindMethod.SCAN ? BindMethod.SECRET : BindMethod.SCAN,
    );
  };
  const mhyIcon = useAssets('mhy-qr-icon.png');
  const cogIcon = useAssets('cog-qr-icon.png');
  const tld = getTLD();
  const icon = tld === MIHOYO_DOMAIN ? mhyIcon : cogIcon;
  const [form] = Form.useForm();
  const values = Form.useWatch([], form);
  const tooltipErrorMap = useMemo(() => {
    return new Map<ServiceErrorCodes, string | [string, () => void]>([
      [INVALID_TOTP_VERIFY_CODE, 'decode'],
    ]);
  }, []);

  const handleSubmit = async (decode: string) => {
    const data = await bindOtp(decode);
    if (data?.code == 0) {
      handleStep();
    } else {
      handleFormErrorTooltip(
        form,
        {
          code: data?.code,
          message: data?.message,
        },
        tooltipErrorMap,
      );
      if (data?.code === INVALID_TOTP_VERIFY_CODE) {
        form.setFieldValue('decode', '');
        codeInputRef.current?.focus();
      }
    }
  };

  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: 'decode', errors: [] }]);
  }, []);

  useEffect(() => {
    if (values?.decode && values?.decode?.length === 6) {
      Promise.resolve().then(() => handleSubmit(values?.decode));
    }
  }, [values]);

  const handlePlayer = async () => {
    const url = 'https://mhyurl.cn/7bECEEuW2';
    if (getIsWaveEnv()) {
      const result = await V3OpenWithBrowserInWave(env, url);
      if (!result) {
        message.error('调用wave api 失败');
        // window.open(url, '_blank');
      }
    } else {
      window.open(url, '_blank');
    }
  };
  return (
    <div className={`${prefixCls}-otp-bind-wrapper`}>
      {/*<span className={`${prefixCls}-otp-bind-desc`}>{desc}</span>*/}
      <div
        className={classNames(`${prefixCls}-otp-bind-step-wrapper`, {
          [`${prefixCls}-otp-bind-step-wrapper-mb20`]:
            bindMethod === BindMethod.SCAN,
          [`${prefixCls}-otp-bind-step-wrapper-mb16`]:
            bindMethod === BindMethod.SECRET,
        })}
      >
        <StepTag text={__('otp:bind:verify:step:tag:1')} />
        <div className={`${prefixCls}-otp-bind-step-text`}>
          {bindMethod === BindMethod.SCAN
            ? __('otp:bind:verify:step:1:scan:suffix')
            : __('otp:bind:verify:step:1:secret:suffix')}
          <span
            className={`${prefixCls}-otp-bind-switch-prefix-link`}
            onClick={switchBindMethod}
          >
            {switchText}
          </span>
        </div>
      </div>
      {bindMethod === BindMethod.SCAN ? (
        <div
          className={`${prefixCls}-otp-bind-qr-wrapper`}
          style={qrCodeWrapperStyle}
        >
          <QRCode
            value={otpSecret?.qrcode}
            size={154}
            // icon={icon}
            iconSize={54}
            errorLevel="Q"
            bordered={false}
          />
          <div className={`${prefixCls}-otp-bind-qr-icon`}>
            <img src={icon} alt={'qr-icon'} />
          </div>
        </div>
      ) : (
        <div className={`${prefixCls}-otp-bind-secret-wrapper`}>
          <Secret
            label={__('otp:secret:name:label')}
            value={otpSecret?.account}
            copyId={'account'}
          />
          <Secret
            label={__('otp:secret:secret:label')}
            value={otpSecret?.secret}
            copyId={'secret'}
          />
        </div>
      )}

      <div
        className={`${prefixCls}-otp-bind-step-wrapper ${prefixCls}-otp-bind-step-wrapper-mt24 ${prefixCls}-otp-bind-step-wrapper-mb12`}
      >
        <StepTag text={__('otp:bind:verify:step:tag:2')} />
        <div className={`${prefixCls}-otp-bind-step-text`}>
          {__('otp:bind:verify:step:2:text')}
        </div>
      </div>

      <Form
        form={form}
        name={'otp-verify'}
        className={`${prefixCls}-login-form-wrapper`}
        disabled={bindOtpLoading}
      >
        <FormItems>
          <FormItem
            name={'decode'}
            validateFirst
            wrap
            rules={[
              { required: true, message: __('otp:verify:message') },
              { whitespace: true, message: __('otp:verify:message') },
            ]}
            validateTrigger={false}
          >
            <CodeInput
              onCharChange={onCodeCharChange}
              codeInputRef={codeInputRef}
              autoFocus
              onPaste={handleOtpPaste('decode', form)}
            />
          </FormItem>
        </FormItems>
      </Form>
      <div
        className={cls(`${prefixCls}-otp-bind-verify-player-text-wrapper`, {
          [`${prefixCls}-otp-bind-verify-player-text-wrapper`]:
            size === ViewportSize.xs,
        })}
      >
        {size !== ViewportSize.xs && <FileExportOutlined />}
        <Button
          type={size === ViewportSize.xs ? 'default' : 'link'}
          onClick={handlePlayer}
          className={cls({
            [`${prefixCls}-otp-bind-verify-player-btn-xs`]:
              size === ViewportSize.xs,
          })}
        >
          {size === ViewportSize.xs && (
            <FileExportOutlined style={{ marginRight: '4px' }} />
          )}
          {__('otp:bind:verify:player:btx:text')}
        </Button>
      </div>
      <div className={`${prefixCls}-otp-verify-form-tips`}>
        {__('otp:verify::error:tips:v2')}
        {/*<Tooltip placement={'bottom'} title={__('otp:verify:tips:message:v2')}>*/}
        {/*  <div className={`${prefixCls}-otp-verify-form-question-icon`}>*/}
        {/*    <QuestionCircleOutlined />*/}
        {/*  </div>*/}
        {/*</Tooltip>*/}
        <Button
          type={'link'}
          onClick={() =>
            window.open('https://km.mihoyo.com/doc/mh0fv7i22pd0', '_blank')
          }
          className={`${prefixCls}-otp-bind-verify-km-btn`}
        >
          {__('otp:verify::error:tips:doc:v2')}
        </Button>
      </div>
      <VideoModal />
      {/*<Button*/}
      {/*  className={`${prefixCls}-otp-bind-btn`}*/}
      {/*  size="large"*/}
      {/*  type="primary"*/}
      {/*  onClick={() => handleStep()}*/}
      {/*>*/}
      {/*  {__('otp:bind:btn')}*/}
      {/*</Button>*/}
    </div>
  );
};

export default Index;
