@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-bind-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .otakus-qrcode {
      img {
      }
    }
  }

  &-otp-bind-desc {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
  }

  &-otp-bind-switch-desc {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--otakus-color-text-description);
    font-size: 14px;
  }

  &-otp-bind-switch-prefix {
    margin-right: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  &-otp-bind-switch-prefix-link {
    color: rgba(78, 95, 246, 1);
    cursor: pointer;
  }

  &-otp-secret-bind-item {
    width: 100%;
    border-radius: 12px;
    border: 1px solid var(--otakus-color-split);
    background: var(--otakus-color-fill-alter);
    padding: 16px 20px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    gap: 8px;
  }

  &-otp-secret-bind-item-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  &-otp-secret-bind-item-label {
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
  }

  &-otp-secret-bind-item-value {
    font-size: 16px;
    color: var(--otakus-color-text-heading);
    font-weight: 500;
    word-break: break-all;
  }

  &-otp-bind-secret-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  &-otp-bind-copy {
    border-radius: var(--otakus-border-radius);
    border: 1px solid
      var(
        --Components-Button-Component-defaultBorderColor,
        rgba(17, 24, 78, 0.13)
      );
    background: var(--Components-Button-Component-defaultBg, #fff);
    box-shadow: 0 2px 1px 0
      var(--palette-alpha-on-light-slate-98, rgba(22, 22, 122, 0.03));
    padding: 0 4px;
    cursor: pointer;

    &:hover {
      color: var(--otakus-color-primary-hover);
      border: 1px solid var(--otakus-color-primary-hover);
      background: var(--Components-Button-Component-defaultBg, #fff);
      box-shadow: 0 2px 1px 0
        var(--palette-alpha-on-light-slate-98, rgba(22, 22, 122, 0.03));
    }

    &:focus:active:focus-within {
      color: var(--otakus-color-primary-active);
      border: 1px solid var(--otakus-color-primary-active);
      background: var(--Components-Button-Component-defaultBg, #fff);
      box-shadow: 0 2px 1px 0
        var(--palette-alpha-on-light-slate-98, rgba(22, 22, 122, 0.03));
    }
  }

  &-otp-bind-qr-wrapper {
    position: relative;
    background-size: 100% 100%;
    width: 188px;
    height: 188px;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-otp-bind-qr-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 8px;
    border: 1px solid var(--otakus-color-split);
    width: 52px;
    height: 52px;

    img {
      width: 52px;
      height: 52px;
    }
  }

  &-otp-bind-btn {
    width: 100%;
    height: 54px;
    margin-top: 24px;
    border-radius: 12px;
  }

  &-otp-bind-verify-step-tag-wrapper {
    border-radius: 12px;
    padding: 0 8px;
    background: var(--otakus-color-primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    margin-top: 2px;
    min-width: 54px;
  }

  &-otp-bind-verify-step-tag {
    color: var(--otakus-color-primary);
    font-size: 12px;
    font-weight: 500;
  }

  &-otp-bind-step-wrapper {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  &-otp-bind-step-text {
    margin-left: 6px;
    color: var(--otakus-color-text-secondary);
  }

  &-otp-bind-step-wrapper-mb20 {
    margin-bottom: 20px;
  }

  &-otp-bind-step-wrapper-mb16 {
    margin-bottom: 16px;
  }

  &-otp-bind-step-wrapper-mb12 {
    margin-bottom: 12px;
  }

  &-otp-bind-step-wrapper-mt24 {
    margin-top: 24px;
  }

  &-otp-bind-verify-player-text-wrapper {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4e5ff6;

    button {
      padding: 0 0 0 4px;
    }
  }

  &-bind-verify-video-modal {
    position: fixed !important;
    top: auto !important;
    bottom: 24px !important;
    right: 24px !important;
    left: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;

    .otakus-modal-wrap {
      position: fixed !important;
      top: auto !important;
      bottom: 24px !important;
      right: 24px !important;
      left: auto !important;
      transform: none !important;
    }

    .otakus-modal {
      position: relative !important;
      top: auto !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    .otakus-modal-content {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .otakus-modal-body {
      display: flex;
      align-items: center;
      justify-content: center;
      //padding: 16px;
    }
  }

  &-bind-verify-video-modal-wrap {
    position: fixed !important;
    top: auto !important;
    bottom: 24px !important;
    right: 24px !important;
    left: auto !important;
    width: 368px !important;
    height: auto !important;
    pointer-events: none !important;

    .otakus-modal {
      pointer-events: auto !important;
    }

    .otakus-modal-content {
      pointer-events: auto !important;
    }
  }

  &-otp-bind-verify-km-btn {
    padding: 0;
  }

  &-otp-bind-verify-player-btn-xs {
    width: 100%;
    height: 54px !important;
    border-radius: 12px;
    font-size: 16px;
  }

  &-otp-bind-verify-player-text-wrapper {
    width: 100%;
  }
}

.@{info-iam-login-prefix-cls}-bind-verify-video-modal.otakus-modal-root {
  position: fixed !important;
  top: auto !important;
  bottom: 24px !important;
  right: 24px !important;
  left: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
}
