import {
  BindMethod,
  FollowAuthMethods,
  GetFollowAuthMethods,
  getIsWaveEnv,
  message,
  OtpSecret,
  prefixCls,
  setVisible,
  useAuthOtp,
  useConfig,
  useLocale,
  useScaleStyle,
  useSize,
  V3OpenWithBrowserInWave,
  ViewportSize,
} from '@iam/login-shared';
import { useMemo } from 'react';
import App from './Applications';
import BindAndVerify from './BindAndVerify';
import Success from './Success';
import Error from './Error';
import Idp from './Idp';
import cls from 'classnames';
import './index.less';
import { Modal, Spin } from '@otakus/design';
import InnerWrapper from '@ui/components/Otp/InnerWrapper';
import Verify from '@ui/components/Otp/Verify';

const Index = () => {
  const {
    step,
    setStep,
    totalStep,
    title,
    bindMethod,
    setBindMethod,
    otpSecretRes,
    followAuthMethodRes,
    followAuthSendCode,
    followAuthSendCodeLoading,
    verifyFollowAuthCode,
    verifyFollowAuthCodeLoading,
    bindOtp,
    bindOtpLoading,
    errType,
    getOtpSecretLoading,
    resetOtp,
    otpExchangeConfirm,
    otpExchangeConfirmLoading,
    getExchangePreCheck,
    getOtpExchangeApprovalFormLink,
  } = useAuthOtp();
  const { size } = useSize();
  const { otpSourceRedirectUrl, env } = useConfig();
  const __ = useLocale();
  const _step = useMemo(() => {
    const result = otpSourceRedirectUrl ? step - 1 : step;
    return result.toString().padStart(2, '0');
  }, [otpSourceRedirectUrl, step]);
  const style = useScaleStyle();
  // 暂时先注释
  // useEffect(() => {
  //   const handle = (e: { preventDefault: () => void; returnValue: string }) => {
  //     e.preventDefault();
  //     e.returnValue = '';
  //     return '';
  //   };
  //   if (resetOtp && step === 5) {
  //     window.removeEventListener('beforeunload', handle);
  //   } else if ((!resetOtp && step === 4) || !!errType) {
  //     window.removeEventListener('beforeunload', handle);
  //   } else {
  //     window.addEventListener('beforeunload', handle);
  //   }
  // }, [step, errType, resetOtp]);

  const handleStep = (number = undefined) => {
    if (!number) {
      setStep(step + 1);
    } else {
      setStep(step + number);
    }
  };
  const handleBindMethod = (method: BindMethod) => {
    setBindMethod(method);
  };

  const _totalStep = useMemo(() => {
    const result = otpSourceRedirectUrl ? totalStep - 1 : totalStep;
    return result.toString().padStart(2, '0');
  }, [otpSourceRedirectUrl, totalStep]);

  const otpSecret = useMemo(() => {
    if (otpSecretRes?.code === 0) {
      return otpSecretRes?.data;
    }
    return {} as unknown as OtpSecret;
  }, [otpSecretRes]);

  const followAuthMethod = useMemo(() => {
    if (followAuthMethodRes?.code === 0) {
      return followAuthMethodRes?.data;
    }
    return {} as unknown as GetFollowAuthMethods;
  }, [followAuthMethodRes]);

  const handleSendCode = async (id: string) => {
    return await followAuthSendCode(id);
  };

  const verifyCode = async (
    method: FollowAuthMethods,
    method_id: string,
    code: string,
  ) => {
    return await verifyFollowAuthCode(method, method_id, code);
  };

  const handleBack = () => {
    handleStep(step - 1);
  };

  const goExchangeForm = async () => {
    const preCheckRes = await getExchangePreCheck();
    if (preCheckRes?.code === 0) {
      const isAllow = preCheckRes?.data?.is_allow_emp_code;
      if (isAllow) {
        const data = await getOtpExchangeApprovalFormLink();
        if (data?.code === 0) {
          const link = data?.data?.link;
          if (getIsWaveEnv()) {
            const result = await V3OpenWithBrowserInWave(env, link);
            if (!result) {
              message.error('调用wave api 失败');
              // window.open(url, '_blank');
            }
          } else {
            window.open(link, '_blank');
          }
        }
      } else {
        Modal.confirm({
          title: __('otp:verify:exchange:fail:modal:title'),
          content: __('otp:verify:exchange:fail:modal:content'),
          okText: __('otp:verify:exchange:fail:modal:btn:text'),
          cancelButtonProps: {
            style: {
              display: 'none',
            },
          },
          icon: null,
        });
      }
    }
  };

  if (getOtpSecretLoading) {
    return (
      <Spin
        spinning={getOtpSecretLoading}
        size={'large'}
        className={cls(`${prefixCls}-otp-spin`, {
          [`${prefixCls}-otp-spin-xs`]: size === ViewportSize.xs,
        })}
        delay={100}
      />
    );
  }
  if (resetOtp) {
    return (
      <InnerWrapper
        title={title}
        setStep={setStep}
        step={step}
        errType={errType}
        getOtpSecretLoading={getOtpSecretLoading}
        totalStep={totalStep}
        resetOtp={resetOtp}
      >
        {setVisible({
          visible: step === 1,
          component: (
            <Verify
              bindOtp={bindOtp}
              bindOtpLoading={bindOtpLoading}
              handleStep={handleStep}
              resetOtp={resetOtp}
              showTips={false}
              goExchangeForm={goExchangeForm}
            />
          ),
        })}
        {setVisible({
          visible: step === 2 && !errType,
          component: (
            <Idp
              handleStep={handleStep}
              handleSendCode={handleSendCode}
              followAuthMethod={followAuthMethod}
              verifyCode={verifyCode}
              verifyFollowAuthCodeLoading={verifyFollowAuthCodeLoading}
            />
          ),
        })}
        {setVisible({
          visible: step === 3 && !errType,
          component: <App handleStep={handleStep} />,
        })}
        {setVisible({
          visible: step === 4 && !errType,
          component: (
            <BindAndVerify
              bindMethod={bindMethod}
              handleBindMethod={handleBindMethod}
              otpSecret={otpSecret}
              handleStep={handleStep}
              bindOtp={otpExchangeConfirm}
              bindOtpLoading={otpExchangeConfirmLoading}
            />
          ),
        })}
        {setVisible({
          visible: step === 5,
          component: <Success resetOtp={resetOtp} />,
        })}
        {setVisible({
          visible: !!errType,
          component: <Error type={errType} />,
        })}
      </InnerWrapper>
    );
  }
  return (
    <InnerWrapper
      title={title}
      setStep={setStep}
      step={step}
      errType={errType}
      getOtpSecretLoading={getOtpSecretLoading}
      totalStep={totalStep}
      resetOtp={resetOtp}
    >
      {setVisible({
        visible: step === 1,
        component: (
          <Idp
            handleStep={handleStep}
            handleSendCode={handleSendCode}
            followAuthMethod={followAuthMethod}
            verifyCode={verifyCode}
            verifyFollowAuthCodeLoading={verifyFollowAuthCodeLoading}
          />
        ),
      })}
      {setVisible({
        visible: step === 2,
        component: <App handleStep={handleStep} />,
      })}
      {setVisible({
        visible: step === 3 && !errType,
        component: (
          <BindAndVerify
            bindMethod={bindMethod}
            handleBindMethod={handleBindMethod}
            otpSecret={otpSecret}
            handleStep={handleStep}
            bindOtp={bindOtp}
            bindOtpLoading={bindOtpLoading}
          />
        ),
      })}
      {setVisible({
        visible: step === 4,
        component: <Success />,
      })}
      {setVisible({
        visible: !!errType,
        component: <Error type={errType} />,
      })}
    </InnerWrapper>
  );
};

export default Index;
