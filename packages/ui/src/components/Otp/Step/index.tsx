import { prefixCls } from '@iam/login-shared';
import './index.less';

interface StepProps {
  cur: string | number;
  total: string | number;
}

const Index = (props: StepProps) => {
  const { cur, total } = props;
  if (cur <= 0) {
    return null;
  }
  return (
    <div className={`${prefixCls}-step-wrapper`}>
      <span className={`${prefixCls}-step-cur`}>{cur}</span>
      <span className={`${prefixCls}-step-divider`}>/</span>
      <span className={`${prefixCls}-step-total`}>{total}</span>
    </div>
  );
};

export default Index;
