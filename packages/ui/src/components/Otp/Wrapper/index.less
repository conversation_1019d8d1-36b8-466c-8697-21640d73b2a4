@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-wrapper {
    padding: 20px 24px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: linear-gradient(
        174deg,
        rgba(78, 95, 246, 0.06) 7.78%,
        rgba(78, 95, 246, 0.02) 16.38%,
        rgba(78, 95, 246, 0) 20.98%
      ),
      #fff;

    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }
  }

  &-otp-wrapper-xs {
    padding: 12px 20px;
    background: linear-gradient(
        174deg,
        rgba(78, 95, 246, 0.06) 7.78%,
        rgba(78, 95, 246, 0.02) 16.38%,
        rgba(78, 95, 246, 0) 20.98%
      ),
      #fff;
  }

  &-otp-head {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    position: relative;
    z-index: 999;
  }

  &-otp-head-left {
    display: flex;
    align-items: center;

    .otakus-divider-vertical {
      margin-left: 20px;
      margin-right: 20px;
    }
  }

  &-otp-head-left-xs {
    overflow: hidden;
    flex: 1;

    .otakus-divider-vertical {
      margin-left: 12px;
      margin-right: 12px;
    }
  }

  &-otp-head-right {
    width: fit-content;
  }

  &-otp-head-title {
    font-size: 20px;
    font-weight: 600;
  }

  &-otp-wrapper-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    flex-direction: column;
  }

  &-otp-head-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;

    div {
      height: 32px;
    }

    svg {
      height: 32px;
      width: auto;
    }
  }

  &-otp-head-logo-xs {
    height: 24px;

    div {
      height: 24px;
    }

    svg {
      height: 24px;
      width: auto;
    }
  }

  &-otp-head-scrolling-container {
    flex: 1;
    overflow: hidden;
    position: relative;

    @keyframes scroll-left {
      0% {
        transform: translateX(100%);
      }
      100% {
        transform: translateX(-100%);
      }
    }
  }

  &-otp-head-scrolling-text {
    display: inline-block;
    white-space: nowrap;
    animation: scroll-left 6s linear infinite;
  }

  &-otp-head-title-xs {
    font-size: 16px;
  }

  &-otp-head-right-zh {
    min-width: 48px;
  }

  &-otp-head-right-en {
    min-width: 68px;
  }

  &-otp-scrolling-text-no-scroll {
    animation: none;
  }

  &-otp-scrolling-text-no-hidden {
    opacity: 0;
  }
}
