import {
  getTLD,
  prefixCls,
  ServerDomain,
  useAssets,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
// import mihoyoLogo from '@ui/assets/mihoyo-logo.svg';
// import cogLogo from '@ui/assets/cognosphere-logo.svg';
import './index.less';
import { FC, useEffect, useState } from 'react';
import { ReactSVG } from 'react-svg';
import { Divider } from '@otakus/design';
import SwitchLang from '@ui/components/SwitchLang';
import cls from 'classnames';

export interface OtpWrapperProps {
  title: string;
}

const Index: FC<OtpWrapperProps> = (props) => {
  const { children, title } = props;
  const __ = useLocale();
  const tld = getTLD();
  const { width, size } = useSize();
  const { lang } = useConfig();
  const mihoyoLogo = useAssets('mihoyo-logo.svg');
  const cogLogo = useAssets('cognosphere-logo.svg');
  const logo = tld === ServerDomain['mihoyo.com'] ? mihoyoLogo : cogLogo;
  const [scroll, setScroll] = useState<boolean>(undefined);

  useEffect(() => {
    setTimeout(() => {
      const scrollingText = document.querySelector(`#otp-head-scrolling-text`);
      const scrollingWrapper = document.querySelector(
        `#otp-head-scrolling-wrapper`,
      );
      if (scrollingText?.clientWidth <= scrollingWrapper?.clientWidth) {
        setScroll(true);
      } else {
        setScroll(false);
      }
    }, 200);
  }, []);

  return (
    <div
      className={cls(`${prefixCls}-otp-wrapper`, {
        [`${prefixCls}-otp-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <div className={`${prefixCls}-otp-head`}>
        <div
          className={cls(`${prefixCls}-otp-head-left`, {
            [`${prefixCls}-otp-head-left-xs`]: size === ViewportSize.xs,
          })}
        >
          <ReactSVG
            src={logo}
            className={cls(`${prefixCls}-otp-head-logo`, {
              [`${prefixCls}-otp-head-logo-xs`]: size === ViewportSize.xs,
            })}
          />
          <Divider
            type="vertical"
            orientationMargin={20}
            orientation={'center'}
          />
          <div
            className={`${prefixCls}-otp-head-scrolling-container`}
            id={'otp-head-scrolling-wrapper'}
          >
            {
              <div
                id={'otp-head-scrolling-text'}
                className={cls(
                  `${prefixCls}-otp-head-scrolling-text ${prefixCls}-otp-head-title`,
                  {
                    [`${prefixCls}-otp-head-title-xs`]:
                      size === ViewportSize.xs,
                    [`${prefixCls}-otp-scrolling-text-no-scroll`]: scroll,
                    [`${prefixCls}-otp-scrolling-text-no-hidden`]:
                      scroll === undefined,
                  },
                )}
              >
                {title}
              </div>
            }
          </div>
        </div>
        <div
          className={cls(`${prefixCls}-otp-head-right`, {
            [`${prefixCls}-otp-head-right-zh`]: lang === 'zh-CN',
            [`${prefixCls}-otp-head-right-en`]: lang === 'en-US',
          })}
        >
          <SwitchLang />
        </div>
      </div>
      <div className={`${prefixCls}-otp-wrapper-content`}>{children}</div>
    </div>
  );
};

export default Index;
