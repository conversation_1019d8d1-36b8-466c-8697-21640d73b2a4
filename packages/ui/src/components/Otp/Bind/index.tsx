import {
  BindMethod,
  getTLD,
  MIHOYO_DOMAIN,
  OtpSecret,
  prefixCls,
  useAssets,
  useLocale,
} from '@iam/login-shared';
import { useMemo, useState } from 'react';
import { Button, QRCode, Tooltip } from '@otakus/design';
import Secret from './Secret';
import './index.less';
import qrWrapper from '@ui/assets/qrcode-wrapper.webp';
import { useTimeout } from 'ahooks';

const qrCodeWrapperStyle = {
  backgroundImage: `url(${qrWrapper})`,
};

interface OtpBindProps {
  bindMethod: BindMethod;
  handleBindMethod: (bindMethod: BindMethod) => void;
  otpSecret: OtpSecret;
  handleStep: () => void;
}

const Index = (props: OtpBindProps) => {
  const { bindMethod, handleBindMethod, otpSecret, handleStep } = props;
  const __ = useLocale();
  const [tipsOpen, setTipsOpen] = useState<boolean>(false);

  useTimeout(() => {
    setTipsOpen(true);
  }, 10000);

  const switchPreFix = useMemo(() => {
    if (bindMethod === BindMethod.SCAN) {
      return __('otp:scan:bind:switch:prefix');
    } else {
      return __('otp:secret:bind:switch:prefix');
    }
  }, [bindMethod, __]);

  const switchText = useMemo(() => {
    if (bindMethod === BindMethod.SCAN) {
      return __('otp:scan:bind:switch');
    } else {
      return __('otp:secret:bind:switch');
    }
  }, [bindMethod, __]);

  const desc = useMemo(() => {
    return bindMethod === BindMethod.SCAN
      ? __('otp:scan:bind:desc')
      : __('otp:secret:bind:desc');
  }, [bindMethod, __]);

  const switchBindMethod = () => {
    handleBindMethod(
      bindMethod === BindMethod.SCAN ? BindMethod.SECRET : BindMethod.SCAN,
    );
  };
  const mhyIcon = useAssets('mhy-qr-icon.png');
  const cogIcon = useAssets('cog-qr-icon.png');
  const tld = getTLD();
  const icon = tld === MIHOYO_DOMAIN ? mhyIcon : cogIcon;
  return (
    <div className={`${prefixCls}-otp-bind-wrapper`}>
      <span className={`${prefixCls}-otp-bind-desc`}>{desc}</span>
      {bindMethod === BindMethod.SCAN ? (
        <div
          className={`${prefixCls}-otp-bind-qr-wrapper`}
          style={qrCodeWrapperStyle}
        >
          <QRCode
            value={otpSecret?.qrcode}
            size={154}
            // icon={icon}
            iconSize={54}
            errorLevel="Q"
            bordered={false}
          />
          <div className={`${prefixCls}-otp-bind-qr-icon`}>
            <img src={icon} alt={'qr-icon'} />
          </div>
        </div>
      ) : (
        <div className={`${prefixCls}-otp-bind-secret-wrapper`}>
          <Secret
            label={__('otp:secret:name:label')}
            value={otpSecret?.account}
            copyId={'account'}
          />
          <Secret
            label={__('otp:secret:secret:label')}
            value={otpSecret?.secret}
            copyId={'secret'}
          />
        </div>
      )}
      <div className={`${prefixCls}-otp-bind-switch-desc`}>
        <span className={`${prefixCls}-otp-bind-switch-prefix`}>
          {switchPreFix}
        </span>
        <span
          className={`${prefixCls}-otp-bind-switch-prefix-link`}
          onClick={switchBindMethod}
        >
          {switchText}
        </span>
      </div>
      <Tooltip
        title={__('otp:bind:btn:tips')}
        placement={'bottom'}
        open={tipsOpen}
        overlayStyle={{ maxWidth: '300px' }}
      >
        <Button
          className={`${prefixCls}-otp-bind-btn`}
          size="large"
          type="primary"
          onClick={() => handleStep()}
        >
          {__('otp:bind:btn')}
        </Button>
      </Tooltip>
    </div>
  );
};

export default Index;
