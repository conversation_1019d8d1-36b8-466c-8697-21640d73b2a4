import {
  prefixCls,
  useConfig,
  useLocale,
  useScaleStyle,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import Wrapper from '../Wrapper';
import Step from '../Step';
import { FC, useMemo } from 'react';
import Title from '@ui/components/Title';
import cls from 'classnames';
import '../index.less';
import { Spin } from '@otakus/design';
import BackButton from '@ui/components/BackButton';
import { OtpErrorType } from '@iam/login-shared/dist/esm';

interface OTPInnerWrapperProps {
  step: number;
  setStep: (value: React.SetStateAction<number>) => void;
  totalStep: number;
  title: string;
  errType: OtpErrorType | '';
  getOtpSecretLoading: boolean;
  resetOtp?: boolean;
}

const Index: FC<OTPInnerWrapperProps> = (props) => {
  const {
    children,
    step,
    setStep,
    totalStep,
    title,
    errType,
    getOtpSecretLoading,
    resetOtp,
  } = props;
  const { size } = useSize();
  const { otpSourceRedirectUrl } = useConfig();
  const __ = useLocale();
  const _step = useMemo(() => {
    const result = otpSourceRedirectUrl ? step - 1 : step;
    return result.toString().padStart(2, '0');
  }, [otpSourceRedirectUrl, step]);
  const style = useScaleStyle();
  // useEffect(() => {
  //   const handle = (e: { preventDefault: () => void; returnValue: string }) => {
  //     e.preventDefault();
  //     e.returnValue = '';
  //     return '';
  //   };
  //   window.addEventListener('beforeunload', handle);
  //   if (step === 4 || !!errType) {
  //     window.removeEventListener('beforeunload', handle);
  //   }
  // }, [step, errType]);

  const handleStep = (number = undefined) => {
    if (!number) {
      setStep(step + 1);
    } else {
      setStep(number);
    }
  };

  const _totalStep = useMemo(() => {
    const result = otpSourceRedirectUrl ? totalStep - 1 : totalStep;
    return result.toString().padStart(2, '0');
  }, [otpSourceRedirectUrl, totalStep]);

  const handleBack = () => {
    handleStep(step - 1);
  };

  const showBackButton = useMemo(() => {
    if (!resetOtp) {
      return step > 2 && !errType && step !== 4;
    } else {
      return !errType && step === 4;
    }
  }, [resetOtp, step, errType]);
  if (getOtpSecretLoading) {
    return (
      <Spin
        spinning={getOtpSecretLoading}
        size={'large'}
        className={cls(`${prefixCls}-otp-spin`, {
          [`${prefixCls}-otp-spin-xs`]: size === ViewportSize.xs,
        })}
        delay={100}
      />
    );
  }
  return (
    <Wrapper title={__('otp:wrapper:title')}>
      <div
        className={cls(`${prefixCls}-otp-root`, {
          [`${prefixCls}-otp-root-xs`]: size === ViewportSize.xs,
        })}
        style={style}
      >
        <div
          className={cls(`${prefixCls}-otp-block`, {
            [`${prefixCls}-otp-block-xs`]: size === ViewportSize.xs,
          })}
        >
          <div
            className={cls(`${prefixCls}-otp-step-wrapper`, {
              [`${prefixCls}-otp-step-wrapper-xs`]: size === ViewportSize.xs,
            })}
            hidden={
              (resetOtp && step === 5) || (!resetOtp && step === 4) || !!errType
            }
          >
            <Step cur={_step} total={_totalStep} />
          </div>
          <div
            className={cls(`${prefixCls}-otp-title-wrapper`, {
              [`${prefixCls}-otp-title-wrapper-xs`]: size === ViewportSize.xs,
            })}
          >
            {showBackButton && <BackButton handleBack={handleBack} />}
            <div
              className={cls(`${prefixCls}-otp-title`, {
                [`${prefixCls}-otp-title-xs`]: size === ViewportSize.xs,
              })}
            >
              <Title title={title} />
            </div>
          </div>
          {children}
        </div>
      </div>
    </Wrapper>
  );
};

export default Index;
