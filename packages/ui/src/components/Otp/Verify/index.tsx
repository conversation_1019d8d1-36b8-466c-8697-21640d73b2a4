import {
  INVALID_TOTP_VERIFY_CODE,
  INVALID_TOTP_VERIFY_CODE_V2,
  prefixCls,
  Response,
  ServiceErrorCodes,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Button, Form, Modal } from '@otakus/design';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { OTPRef } from 'antd/es/input/OTP';
import './index.less';
import { FormItem, FormItems } from '@ui/components/Form/FormItem';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';
import { handleFormErrorTooltip } from '@ui/components/LoginForm/helper';
import Tooltip from '@ui/components/LoginForm/ToolTip';
import { QuestionCircleOutlined } from '@otakus/icons';
import cls from 'classnames';

interface OtpVerifyProps {
  handleStep: () => void;
  bindOtp: (dcode: string) => Promise<Response>;
  bindOtpLoading: boolean;
  resetOtp?: boolean;
  showTips?: boolean;
  goExchangeForm: () => Promise<void>;
}

const Index = (props: OtpVerifyProps) => {
  const {
    handleStep,
    bindOtp,
    bindOtpLoading,
    resetOtp,
    showTips,
    goExchangeForm,
  } = props;
  const [form] = Form.useForm();
  const [modal, contextHolder] = Modal.useModal();
  const codeInputRef = useRef<OTPRef>(null);
  const { size } = useSize();
  const __ = useLocale();
  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: 'decode', errors: [] }]);
  }, []);
  const values = Form.useWatch([], form);

  const tooltipErrorMap = useMemo(() => {
    return new Map<ServiceErrorCodes, string | [string, () => void]>([
      [INVALID_TOTP_VERIFY_CODE, 'decode'],
      [INVALID_TOTP_VERIFY_CODE_V2, 'decode'],
    ]);
  }, []);

  const handleSubmit = async (decode: string) => {
    const data = await bindOtp(decode);
    if (data?.code == 0) {
      handleStep();
    } else {
      handleFormErrorTooltip(
        form,
        {
          code: data?.code,
          message: data?.message,
        },
        tooltipErrorMap,
      );
      if (
        data?.code === INVALID_TOTP_VERIFY_CODE ||
        data?.code === INVALID_TOTP_VERIFY_CODE_V2
      ) {
        form.setFieldValue('decode', '');
        codeInputRef.current?.focus();
      }
    }
  };

  const _goExchangeForm = async () => {
    const config = {
      title: __('otp:exchange:form:modal:title'),
      content: __('otp:exchange:form:modal:content'),
      okText: __('otp:exchange:form:modal:ok:text'),
      cancelText: __('otp:exchange:form:modal:cancel:text'),
      icon: null,
    };
    const confirmed = await modal.confirm(config);
    if (confirmed) {
      await goExchangeForm();
    }
  };

  useEffect(() => {
    if (values?.decode && values?.decode?.length === 6) {
      Promise.resolve().then(() => handleSubmit(values?.decode));
    }
  }, [values]);

  return (
    <div className={`${prefixCls}-otp-verify-wrapper`}>
      <span
        className={cls(`${prefixCls}-otp-verify-desc`, {
          [`${prefixCls}-otp-verify-desc-xs`]: size === ViewportSize.xs,
        })}
      >
        {resetOtp ? __('otp:verify:exchange:desc') : __('otp:bind:desc')}
      </span>
      <Form
        form={form}
        name={'otp-verify'}
        className={`${prefixCls}-login-form-wrapper`}
        disabled={bindOtpLoading}
      >
        <FormItems>
          <FormItem
            name={'decode'}
            validateFirst
            wrap
            rules={[
              { required: true, message: __('otp:verify:message') },
              { whitespace: true, message: __('otp:verify:message') },
            ]}
            validateTrigger={false}
          >
            <CodeInput
              onCharChange={onCodeCharChange}
              codeInputRef={codeInputRef}
              autoFocus
              onPaste={handleOtpPaste('decode', form)}
            />
          </FormItem>
        </FormItems>
        <div
          className={cls(`${prefixCls}-otp-verify-exchange-wrapper`, {
            [`${prefixCls}-otp-verify-exchange-wrapper-xs`]:
              size === ViewportSize.xs,
          })}
        >
          <span className={`${prefixCls}-otp-verify-exchange-prefix`}>
            {__('otp:verify:exchange:form:prefix')}
          </span>
          <Button type="link" onClick={_goExchangeForm}>
            {__('otp:verify:exchange:form:link')}
          </Button>
        </div>
        {showTips && (
          <div className={`${prefixCls}-otp-verify-form-tips`}>
            {__('otp:verify:tips')}
            <Tooltip placement={'bottom'} title={__('otp:verify:tips:message')}>
              <div className={`${prefixCls}-otp-verify-form-question-icon`}>
                <QuestionCircleOutlined />
              </div>
            </Tooltip>
          </div>
        )}
      </Form>
      {contextHolder}
    </div>
  );
};

export default Index;
