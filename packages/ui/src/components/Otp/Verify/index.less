@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-verify-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-otp-verify-desc {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
  }

  &-otp-verify-desc-xs {
    margin-bottom: 12px;
  }

  &-otp-verify-form-wrapper {
    width: 100%;

    .-form-item-explain-error {
      display: none;
    }

    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }
  }

  &-otp-verify-form-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    color: var(--otakus-color-text-description);
  }

  &-otp-verify-form-question-icon {
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: var(--otakus-border-radius);
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-otp-verify-form-question-icon:hover {
    background: var(--otakus-color-bg-text-hover);
  }

  &-otp-verify-form-question-icon:active {
    background: var(--otakus-color-bg-text-active);
  }

  &-otp-verify-exchange-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    margin-top: 24px;
    font-size: 14px;

    .otakus-btn {
      padding: 0;
    }
  }

  &-otp-verify-exchange-prefix {
    color: var(--otakus-color-text-description);
  }

  &-otp-verify-exchange-wrapper-xs {
    margin-top: 20px;
  }
}
