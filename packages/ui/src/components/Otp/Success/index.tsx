import {
  accountDomainMap,
  buildUrl,
  getIsWaveEnv,
  message,
  OtpSourceType,
  passwdDomainMap,
  prefixCls,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { ReactSVG } from 'react-svg';
import successSvg from '@ui/assets/otp-success.svg';
import { Button } from '@otakus/design';
import { useCountDown } from 'ahooks';
import cls from 'classnames';
import './index.less';
import { useEffect } from 'react';

interface IOtpSuccessProps {
  resetOtp?: boolean;
}

const Index = (props: IOtpSuccessProps) => {
  const { resetOtp } = props;
  const __ = useLocale();
  const { otpSourceType, env, lang, flowToken, sourceRedirectUrl } =
    useConfig();
  const { size } = useSize();
  const goSource = () => {
    if (otpSourceType === OtpSourceType.PWD) {
      const domain = passwdDomainMap[env] || passwdDomainMap['prod'];
      const search = {
        lang,
        flow_token: flowToken,
      };
      window.location.href = buildUrl(domain, search);
    }
  };
  useEffect(() => {
    // 在自主绑定流程中
    if (otpSourceType === OtpSourceType.ACCOUNT && resetOtp) {
      // 在 wave 中   直接跳转账号中心
      // 在 非 wave 中  成功
      if (getIsWaveEnv()) {
        message.success(__('otp:success'));
        setTimeout(() => {
          const domain = accountDomainMap[env] || accountDomainMap['prod'];
          const search = {
            resetOtp,
            lang,
          } as unknown as {};
          window.location.href = buildUrl(domain, search);
        }, 2000);
      }
    }
  }, [otpSourceType, resetOtp]);

  const [countdown, formattedRes] = useCountDown({
    leftTime: 3 * 1000,
    onEnd: goSource,
  });

  return (
    <div
      className={cls(`${prefixCls}-otp-success-wrapper`, {
        [`${prefixCls}-otp-success-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <ReactSVG src={successSvg} />
      <span className={`${prefixCls}-otp-success-title`}>
        {resetOtp ? __('otp:exchange:success:text') : __('otp:success:title')}
      </span>
      {otpSourceType === OtpSourceType.PWD && (
        <>
          <span className={`${prefixCls}-otp-success-pwd-title`}>
            {__('otp:success:pwd:desc')}
          </span>
          <Button onClick={goSource} type={'primary'}>
            {__('otp:success:pwd:link:text') +
              `（${Math.round(countdown / 1000)}）`}
          </Button>
        </>
      )}
    </div>
  );
};

export default Index;
