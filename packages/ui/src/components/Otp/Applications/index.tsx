import {
  DeviceType,
  getIsWaveEnv,
  IV3AuthMethodConfigs,
  IV3Method,
  message,
  prefixCls,
  useAssets,
  useConfig,
  useDeviceType,
  useLocale,
  useSize,
  V3OpenWithBrowserInWave,
  ViewportSize,
} from '@iam/login-shared';
import { Button, useTheme } from '@otakus/design';
import Tab from '@ui/components/Tab';
import React, { useMemo, useState } from 'react';
import { ApplicationItemProps } from './Item';
import cls from 'classnames';
import classNames from 'classnames';

import './index.less';
import { ExclamationCircleFilled } from '@otakus/icons';
import { useSpring } from '@react-spring/web';
import qrWrapper from '@ui/assets/qrcode-wrapper.webp';
import { ReactSVG } from 'react-svg';

interface ApplicationProps {
  handleStep: () => void;
}

const methods = [
  {
    method: IV3Method.iOS,
  },
  {
    method: IV3Method.Android,
  },
] as unknown as IV3AuthMethodConfigs[];

const Index = (props: ApplicationProps) => {
  const { handleStep } = props;
  const __ = useLocale();
  const googleSvg = useAssets('google-authenticator.svg');
  const microsoftSvg = useAssets('microsoft-authenticator.svg');
  const towFaSvg = useAssets('2fa-authenticator.svg');
  const [_deviceType, set_DeviceType] = useState<IV3Method>(IV3Method.iOS);
  const [hoverApp, setHoverApp] = useState<string>(undefined);
  const token = useTheme();
  const { deviceType } = useDeviceType();
  const { lang, env } = useConfig();
  const { size } = useSize();

  const showTips = useMemo(() => {
    if (deviceType === DeviceType.PC) {
      return (
        hoverApp === __('otp:apps:google') && _deviceType === IV3Method.iOS
      );
    }
    return deviceType === DeviceType.iOS;
  }, [__, deviceType, _deviceType]);

  const qrCodeWrapperStyle = {
    backgroundImage: `url(${qrWrapper})`,
  };

  const opacityStyles = useSpring({
    opacity: showTips ? 1 : 0,
    config: { duration: 200 },
    immediate: deviceType === DeviceType.iOS,
  });

  const initialHeight = useMemo(() => {
    return lang === 'zh-CN' ? 42 : 64;
  }, [deviceType]);

  const heightStyles = useSpring({
    height: showTips ? initialHeight : 0,
    config: { duration: 200 },
    immediate: deviceType === DeviceType.iOS,
  });

  const handle_DeviceType = (active: IV3Method) => {
    set_DeviceType(active);
  };

  const handleHover = (app: string) => {
    setHoverApp(app);
  };

  const defaultAppList = [
    {
      logo: microsoftSvg,
      titleFirst: __('otp:apps:microsoft'),
      titleSecond: __('otp:apps:authenticator'),
      iOSLink:
        'https://apps.apple.com/cn/app/microsoft-authenticator/id983156458',
      iOSQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/z6GApyLpT9CrTAYM7Dr76AuzFxIWebAqaMslKF/1733313001727491642.png',
      AndroidLink: 'https://mhyurl.cn/4tVlFDAL',
      AndroidQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/jHnfrCboRleKxjCwgUOrvwPRiLCSaDpmYIFJHF/1733293368710045714.png',
    },
    {
      logo: towFaSvg,
      titleFirst: __('opt:apps:2fa'),
      titleSecond: __('otp:apps:authenticator'),
      iOSLink:
        'https://apps.apple.com/us/app/2fa-authenticator-2fas/id1217793794',
      iOSQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/NY1OBya3ScerIK9x9z1idhVZwYwfGKSjndmoOTRhA/1733313001492669917.png',
      AndroidLink: 'https://mhyurl.cn/0tVlFCAL',
      AndroidQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/o3aVQZLLTcupbGMmTZVVsAOKvsTTanxXOhWaUK/1733293360797331614.png',
    },
    {
      logo: googleSvg,
      titleFirst: __('otp:apps:google'),
      titleSecond: __('otp:apps:authenticator'),
      iOSLink: 'https://apps.apple.com/us/app/google-authenticator/id388497605',
      iOSQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/a4lsimpxRxybz9eNxl9IPwUEmJUhLOswFHfzJA/1733313001595749470.png',
      AndroidLink: 'https://mhyurl.cn/ctVlFBAL',
      AndroidQrCode:
        'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/EXTwkhcxSIaUmqWhVAA8WgIgthxghMQoXeJzlQ/1733293368642089519.png',
    },
  ] as unknown as ApplicationItemProps[];

  const downloadApp = async () => {
    const url =
      deviceType === DeviceType.iOS
        ? defaultAppList[0].iOSLink
        : defaultAppList[0].AndroidLink;
    if (getIsWaveEnv()) {
      const result = await V3OpenWithBrowserInWave(env, url);
      if (!result) {
        message.error('调用wave api 失败');
        // window.open(url, '_blank');
      }
    } else {
      window.open(url, '_blank');
    }
  };

  return (
    <div
      className={classNames(`${prefixCls}-otp-app-wrapper`, {
        [`${prefixCls}-otp-app-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      {/*<span className={`${prefixCls}-otp-app-desc`}>{__('otp:apps:desc')}</span>*/}
      {deviceType === DeviceType.PC && (
        <Tab
          authMethodConfigs={methods}
          active={_deviceType}
          onChange={handle_DeviceType}
        ></Tab>
      )}
      {deviceType === DeviceType.PC && (
        <div className={`${prefixCls}-otp-app-desc-1`}>
          {__('otp:app:pc:desc:1')}
        </div>
      )}
      {deviceType === DeviceType.PC && (
        <div
          className={`${prefixCls}-otp-app-main-qrcode`}
          style={qrCodeWrapperStyle}
        >
          <img
            alt={'microsoftSvg'}
            src={
              _deviceType === IV3Method.iOS
                ? defaultAppList[0].iOSQrCode
                : defaultAppList[0].AndroidQrCode
            }
          ></img>
        </div>
      )}
      {deviceType !== DeviceType.PC && (
        <div className={`${prefixCls}-otp-app-microsoft`}>
          <div className={`${prefixCls}-otp-app-logo-wrapper`}>
            <ReactSVG
              src={microsoftSvg}
              className={`${prefixCls}-otp-app-logo`}
            ></ReactSVG>
          </div>
          <span className={`${prefixCls}-otp-app-logo-title`}>
            {`${__('otp:apps:microsoft')}`}
            {'  '}
            {`${__('otp:apps:authenticator')}`}
          </span>
        </div>
      )}
      {deviceType !== DeviceType.PC && (
        <Button
          className={`${prefixCls}-otp-app-download-btn`}
          size="large"
          type="outlined"
          onClick={downloadApp}
        >
          {deviceType === DeviceType.iOS
            ? __('otp:app:mb:ios:download:btn:text')
            : __('otp:app:mb:android:download:btn:text')}
        </Button>
      )}
      {/*<div className={`${prefixCls}-otp-app-list`}>*/}
      {/*  {AppList.map((e) => (*/}
      {/*    <Item*/}
      {/*      {...e}*/}
      {/*      key={e.titleFirst}*/}
      {/*      active={_deviceType}*/}
      {/*      handleHover={handleHover}*/}
      {/*    ></Item>*/}
      {/*  ))}*/}
      {/*</div>*/}
      {/*<animated.div*/}
      {/*  style={{ ...heightStyles, ...opacityStyles, width: '100%' }}*/}
      {/*>*/}
      {/*  <div className={`${prefixCls}-otp-app-download-tips`}>*/}
      {/*    <ExclamationCircleFilled*/}
      {/*      color={token.colorWarning}*/}
      {/*      className={`${prefixCls}-otp-app-download-tips-icon`}*/}
      {/*    />*/}
      {/*    <span>{downloadTips}</span>*/}
      {/*  </div>*/}
      {/*</animated.div>*/}
      <div
        className={cls(`${prefixCls}-otp-app-desc-2`, {
          [`${prefixCls}-otp-app-desc-2-xs`]: size === ViewportSize.xs,
        })}
      >
        <ExclamationCircleFilled
          className={cls(`${prefixCls}-otp-app-desc-2-icon`, {
            [`${prefixCls}-otp-app-desc-2-icon-xs`]: size === ViewportSize.xs,
          })}
        />
        <span>{__('otp:app:pc:desc:2')}</span>
      </div>
      <Button
        className={cls(`${prefixCls}-otp-app-btn`, {
          [`${prefixCls}-otp-app-btn-xs`]: size === ViewportSize.xs,
        })}
        size="large"
        type="primary"
        onClick={() => handleStep()}
      >
        {__('otp:app:submit:btn:text')}
      </Button>
      <div className={`${prefixCls}-otp-app-error-tips`}>
        {__('otp:app:download:error:tips:v2')}
        <Button
          type={'link'}
          className={`${prefixCls}-otp-app-error-tips-km-btn`}
          onClick={() =>
            window.open('https://km.mihoyo.com/doc/mh1at603nx9k', '_blank')
          }
        >
          {__('otp:app:download:error:tips:doc')}
        </Button>
        {/*<Tooltip*/}
        {/*  placement={'bottom'}*/}
        {/*  title={() => {*/}
        {/*    return (*/}
        {/*      <div>*/}
        {/*        <span>{__('otp:app:download:error:tips:desc')}</span>*/}
        {/*      </div>*/}
        {/*    );*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <div className={`${prefixCls}-login-form-question-icon`}>*/}
        {/*    <QuestionCircleOutlined />*/}
        {/*  </div>*/}
        {/*</Tooltip>*/}
      </div>
    </div>
  );
};

export default Index;
