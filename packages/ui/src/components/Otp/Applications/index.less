@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-app-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-otp-app-wrapper-xs {
    margin-top: 44px;
  }

  &-otp-app-desc {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
  }

  &-otp-app-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    flex-direction: row;
  }

  &-otp-app-item {
    //padding: 16px 16px 6px;
    border-radius: 10px;
    border: 1px solid var(--otakus-color-split);
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 160px;
    position: relative;
  }

  &-otp-app-item-recommend {
    background: var(--otakus-color-bg-container); /* 背景颜色 */
    border: 1px solid transparent; /* 透明边框占据空间 */
    background-clip: padding-box; /* 背景仅在内边距区域显示 */
  }

  &-otp-app-item-recommend::before {
    content: '';
    position: absolute;
    top: -1px; /* 边框宽度 */
    left: -1px; /* 边框宽度 */
    right: -1px; /* 边框宽度 */
    bottom: -1px; /* 边框宽度 */
    background: linear-gradient(
      -65deg,
      rgba(178, 99, 252, 0.5),
      /* #B263FC 50% 透明度 */ rgba(113, 170, 255, 0.5),
      /* #71AAFF 50% 透明度 */ rgba(204, 172, 248, 0.5),
      /* #CCACF8 50% 透明度 */ rgba(204, 172, 248, 0.5) /* #CCACF8 50% 透明度 */
    );
    z-index: -1;
    border-radius: 10px; /* 圆角，比容器大 4px */
  }

  &-otp-app-icon {
    padding: 31px;
    display: flex;
    align-items: center;
    justify-content: center;

    div {
      width: 40px;
      height: 40px;
    }

    svg {
      width: 40px;
      height: 40px;
    }
  }

  &-otp-app-name {
    background: var(--otakus-color-fill-alter);
    display: flex;
    align-items: center;
    color: var(--otakus-color-text-secondary);
    font-size: 14px;
    padding: 6px 10px 6px 12px;
    flex: 1;
    font-weight: 400;
    justify-content: center;
    flex-direction: column;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }

  &-otp-app-btn {
    width: 100%;
    height: 54px;
    margin-top: 24px;
    border-radius: 12px;
  }

  &-otp-app-download-btn {
    width: 100%;
    height: 54px;
    margin-top: 44px;
    border-radius: 12px;
  }

  &-otp-app-help {
    margin-top: 24px;
  }

  &-otp-app-help {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 4px;
    color: var(--otakus-color-primary);
    font-weight: 500;
    font-size: 14px;

    span {
      cursor: pointer;
    }
  }

  &-otp-app-qrcode-wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    text-align: center;

    img {
      width: 106px;
      height: 106px;
    }

    span {
      font-size: 14px;
      color: var(--otakus-color-text-description);
    }
  }

  &-otp-app-download-tips {
    width: 100%;
    border-radius: 8px;
    border: 0px solid var(--otakus-color-split);
    background: var(--otakus-color-warning-bg);
    padding: 4px 8px;
    color: var(--otakus-color-warning);
    font-weight: 500;
    margin-top: 12px;

    & > span:last-child {
      font-size: 12px;
    }
  }

  &-otp-app-download-tips-icon {
    margin-right: 8px;
  }

  &-otp-app-desc-mb-device {
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
    font-weight: 400;
    margin-bottom: 20px;
  }

  &-otp-app-recommend-wrapper {
    position: absolute;
    //width: 70px;
    top: -10px;
    left: 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    background: #fff;
    padding-left: 4px;
    padding-right: 2px;
  }

  &-otp-app-item-recommend &-otp-app-name {
    background: var(--otakus-color-purple-bg);
    color: var(--otakus-color-purple-text);
  }

  &-otp-app-recommend-icon {
    height: 15px;
    margin-left: 4px;
  }

  &-otp-app-recommend-text {
    background: linear-gradient(
      to right,
      rgba(124, 55, 178, 1),
      rgba(102, 126, 255, 1)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  &-otp-app-error-tips {
    color: var(--otakus-color-text-description);
    align-items: center;
    display: flex;
    margin-top: 24px;
  }

  &-otp-app-desc-1 {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--otakus-color-text-secondary);
    font-weight: 400;
  }

  &-otp-app-main-qrcode {
    position: relative;
    background-size: 100% 100%;
    width: 188px;
    height: 188px;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 166px;
      height: 166px;
    }
  }

  &-otp-app-desc-2 {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    border-radius: 8px;
    background: var(--otakus-color-primary-bg);
    padding: 4px 6px;
    margin-top: 20px;
    color: var(--otakus-color-primary);
    font-weight: 500;
    font-size: 12px;
  }

  &-otp-app-desc-2-xs {
    align-items: flex-start;
    margin-top: 12px;
  }

  &-otp-app-desc-2-icon {
    margin-right: 4px;
    margin-top: 3px;
  }

  &-otp-app-desc-2-icon-xs {
    margin-top: 4px;
  }

  &-otp-app-logo-wrapper {
    border-radius: 12px;
    height: 62px;
    width: 62px;
    border: 1px solid var(--otakus-color-split);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-otp-app-logo {
    margin-top: 6px;
  }

  &-otp-app-microsoft {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  &-otp-app-logo-title {
    margin-top: 16px;
    font-weight: 600;
    font-size: 16px;
    color: rgba(2, 4, 19, 0.87);
  }

  &-otp-app-btn-xs {
    margin-top: 12px;
  }

  &-otp-app-error-tips-km-btn {
    padding: 0;
  }
}
