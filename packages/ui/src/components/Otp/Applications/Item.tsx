import {
  DeviceType,
  getIsWaveEnv,
  IV3Method,
  message,
  prefixCls,
  useConfig,
  useDeviceType,
  useLocale,
  V3OpenWithBrowserInWave,
} from '@iam/login-shared';
import { useEffect, useRef } from 'react';
import { useHover } from 'ahooks';
import { animated, useSpring } from '@react-spring/web';
import { ReactSVG } from 'react-svg';
import cls from 'classnames';
import recommendSvg from '@ui/assets/otp-recommand.svg';

export interface ApplicationItemProps {
  logo: string;
  titleFirst: string;
  titleSecond: string;
  active: IV3Method;
  iOSLink?: string;
  iOSQrCode?: string;
  AndroidLink?: string;
  AndroidQrCode?: string;
  handleHover?: (app: string) => void;
  recommend?: boolean;
}

const Index = (props: ApplicationItemProps) => {
  const {
    logo,
    titleFirst,
    titleSecond,
    active,
    iOSQrCode,
    AndroidQrCode,
    handleHover,
    iOSLink,
    AndroidLink,
    recommend,
  } = props;
  const ref = useRef(null);
  const isHovering = useHover(ref);
  const { deviceType } = useDeviceType();
  const __ = useLocale();
  const { env } = useConfig();

  const fadeInStyle = useSpring({
    opacity: deviceType === DeviceType.PC && isHovering ? 1 : 0,

    config: { duration: 200 },
  });

  useEffect(() => {
    if (isHovering) {
      handleHover?.(titleFirst);
    } else {
      handleHover?.(undefined);
    }
  }, [isHovering]);

  const fadeOutStyle = useSpring({
    opacity: deviceType === DeviceType.PC && isHovering ? 0 : 1,
    config: { duration: 200 },
  });
  const qrcode = active === IV3Method.iOS ? iOSQrCode : AndroidQrCode;
  const Qrcode = () => {
    return (
      <div className={`${prefixCls}-otp-app-qrcode-wrapper`}>
        <img src={qrcode} alt={'qrcode'} />
        <span>{__('otp:app:scan:download')}</span>
      </div>
    );
  };

  const goDownloadLink = async () => {
    if (deviceType === DeviceType.PC) {
      return;
    }
    const url = deviceType === DeviceType.iOS ? iOSLink : AndroidLink;
    if (getIsWaveEnv()) {
      const result = await V3OpenWithBrowserInWave(env, url);
      if (!result) {
        message.error('调用wave api 失败');
        // window.open(url, '_blank');
      }
    } else {
      window.open(url, '_blank');
    }
  };
  return (
    <div
      className={cls(`${prefixCls}-otp-app-item`, {
        [`${prefixCls}-otp-app-item-recommend`]: recommend,
      })}
      ref={ref}
    >
      {recommend && (
        <div className={`${prefixCls}-otp-app-recommend-wrapper`}>
          <div className={`${prefixCls}-otp-app-recommend-text`}>
            {__('otp:app:recommend:text')}
          </div>
          <div className={`${prefixCls}-otp-app-recommend-icon`}>
            <ReactSVG src={recommendSvg}></ReactSVG>
          </div>
        </div>
      )}
      <animated.div
        style={{
          ...fadeInStyle,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          borderRadius: '10px',
          boxShadow:
            ' 0px 20px 40px 0px rgba(2, 4, 30, 0.01), 0px 16px 16px 0px rgba(2, 4, 30, 0.01), 0px 12px 12px 0px rgba(2, 4, 30, 0.02), 0px 4px 4px 0px rgba(2, 4, 30, 0.02)',
        }}
      >
        <Qrcode />
      </animated.div>
      <animated.div
        style={{
          ...fadeOutStyle,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          borderRadius: '10px',
        }}
      >
        <div onClick={goDownloadLink}>
          <div className={`${prefixCls}-otp-app-icon`}>
            <ReactSVG src={logo}></ReactSVG>
          </div>
          <div className={`${prefixCls}-otp-app-name`}>
            <span>{titleFirst}</span>
            <span>{titleSecond}</span>
          </div>
        </div>
      </animated.div>
    </div>
  );
};

export default Index;
