import {
  DeviceType,
  IV3AuthMethodConfigs,
  IV3Method,
  prefixCls,
  useAssets,
  useConfig,
  useDeviceType,
  useLocale,
} from '@iam/login-shared';
import { Button, useTheme } from '@otakus/design';
import Tab from '@ui/components/Tab';
import React, { useMemo, useState } from 'react';
import Item, { ApplicationItemProps } from './Item';

import './index.less';
import { ExclamationCircleFilled, QuestionCircleOutlined } from '@otakus/icons';
import { animated, useSpring } from '@react-spring/web';
import Tooltip from '@ui/components/LoginForm/ToolTip';

interface ApplicationProps {
  handleStep: () => void;
}

const methods = [
  {
    method: IV3Method.iOS,
  },
  {
    method: IV3Method.Android,
  },
] as unknown as IV3AuthMethodConfigs[];

const Index = (props: ApplicationProps) => {
  const { handleStep } = props;
  const __ = useLocale();
  const googleSvg = useAssets('google-authenticator.svg');
  const microsoftSvg = useAssets('microsoft-authenticator.svg');
  const towFaSvg = useAssets('2fa-authenticator.svg');
  const [_deviceType, set_DeviceType] = useState<IV3Method>(IV3Method.iOS);
  const [hoverApp, setHoverApp] = useState<string>(undefined);
  const token = useTheme();
  const { deviceType } = useDeviceType();
  const { lang } = useConfig();

  const showTips = useMemo(() => {
    if (deviceType === DeviceType.PC) {
      return (
        hoverApp === __('otp:apps:google') && _deviceType === IV3Method.iOS
      );
    }
    return deviceType === DeviceType.iOS;
  }, [__, deviceType, _deviceType]);

  const opacityStyles = useSpring({
    opacity: showTips ? 1 : 0,
    config: { duration: 200 },
    immediate: deviceType === DeviceType.iOS,
  });

  const initialHeight = useMemo(() => {
    return lang === 'zh-CN' ? 42 : 64;
  }, [deviceType]);

  const heightStyles = useSpring({
    height: showTips ? initialHeight : 0,
    config: { duration: 200 },
    immediate: deviceType === DeviceType.iOS,
  });

  const desc = useMemo(() => {
    if (deviceType === DeviceType.PC || deviceType === DeviceType.Unknown) {
      return '';
    }
    return deviceType === DeviceType.Android
      ? __('otp:app:android:desc')
      : __('otp:app:ios:desc');
  }, [deviceType, __]);

  const handle_DeviceType = (active: IV3Method) => {
    set_DeviceType(active);
  };

  const handleHover = (app: string) => {
    setHoverApp(app);
  };

  const AppList = useMemo(() => {
    const defaultAppList = [
      {
        logo: microsoftSvg,
        titleFirst: __('otp:apps:microsoft'),
        titleSecond: __('otp:apps:authenticator'),
        iOSLink:
          'https://apps.apple.com/cn/app/microsoft-authenticator/id983156458',
        iOSQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/z6GApyLpT9CrTAYM7Dr76AuzFxIWebAqaMslKF/1733313001727491642.png',
        AndroidLink: 'https://mhyurl.cn/4tVlFDAL',
        AndroidQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/jHnfrCboRleKxjCwgUOrvwPRiLCSaDpmYIFJHF/1733293368710045714.png',
      },
      {
        logo: towFaSvg,
        titleFirst: __('opt:apps:2fa'),
        titleSecond: __('otp:apps:authenticator'),
        iOSLink:
          'https://apps.apple.com/us/app/2fa-authenticator-2fas/id1217793794',
        iOSQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/NY1OBya3ScerIK9x9z1idhVZwYwfGKSjndmoOTRhA/1733313001492669917.png',
        AndroidLink: 'https://mhyurl.cn/0tVlFCAL',
        AndroidQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/o3aVQZLLTcupbGMmTZVVsAOKvsTTanxXOhWaUK/1733293360797331614.png',
      },
      {
        logo: googleSvg,
        titleFirst: __('otp:apps:google'),
        titleSecond: __('otp:apps:authenticator'),
        iOSLink:
          'https://apps.apple.com/us/app/google-authenticator/id388497605',
        iOSQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/a4lsimpxRxybz9eNxl9IPwUEmJUhLOswFHfzJA/1733313001595749470.png',
        AndroidLink: 'https://mhyurl.cn/ctVlFBAL',
        AndroidQrCode:
          'https://oc-cdn.app.mihoyo.com/blob/v1/cdn/download/EXTwkhcxSIaUmqWhVAA8WgIgthxghMQoXeJzlQ/1733293368642089519.png',
      },
    ] as unknown as ApplicationItemProps[];
    if (deviceType === DeviceType.PC) {
      if (_deviceType === IV3Method.Android) {
        [defaultAppList[0], defaultAppList[2]] = [
          defaultAppList[2],
          defaultAppList[0],
        ];
      }
      defaultAppList[0].recommend = true;
      return defaultAppList;
    }
    if (
      deviceType === DeviceType.Android ||
      deviceType === DeviceType.Unknown
    ) {
      [defaultAppList[0], defaultAppList[2]] = [
        defaultAppList[2],
        defaultAppList[0],
      ];
    }
    defaultAppList[0].recommend = true;
    return defaultAppList;
  }, [_deviceType, deviceType]) as unknown as ApplicationItemProps[];

  const downloadTips = useMemo(() => {
    return __('otp:app:download:tips');
  }, [deviceType, __]);

  return (
    <div className={`${prefixCls}-otp-app-wrapper`}>
      {/*<span className={`${prefixCls}-otp-app-desc`}>{__('otp:apps:desc')}</span>*/}
      {deviceType === DeviceType.PC && (
        <Tab
          authMethodConfigs={methods}
          active={_deviceType}
          onChange={handle_DeviceType}
        ></Tab>
      )}
      {desc && (
        <span className={`${prefixCls}-otp-app-desc-mb-device`}>{desc}</span>
      )}
      <div className={`${prefixCls}-otp-app-list`}>
        {AppList.map((e) => (
          <Item
            {...e}
            key={e.titleFirst}
            active={_deviceType}
            handleHover={handleHover}
          ></Item>
        ))}
      </div>
      <animated.div
        style={{ ...heightStyles, ...opacityStyles, width: '100%' }}
      >
        <div className={`${prefixCls}-otp-app-download-tips`}>
          <ExclamationCircleFilled
            color={token.colorWarning}
            className={`${prefixCls}-otp-app-download-tips-icon`}
          />
          <span>{downloadTips}</span>
        </div>
      </animated.div>

      <Button
        className={`${prefixCls}-otp-app-btn`}
        size="large"
        type="primary"
        onClick={() => handleStep()}
      >
        {__('otp:apps:btn')}
      </Button>
      <div className={`${prefixCls}-otp-app-error-tips`}>
        {__('otp:app:download:error:tips')}
        <Tooltip
          placement={'bottom'}
          title={() => {
            return (
              <div>
                <span>{__('otp:app:download:error:tips:desc')}</span>
              </div>
            );
          }}
        >
          <div className={`${prefixCls}-login-form-question-icon`}>
            <QuestionCircleOutlined />
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default Index;
