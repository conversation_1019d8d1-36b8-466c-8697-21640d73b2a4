import {
  FollowAuthMethods,
  GetFollowAuthMethods,
  message,
  prefixCls,
  Response,
  setVisible,
  useConfig,
  useKeyPressSubmit,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Button, Form, InputRef, Tooltip } from '@otakus/design';

import { FormItem, FormItems } from '@ui/components/Form/FormItem';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';
import { OTPRef } from 'antd/es/input/OTP';
import SwitchPhone from '@ui/components/SwitchPhone';
import './index.less';
import { QuestionCircleOutlined } from '@otakus/icons';
import { useAsyncEffect, useCountDown } from 'ahooks';
import Input from '@ui/components/Input';

interface OtpIdpProps {
  followAuthMethod: GetFollowAuthMethods;
  handleSendCode: (id: string) => Promise<Response>;
  handleStep: () => void;
  verifyCode: (
    method: FollowAuthMethods,
    method_id: string,
    code: string,
  ) => Promise<Response>;
  verifyFollowAuthCodeLoading: boolean;
}

const Index = (props: OtpIdpProps) => {
  const {
    followAuthMethod,
    handleSendCode,
    handleStep,
    verifyCode,
    verifyFollowAuthCodeLoading,
  } = props;
  const [form] = Form.useForm();
  const __ = useLocale();
  const { env, lang } = useConfig();
  const { candidate_methods = [] } = followAuthMethod;
  const [codeVisible, setCodeVisible] = useState<boolean>(true);
  const { size } = useSize();
  const [targetDate, setTargetDate] = useState<number>();
  const [countdown] = useCountDown({
    targetDate,
  });
  const pwdInputRef = useRef<InputRef>(null);
  // 判断当前属于那种表单形式
  const formType = useMemo(() => {
    if (candidate_methods?.length == 0) {
      return undefined;
    }
    const phoneList = candidate_methods.filter(
      (e) => e.method === FollowAuthMethods.SMS,
    );
    return phoneList?.length > 1
      ? 'phone_list'
      : phoneList?.length === 1
      ? 'phone'
      : 'pwd';
  }, [candidate_methods]);

  useEffect(() => {
    if (formType === 'phone_list') {
      form.setFieldsValue({
        method_id: candidate_methods[0].id + '',
      });
    }
  }, [formType, form, candidate_methods]);

  useEffect(() => {
    if (verifyFollowAuthCodeLoading) {
      message.loading(__('otp:idp:loading'));
    } else {
      message.destroy();
    }
  }, [verifyFollowAuthCodeLoading]);

  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: 'code', errors: [] }]);
  }, []);

  const codeInputRef = useRef<OTPRef>(null);

  const handleValuesChange = (val: any) => {
    if (val?.method_id) {
      setCodeVisible(false);
      setTimeout(() => {
        setCodeVisible(true);
        form.setFields([
          {
            name: 'code',
            value: '',
            errors: [],
          },
        ]);
      }, 5);
    }
  };

  const _handleSendCode = async () => {
    let method_id = '';
    if (formType === 'phone_list') {
      method_id = form.getFieldValue('method_id');
    } else {
      method_id = candidate_methods[0].id + '';
    }
    const data = await handleSendCode(method_id);
    if (data?.code == 0) {
      message.success(__('sms:success'));
      if (formType === 'pwd') {
        pwdInputRef.current.focus();
      } else {
        if (size !== ViewportSize.xs) {
          codeInputRef?.current?.focus();
        }
      }
      setTargetDate(Date.now() + 60 * 1000);
    } else {
      form.setFields([
        {
          name: 'code',
          errors: [data?.message],
        },
      ]);
    }
  };

  const values = Form.useWatch([], form);

  useEffect(() => {
    if (formType === 'phone_list') {
      if (values?.method_id && values?.code && values?.code?.length === 6) {
        verifyCode(FollowAuthMethods.SMS, values.method_id, values.code).then(
          (res) => {
            if (res?.code === 0) {
              handleStep();
            } else {
              form.setFields([
                { name: 'code', errors: [res?.message], value: '' },
              ]);
              codeInputRef.current.focus();
            }
          },
        );
      }
    } else if (formType === 'phone') {
      if (values?.code && values?.code?.length === 6) {
        verifyCode(
          FollowAuthMethods.SMS,
          candidate_methods[0].id + '',
          values.code,
        ).then((res) => {
          if (res?.code === 0) {
            handleStep();
          } else {
            form.setFields([
              { name: 'code', errors: [res?.message], value: '' },
            ]);
            codeInputRef.current.focus();
          }
        });
      }
    } else if (formType === 'pwd') {
      if (values?.code) {
        form.setFields([{ name: 'code', errors: [] }]);
      }
    }
  }, [values, formType]);

  useAsyncEffect(async () => {
    if (formType === 'phone') {
      await _handleSendCode();
    }
  }, [formType]);

  const [pwdDescPrefix, pwdDescSuffix] = __('otp:idp:pwd:desc').split('|');

  const handlePwdSubmit = () => {
    if (values?.code) {
      verifyCode(
        FollowAuthMethods.PWD,
        candidate_methods[0].id + '',
        values?.code,
      ).then((res) => {
        if (res?.code === 0) {
          handleStep();
        } else {
          form.setFields([{ name: 'code', errors: [res?.message] }]);
          if (formType === 'pwd') {
            pwdInputRef.current.focus();
          }
        }
      });
    }
  };

  const userInfo = useMemo(() => {
    if (formType === 'pwd') {
      const { name = '' } = candidate_methods?.filter(
        (e) => e.method === FollowAuthMethods.PWD,
      )?.[0];
      return {
        name,
      };
    }
    if (formType === 'phone') {
      const { cid = '', phone_number = '' } = candidate_methods?.filter(
        (e) => e.method === FollowAuthMethods.SMS,
      )?.[0];
      return {
        cid,
        phone_number,
      };
    }
    if (formType === 'phone_list') {
      const options = candidate_methods.map((e) => ({
        cid: e.cid,
        phoneNumber: e.phone_number,
        value: e.id + '',
      }));
      return {
        options,
      };
    }
  }, [candidate_methods, formType]);

  useKeyPressSubmit(
    () => {
      if (formType === 'pwd' && values?.code) {
        handlePwdSubmit();
      }
      return;
    },
    {
      target: document.querySelector('#otp-idp'),
    },
  );

  const handleClear = () => {
    form.setFields([{ name: 'code', value: '', errors: [] }]);
  };

  if (!formType) {
    return null;
  }

  return (
    <div className={`${prefixCls}-idp-wrapper`}>
      <Form
        form={form}
        className={`${prefixCls}-otp-idp-form-wrapper`}
        name={'otp-idp'}
        onValuesChange={handleValuesChange}
        disabled={verifyFollowAuthCodeLoading}
        id={'otp-idp'}
      >
        <FormItems>
          {setVisible({
            visible: formType === 'phone_list',
            component: (
              <>
                <FormItems>
                  <FormItem name={'method_id'}>
                    <SwitchPhone options={userInfo?.options} />
                  </FormItem>
                  <FormItem
                    name={'code'}
                    validateFirst
                    wrap
                    validateTrigger={false}
                  >
                    {codeVisible ? (
                      <CodeInput
                        onCharChange={onCodeCharChange}
                        codeInputRef={codeInputRef}
                        onPaste={handleOtpPaste('code', form)}
                      />
                    ) : (
                      <div style={{ width: '100%', height: '68px' }}></div>
                    )}
                  </FormItem>
                </FormItems>
              </>
            ),
          })}
          {setVisible({
            visible: formType === 'phone',
            component: (
              <>
                <div className={`${prefixCls}-otp-idp-phone-desc`}>
                  <span>{__('otp:idp:phone:desc')}</span>
                  <span className={`${prefixCls}-otp-idp-phone`}>
                    {userInfo?.cid + ' ' + userInfo?.phone_number}
                  </span>
                </div>
                <FormItem
                  name={'code'}
                  validateFirst
                  wrap
                  validateTrigger={false}
                >
                  <CodeInput
                    onCharChange={onCodeCharChange}
                    codeInputRef={codeInputRef}
                    autoFocus={size !== ViewportSize.xs}
                    onPaste={handleOtpPaste('code', form)}
                  />
                </FormItem>
              </>
            ),
          })}
          {setVisible({
            visible: formType === 'pwd',
            component: (
              <>
                <div className={`${prefixCls}-otp-idp-name-desc`}>
                  <span>{pwdDescPrefix}</span>
                  <span className={`${prefixCls}-otp-idp-name`}>
                    {userInfo?.name}
                  </span>
                  <span>{pwdDescSuffix}</span>
                </div>
                <FormItem wrap name={'code'}>
                  <div style={{ width: '100%' }}>
                    <Input
                      passwd
                      normal
                      placeholder={__('password:message')}
                      allowClear
                      autoFocus={size !== ViewportSize.xs}
                      ref={pwdInputRef}
                      onClear={handleClear}
                      {...props}
                    />
                  </div>
                </FormItem>
              </>
            ),
          })}
        </FormItems>
      </Form>
      {setVisible({
        visible: formType !== 'pwd',
        component: (
          <div className={`${prefixCls}-otp-idp-bottom`}>
            <Button
              type={'primary'}
              className={`${prefixCls}-otp-idp-btn`}
              onClick={_handleSendCode}
              disabled={countdown > 0 || verifyFollowAuthCodeLoading}
            >
              {__('otp:idp:phone:btn') +
                (countdown > 0 ? `(${Math.round(countdown / 1000)}s)` : '')}
            </Button>
            <div className={`${prefixCls}-otp-phone-tips`}>
              {__('otp:idp:phone:tips')}
              <Tooltip
                placement={'bottom'}
                overlayStyle={{ width: '266px' }}
                title={<span>{__('otp:idp:phone:message:v2')}</span>}
                // title={() => {
                //   const [pre, next] = __('otp:idp:phone:message').split('|');
                //   return (
                //     <>
                //       <span>{pre}</span>
                //       <Button
                //         type={'link'}
                //         size={'small'}
                //         className={`${prefixCls}-login-form-tips-btn`}
                //         onClick={() => goAccount(env, lang)}
                //       >
                //         {__('account:center:name')}
                //       </Button>
                //       <span>{next}</span>
                //     </>
                //   );
                // }}
              >
                <div className={`${prefixCls}-otp-idp-phone-icon`}>
                  <QuestionCircleOutlined />
                </div>
              </Tooltip>
            </div>
          </div>
        ),
      })}
      {setVisible({
        visible: formType === 'pwd',
        component: (
          <div className={`${prefixCls}-otp-idp-bottom`}>
            <Button
              type={'primary'}
              className={`${prefixCls}-otp-idp-btn`}
              onClick={handlePwdSubmit}
              disabled={!values?.code || verifyFollowAuthCodeLoading}
            >
              {__('otp:idp:pwd:btn')}
            </Button>
          </div>
        ),
      })}
    </div>
  );
};

export default Index;
