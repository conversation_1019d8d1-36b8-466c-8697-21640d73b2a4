@import '../../../index.less';

.@{info-iam-login-prefix-cls} {
  &-otp-idp-form-wrapper {
    width: 100%;

    .-form-item-explain-error {
      display: none;
    }

    .otakus-form-item-explain-error {
      display: none; /* 隐藏错误提示 */
    }

    .@{info-iam-login-prefix-cls}-otp-input {
      border-radius: 12px !important;
      margin-top: 12px;
      //border-top-left-radius: 12px !important;
      //border-top-right-radius: 12px !important;
    }
  }

  &-otp-idp-bottom {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-otp-idp-btn {
    width: 100%;
    height: 54px;
    margin-top: 24px;
    border-radius: 12px;
  }

  &-otp-phone-tips {
    color: var(--otakus-color-text-description);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
  }

  &-otp-idp-phone {
    font-size: 16px;
    color: var(--otakus-color-text-heading);
    font-weight: 500;
    white-space: nowrap;
  }

  &-otp-idp-phone-desc {
    //display: flex;
    align-items: baseline;
    justify-content: flex-start;
    color: var(--otakus-color-text-secondary);
    font-size: 14px;
    word-break: break-word;
  }

  &-otp-idp-phone-icon {
    width: 24px;
    background: #fff;
    border-radius: var(--otakus-border-radius);
    text-align: center;
    cursor: pointer;
  }

  &-otp-idp-phone-icon:hover {
    background: var(--otakus-color-bg-text-hover);
  }

  &-otp-idp-phone-icon:active {
    background: var(--otakus-color-bg-text-active);
  }

  &-otp-idp-name {
    font-size: 16px;
    color: var(--otakus-color-text-heading);
    font-weight: 500;
    margin: 0 4px;
  }

  &-otp-idp-name-desc {
    //display: flex;
    align-items: center;
    justify-content: flex-start;
    color: var(--otakus-color-text-secondary);
    font-size: 14px;
    margin-bottom: 12px;
    word-break: break-all;
  }
}
