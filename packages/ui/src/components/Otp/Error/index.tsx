import {
  OtpErrorType,
  prefixCls,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { ReactSVG } from 'react-svg';
import OtpErrorSvg from '@ui/assets/otp-error.svg';
import './index.less';
import cls from 'classnames';

interface OtpErrorPage {
  type: OtpErrorType | '';
}

const Index = (props: OtpErrorPage) => {
  const { type } = props;
  const __ = useLocale();
  const { size } = useSize();
  if (!type) {
    return null;
  }

  return (
    <div
      className={cls(`${prefixCls}-otp-error-wrapper`, {
        [`${prefixCls}-otp-error-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <ReactSVG src={OtpErrorSvg} />
      <span className={`${prefixCls}-otp-error-title`}>
        {__('otp:link:failure:title')}
      </span>
      <span className={`${prefixCls}-otp-error-desc`}>
        {__('otp:link:failure:desc')}
      </span>
    </div>
  );
};

export default Index;
