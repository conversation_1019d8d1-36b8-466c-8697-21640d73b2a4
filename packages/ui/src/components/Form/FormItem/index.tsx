import { Form, Tooltip } from '@otakus/design';

import './index.less';
import classNames from 'classnames';
import { prefixCls } from '@iam/login-shared';
import {
  cloneElement,
  PropsWithChildren,
  ReactElement,
  ReactNode,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FormItemProps, FormItemWrapProps } from './types';
import { EventEmitter } from 'events';

let idx = 0;

/**
 * 全局变量控制表单 wrap 层通信
 * TODO: emitter by from instance
 */
const emitter = new EventEmitter();

/**
 * copy from: antd
 */
export interface TooltipRef {
  /** @deprecated Please use `forceAlign` instead */
  forcePopupAlign: VoidFunction;
  forceAlign: VoidFunction;
  /** Wrapped dom element. Not promise valid if child not support ref */
  nativeElement: HTMLElement;
}

export function FormItem({
  rules,
  className,
  children,
  name,
  help,
  wrap,
  tooltipProps,
  ...rest
}: FormItemProps) {
  // TODO: 隐藏 FormItem help
  return (
    <Form.Item
      name={name}
      rules={rules}
      {...rest}
      className={classNames(className, prefixCls + '-form-item')}
    >
      {wrap ? (
        <FormItemWrap name={name} tooltipProps={tooltipProps}>
          {children as ReactElement}
        </FormItemWrap>
      ) : (
        children
      )}
    </Form.Item>
  );
}

export function FormItems({ children }: PropsWithChildren<any>) {
  return (
    <div className={classNames(prefixCls + '-form-items')}>{children}</div>
  );
}

/**
 * form item 和 交互组件中间层，拦截错误信息并展示
 * 如果是后台错误可以通过Form.setFields 间接触发到tooltip展示
 * 如果有 required rule, 编辑之后errors会变为空数组，达到编辑自动消失的效果
 *
 * 交互要求：
 *
 * 1. 具有error的输入控件失去焦点时tooltip隐藏
 * 2. 再次聚焦时恢复展示
 * 3. 编辑时tooltip隐藏
 */
export function FormItemWrap({
  children,
  tooltipProps,
  ...rest
}: FormItemWrapProps) {
  const { errors } = Form.Item.useStatus();
  const ref = useRef<TooltipRef>(null);
  const inputsRef = useRef<NodeListOf<HTMLInputElement>>();
  const firstErrorRef = useRef<ReactNode>(null);

  /**
   * 每个FormItemWrap具有唯一id标识
   */
  const id = useRef(++idx);
  const [error, setError] = useState(null);

  const firstError = useMemo(() => {
    return errors?.[0];
  }, [errors]);

  firstErrorRef.current = firstError;

  useEffect(() => {
    // 表单产生错误时变更内部 error 引用
    setError(firstError);
    // experimental
  }, [firstError]);

  useEffect(() => {
    // 查找FormItemWrap下级所有非secret-class 的input节点，添加 focus，blur 事件
    let inputs: NodeListOf<HTMLInputElement>;
    if (ref.current.nativeElement.tagName === 'INPUT') {
      inputs = [
        ref.current.nativeElement,
      ] as unknown as NodeListOf<HTMLInputElement>;
    } else {
      inputs = ref.current.nativeElement.querySelectorAll(
        'input:not(.secret-class)',
      ) as NodeListOf<HTMLInputElement>;
    }

    inputsRef.current = inputs;

    function onFocus() {
      // focus时展示上一个表单错误
      setError(firstErrorRef.current);
      // 控件聚焦时向其他组件发送聚焦事件，并携带当前id
      emitter.emit('focus', id.current);
    }

    function onBlur(event: FocusEvent) {
      // blur 时将error暂时置为空
      let isInternal = false;
      // WARN: 如果获取焦点的input与relatedTarget在同一个FormItem下，可粗糙的认为formItem没有失去焦点
      for (let i = 0; i < inputs.length - 1; i++) {
        if (inputs[i] === event.relatedTarget) {
          isInternal = true;
          break;
        }
      }
      if (!isInternal) {
        setError(null);
      }
    }

    for (let input of inputs) {
      input.addEventListener('focus', onFocus);
      input.addEventListener('blur', onBlur);
    }

    return () => {
      for (let input of inputs) {
        input.removeEventListener('focus', onFocus);
        input.removeEventListener('blur', onBlur);
      }
    };
  }, []);

  useEffect(() => {
    // 监听到其他FormItemWrap的focus事件时，隐藏当前 error
    function onFocus(currentId: number) {
      if (currentId !== id.current) {
        setError(null);
      }
    }

    emitter.addListener('focus', onFocus);
    return () => {
      emitter.removeListener('focus', onFocus);
    };
  }, []);

  return (
    <>
      {/* 如果存在错误将使用tooltip展示，和autocomplete冲突 */}
      <Tooltip
        // getPopupContainer={
        //   tooltipProps?.getPopupContainer ?? ((e) => e.parentElement)
        // }
        ref={ref}
        title={error}
        open={!!error}
        placement="bottom"
        className={prefixCls + '-form-item-error-tooltip'}
      >
        {cloneElement(children, rest)}
      </Tooltip>
    </>
  );
}
