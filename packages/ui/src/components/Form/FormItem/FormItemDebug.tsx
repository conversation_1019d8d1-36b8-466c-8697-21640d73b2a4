import { Button, Form, Input } from '@otakus/design';
import { FormItem, FormItemWrap } from '.';
import { CodeInputDebug } from '@ui/components/Input/CodeInput/CodeInputDebug';

export function FormItemDebug() {
  const [form] = Form.useForm();

  return (
    <Form form={form}>
      <FormItem
        name={'name'}
        rules={[{ required: true, message: '请输入' }]}
        help="x"
      >
        <FormItemWrap>
          <Input placeholder="用户名" disabled />
        </FormItemWrap>
      </FormItem>
      <FormItem
        name={'name'}
        rules={[{ required: true, message: '请输入' }]}
        help="x"
      >
        <FormItemWrap>
          <Input placeholder="用户名" />
        </FormItemWrap>
      </FormItem>

      <FormItem
        name={'password'}
        rules={[{ required: true, message: '请输入' }]}
      >
        <FormItemWrap>
          <CodeInputDebug />
        </FormItemWrap>
      </FormItem>
      <Form.Item>
        <Button htmlType="submit">提交</Button>
      </Form.Item>
    </Form>
  );
}
