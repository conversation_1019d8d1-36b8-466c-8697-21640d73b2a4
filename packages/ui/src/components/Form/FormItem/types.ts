import {
  FormItemProps as OtakusFormItemProps,
  TooltipProps,
} from '@otakus/design';
import EventEmitter from 'events';
import { ReactElement } from 'react';

export interface FormItemWrapProps {
  children?: ReactElement;
  tooltipProps?: TooltipProps;
  name?: string;
}

export interface FormItemProps
  extends OtakusFormItemProps,
    Pick<FormItemWrapProps, 'tooltipProps'> {
  /**
   * 使用 FormItemWrap 组件包裹
   */
  wrap?: boolean;
}
