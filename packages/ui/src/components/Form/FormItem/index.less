@import '../../../index.less';

.otakus-form-item-control-input-content
  .@{info-iam-login-prefix-cls}-form-item-error-tooltip::not(
    .otakus-input-affix-wrapper-focused
  ) {
}

/**
 * TODO: secret-class 临时方案, 优化实现
 */
.@{info-iam-login-prefix-cls} {
  &-form-item-error-tooltip {
  }

  &-form-items {
    .@{info-iam-login-prefix-cls}-form-item {
      margin-bottom: 0;
      position: relative;
    }

    .otakus-form-item-control-input-content {
      > .@{info-iam-login-prefix-cls}-phone-input > .otakus-input-outlined,
      > .otakus-input-outlined:not(.secret-class),
      > .otakus-input-password,
      > .@{info-iam-login-prefix-cls}-otp-input,
      > .otakus-input-affix-wrapper:not(.secret-class) {
        height: 54px;
        border-radius: var(--otakus-border-radius-xl);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        // border-color: var(--otakus-color-border);
      }
    }

    .@{info-iam-login-prefix-cls}-form-item {
      & + .@{info-iam-login-prefix-cls}-form-item {
        // transform: translateY(-1px);
        margin-top: -1px;

        .otakus-form-item-control-input-content {
          //   background: red;
          //   background: red;
          // 补充类型
          > .@{info-iam-login-prefix-cls}-phone-input > .otakus-input-outlined,
          > .otakus-input-outlined:not(.secret-class),
          > .otakus-input-password,
          > .otakus-input-affix-wrapper:not(.secret-class),
          > .@{info-iam-login-prefix-cls}-otp-input {
            //   border-top: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            // border: 0;
          }
        }
      }
    }

    .@{info-iam-login-prefix-cls}-form-item:last-of-type
      .otakus-form-item-control-input-content {
      > .otakus-input-outlined:not(.secret-class),
      > .@{info-iam-login-prefix-cls}-phone-input > .otakus-input-outlined,
      > .otakus-input-password,
      > .@{info-iam-login-prefix-cls}-otp-input,
      > .otakus-input-affix-wrapper:not(.secret-class) {
        border-bottom-left-radius: var(--otakus-border-radius-xl);
        border-bottom-right-radius: var(--otakus-border-radius-xl);
      }
    }

    .@{info-iam-login-prefix-cls}-phone-input .otakus-input-suffix .otakus-btn {
      width: 72px;
    }
  }
}
