import { Form } from '@otakus/design';
import Input from '@ui/components/Input';
import { useLocale, useSize, ViewportSize } from '@iam/login-shared';
import { FormItem, FormItems } from './FormItem';

interface AccountFormProps {
  enableForgetPwd: boolean;
}

const { Item } = Form;

export enum ACCOUNT_FORM_ITEM {
  USER_ACCOUNT = 'user_account',
  PASSWD = 'password',
}

const Index = (props: AccountFormProps) => {
  const { enableForgetPwd } = props;
  const { size } = useSize();
  const __ = useLocale();
  return (
    <FormItems>
      <FormItem
        wrap
        name={ACCOUNT_FORM_ITEM.USER_ACCOUNT}
        // rules={[
        //   { whitespace: true, message: __('account:message:error') },
        //   { required: true, message: __('account:message:error') },
        // ]}
        label=""
      >
        <Input
          topRadius
          placeholder={__('account:placeholder')}
          autoFocus={size !== ViewportSize.xs}
          allowClear
          // autoComplete="new-password"
        />
      </FormItem>
      <FormItem
        wrap
        name={ACCOUNT_FORM_ITEM.PASSWD}
        // rules={[
        //   { whitespace: true, message: __('password:message:error') },
        //   { required: true, message: __('password:message:error') },
        // ]}
      >
        <div style={{ width: '100%' }}>
          <Input
            passwd
            bottomRadius
            placeholder={__('password:message')}
            allowClear
            // autoComplete="new-password"
          />
        </div>
      </FormItem>
    </FormItems>
  );
};

export default Index;
