import {
  GeetestResponse,
  IV3_SCENE_TYPE,
  ServiceErrorCodes,
} from '@iam/login-shared';
import { PhoneInputProps } from '@ui/components/Input/PhoneInput/types';
import { FormInstance, InputRef } from '@otakus/design';
import { OTPRef } from 'antd/es/input/OTP';
import { MutableRefObject } from 'react';

export type SMSFormProps = Pick<PhoneInputProps, 'captcha'> & {
  onSlider?: (id: string) => Promise<GeetestResponse>;
  form?: FormInstance;
  /**
   * 处理发送验证码error
   */
  onFormError?: (e: { code?: ServiceErrorCodes; message: string }) => void;
  codeInputRef?: MutableRefObject<OTPRef>;
  phoneInputRef?: MutableRefObject<InputRef>;
  scene?: IV3_SCENE_TYPE;
};

export type UseSMSParams = SMSFormProps;
