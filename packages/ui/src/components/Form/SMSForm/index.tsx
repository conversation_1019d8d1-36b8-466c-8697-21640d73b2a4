import {
  authApiPrefix,
  authnV3PhoneAreaCodeApi,
  authnV3SmsSendApi,
  getPreCheck,
  IV3Method,
  IV3PerCheckParams,
  request2,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Form, Input } from '@otakus/design';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { PhoneInput } from '@ui/components/Input/PhoneInput/PhoneInput';
import { parseCidFromValue } from '@ui/components/Select/RegionNumber/utils';
import { useCallback, useEffect } from 'react';
import { FormItem, FormItems } from '../FormItem';
import { SMSFormProps } from './types';
import { useSMS } from './useSMS';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';

export enum SMS_FORM_ITEM {
  PHONE_NUMBER = 'phone_number',
  CID = 'cid',
  VERIFY_CODE = 'verify_code',
}

export function SMSForm({
  form,
  captcha,
  onSlider,
  onFormError,
  codeInputRef,
  phoneInputRef,
}: SMSFormProps) {
  const { cid, setCid } = useSMS({ form });
  const { lang, env, scene } = useConfig();
  const locale = useLocale();
  const { size } = useSize();

  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: SMS_FORM_ITEM.VERIFY_CODE, errors: [] }]);
  }, []);

  async function handlePrepareCaptcha() {
    await validatePhone();
    return new Promise<IV3PerCheckParams>((resolve, reject) => {
      getPreCheck(
        env,
        captcha,
        {
          auth_method: IV3Method['SMS'],
          scene_type: scene,
        },
        /**
         * 成功回调
         */
        async function onFulfill(data: IV3PerCheckParams) {
          setTimeout(() => {
            if (size !== ViewportSize.xs) {
              codeInputRef?.current.focus();
            }
          }, 20);
          resolve(data);
        },

        /**
         * 准备滑动验证
         */
        async function onRequestSlider(id: string) {
          return onSlider(id);
        },

        /**
         * 失败回调
         */
        async function onReject() {
          reject();
        },
      );
    });
  }

  /**
   * TODO: 处理编辑时自动校验？
   */
  async function validatePhone() {
    const cid = form.getFieldValue(SMS_FORM_ITEM.CID);
    const phone = (
      (form.getFieldValue(SMS_FORM_ITEM.PHONE_NUMBER) as string) || ''
    ).trim();

    if (cid === '+86') {
      if (phone.length !== 11 || !phone.startsWith('1')) {
        form.setFields([
          {
            name: SMS_FORM_ITEM.PHONE_NUMBER,
            errors: [locale('phone:message:wrongFormat')],
          },
        ]);
        return Promise.reject(locale('phone:message:wrongFormat'));
      }
    }

    form.setFields([{ name: SMS_FORM_ITEM.PHONE_NUMBER, errors: [] }]);
  }

  async function sendCode(
    captcha: IV3PerCheckParams,
    {
      phone,
      cid,
    }: {
      cid: string;
      phone: string;
    },
  ) {
    try {
      const data = await request2.post(
        `${authApiPrefix[env]}/${authnV3SmsSendApi}`,
        {
          captcha,
          phone_number: phone.trim(),
          cid: cid,
          otp_scene: scene,
        },
      );
    } catch (error) {
      onFormError?.(error);
      throw error;
    }
  }

  useEffect(() => {
    form.setFieldValue(
      SMS_FORM_ITEM.CID,
      typeof cid === 'string' ? parseCidFromValue(cid) : cid,
    );
  }, [cid]);

  useEffect(() => {
    // TODO: 默认区号
    setCid('+86|3300');
  }, []);

  return (
    <FormItems>
      <Form.Item name={SMS_FORM_ITEM.CID} hidden>
        <Input />
      </Form.Item>
      <FormItem
        wrap
        name={SMS_FORM_ITEM.PHONE_NUMBER}
        validateFirst
        // rules={[
        //   { required: true, message: locale('phone:message:error') },
        //   { whitespace: true, message: locale('phone:message:error') },
        // ]}
        tooltipProps={{ getPopupContainer: (e) => e }}
      >
        <PhoneInput
          phoneInputRef={phoneInputRef}
          autoFocus={size !== ViewportSize.xs}
          placeholder={locale('phone:placeholder')}
          onPrepareCaptcha={handlePrepareCaptcha}
          captcha={captcha}
          onSendCode={(captcha, data) => {
            return sendCode(captcha, data);
          }}
          selectProps={{
            locale: lang === 'en-US' ? 'en_US' : 'zh_CN',
            value: cid,
            onChange(value) {
              setCid(() => value);
            },
            request: () =>
              request2.post(
                authApiPrefix[env] + '/' + authnV3PhoneAreaCodeApi,
                {},
                { extract: true },
              ),

            getPopupContainer: (e) =>
              e.parentElement.parentElement.parentElement.parentElement,
            dropdownAlign: { targetOffset: [1, -12] },
          }}
        />
      </FormItem>
      {/* 在窗口变化时，会出现遮挡 */}
      <FormItem
        name={SMS_FORM_ITEM.VERIFY_CODE}
        validateFirst
        wrap
        rules={[
          { required: true, message: locale('sms:message:error') },
          { whitespace: true, message: locale('sms:message:error') },
        ]}
        validateTrigger={false}
      >
        <CodeInput
          onCharChange={onCodeCharChange}
          codeInputRef={codeInputRef}
          onPaste={handleOtpPaste('verify_code', form)}
        />
      </FormItem>
    </FormItems>
  );
}
