import { GeetestResponse, ServiceErrorCodes } from '@iam/login-shared';
import { MailInputProps } from '@ui/components/Input/MailInput/types';
import { FormInstance, InputRef } from '@otakus/design';
import { MutableRefObject } from 'react';
import { OTPRef } from 'antd/es/input/OTP';

export interface MailFormProps extends Pick<MailInputProps, 'captcha'> {
  onSlider?: (id: string) => Promise<GeetestResponse>;
  form?: FormInstance;
  /**
   * 处理发送验证码error
   */
  onFormError?: (e: { code: ServiceErrorCodes; message: string }) => void;
  codeInputRef?: MutableRefObject<OTPRef>;
  emailInputRef?: MutableRefObject<InputRef>;
}
