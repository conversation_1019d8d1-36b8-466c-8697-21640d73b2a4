import {
  authApiPrefix,
  authnV3EmailSend<PERSON>pi,
  EMAIL_PATTERN,
  getPreCheck,
  IV3Method,
  IV3PerCheckParams,
  request2,
  useConfig,
  useLocale,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { CodeInput } from '@ui/components/Input/CodeInput';
import { MailInput } from '@ui/components/Input/MailInput/MailInput';
import { FormItem, FormItems } from '../FormItem';
import { MailFormProps } from './types';
import { useCallback } from 'react';
import { handleOtpPaste } from '@ui/components/Form/utils/handleOtpPaste';

export enum EMAIL_FORM_ITEM {
  EMAIL = 'email',
  VERIFY_CODE = 'verify_code',
}

export function MailForm({
  captcha,
  onSlider,
  onFormError,
  emailInputRef,
  codeInputRef,
  form,
}: MailFormProps) {
  const { env, scene } = useConfig();
  const locale = useLocale();
  const { size } = useSize();

  const onCodeCharChange = useCallback(() => {
    form.setFields([{ name: EMAIL_FORM_ITEM.VERIFY_CODE, errors: [] }]);
  }, []);

  const validateEmail = () => {
    const email = (form.getFieldValue('email') || '').trim();
    if (!EMAIL_PATTERN.test(email)) {
      form.setFields([
        {
          name: EMAIL_FORM_ITEM.EMAIL,
          errors: [locale('email:verify:message')],
        },
      ]);
      return Promise.reject(locale('email:verify:message'));
    }
  };

  async function handlePrepareCaptcha() {
    await validateEmail();
    return new Promise<IV3PerCheckParams>((resolve, reject) => {
      getPreCheck(
        env,
        captcha,
        {
          auth_method: IV3Method.Email,
          scene_type: scene,
        },
        /**
         * 成功回调
         */
        async function onFulfill(data: IV3PerCheckParams) {
          resolve(data);
        },

        onSlider,
        /**
         * 失败回调
         */
        async function onReject() {
          reject();
        },
      );
    });
  }

  async function sendCode(
    captcha: IV3PerCheckParams,
    {
      email,
    }: {
      email?: string;
    },
  ) {
    try {
      const data = await request2.post(
        `${authApiPrefix[env]}/${authnV3EmailSendApi}`,
        {
          captcha,
          email: email.trim(),
          otp_scene: scene,
        },
      );
    } catch (error) {
      console.log(error);
      onFormError?.(error);
      throw error;
    }
  }

  return (
    <FormItems>
      <FormItem
        name={EMAIL_FORM_ITEM.EMAIL}
        wrap
        validateFirst
        tooltipProps={{ getPopupContainer: (e) => e }}
        // rules={[
        //   {
        //     required: true,
        //     message: locale('email:message:error'),
        //   },
        //   {
        //     whitespace: true,
        //     message: locale('email:message:error'),
        //   },
        //   {
        //     pattern: EMAIL_PATTERN,
        //     message: locale('email:verify:message'),
        //   },
        // ]}
      >
        <MailInput
          autoFocus={size !== ViewportSize.xs}
          onPrepareCaptcha={handlePrepareCaptcha}
          captcha={captcha}
          onSendCode={(captcha, data) => {
            return sendCode(captcha, data);
          }}
          emailInputRef={emailInputRef}
        />
      </FormItem>
      <FormItem
        name={EMAIL_FORM_ITEM.VERIFY_CODE}
        validateFirst
        wrap
        rules={[
          { required: true, message: locale('sms:message:error') },
          { whitespace: true, message: locale('sms:message:error') },
        ]}
        validateTrigger={false}
      >
        <CodeInput
          onCharChange={onCodeCharChange}
          codeInputRef={codeInputRef}
          onPaste={handleOtpPaste('verify_code', form)}
        />
      </FormItem>
    </FormItems>
  );
}
