import { FormInstance } from '@otakus/design';
import { ClipboardEventHandler } from 'react';

const handleOtpPaste = (name: string, form: FormInstance) => {
  return ((e) => {
    form.setFieldValue(name, '');
    e.preventDefault();
    const content = e.clipboardData.getData('Text');

    const pastedData = content.replaceAll(/[^0-9]/g, '').slice(0, 6);
    setTimeout(() => {
      form.setFieldValue(name, pastedData);
    }, 20);
  }) as ClipboardEventHandler;
};

export { handleOtpPaste };
