import { prefixCls, useAssets, useLocale } from '@iam/login-shared';
// import Lock from '@ui/assets/lock.svg';
import { ReactSVG } from 'react-svg';
import { Button } from '@otakus/design';
import '../index.less';

const Index = () => {
  const __ = useLocale();
  const LockSvg = useAssets('lock.svg');
  return (
    <div className={`${prefixCls}-error-page-wrapper`}>
      <div className={`${prefixCls}-error-page-svg`}>
        <ReactSVG src={LockSvg} />
      </div>
      <span className={`${prefixCls}-error-page-title`}>
        {__('error:page:lock:title')}
      </span>
      <span className={`${prefixCls}-error-page-desc`}>
        {__('error:page:lock:desc')}
      </span>
      <Button className={`${prefixCls}-error-page-default`}>
        {__('error:page:lock:btn:text')}
      </Button>
    </div>
  );
};
export default Index;
