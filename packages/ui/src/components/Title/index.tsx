import { Typography } from '@otakus/design';
import { prefixCls, useConfig, useSize, ViewportSize } from '@iam/login-shared';
import './index.less';
import { useEffect, useState } from 'react';

const { Title } = Typography;

interface TitleProps {
  title: string;
}

const Index = (props: TitleProps) => {
  const { lang } = useConfig();
  const { title } = props;
  const { size } = useSize();

  const [level, setLevel] = useState<number>(3);
  const isOverflowing = (element: Element) => {
    return (
      element.scrollHeight >= element.clientHeight ||
      element.scrollWidth >= element.clientWidth
    );
  };
  useEffect(() => {
    setTimeout(() => {
      const defaultLevel = size === ViewportSize.xs ? 3 : 2;
      const scrollLevel = size === ViewportSize.xs ? 4 : 3;
      const targetElement = document.querySelector('.info-iam-login-title');
      if (targetElement) {
        isOverflowing(targetElement)
          ? setLevel(scrollLevel)
          : setLevel(defaultLevel);
      }
    }, 10);
  }, [size, title]);

  return (
    //@ts-ignore
    <Title className={`${prefixCls}-title`} ellipsis level={level}>
      {title}
    </Title>
  );
};

export default Index;
