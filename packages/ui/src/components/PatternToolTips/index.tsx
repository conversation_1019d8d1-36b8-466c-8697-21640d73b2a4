import type { IV3GetPattern } from '@iam/login-shared';
import { prefixCls, useLocale } from '@iam/login-shared';
import defaultSvg from '@ui/assets/reset-pattern-default.svg';
import activeSvg from '@ui/assets/reset-pattern-active.svg';
import { ReactSVG } from 'react-svg';
import './index.less';

interface PatternToolTipsProps {
  pattern: IV3GetPattern[];
  extraDescription?: string;
}

const Icon = ({ pass }: { pass: boolean }) => {
  return (
    <ReactSVG
      className={`${prefixCls}-pattern-icon`}
      src={pass ? activeSvg : defaultSvg}
    />
  );
};

const Item = (props: IV3GetPattern) => {
  const { pass, description } = props;
  return (
    <div className={`${prefixCls}-pattern-item-wrapper`}>
      <Icon pass={pass} />
      <span className={`${prefixCls}-pattern-item-text`}>{description}</span>
    </div>
  );
};

const Index = (props: PatternToolTipsProps) => {
  const __ = useLocale();
  const { pattern, extraDescription } = props;
  if (!pattern) {
    return null;
  }
  return (
    <div className={`${prefixCls}-pattern-wrapper`}>
      {pattern.map((item, index) => {
        return <Item {...item} key={index}></Item>;
      })}
      <span>{extraDescription}</span>
    </div>
  );
};

export default Index;
