import Visual from '../Visual';
import Navigation from '../Navigation';
import './index.less';
import {
  getIsWaveEnv,
  prefixCls,
  useConfig,
  useLocale,
  usePageConfig,
  useScaleStyle,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import React, { FC } from 'react';
import cls from 'classnames';
import Footer from '@ui/components/Footer';
import Empty from '@ui/components/Empty';
import VisualFooter from '@ui/components/VisualFooter';
import { useUpdateEffect } from 'ahooks';

interface WrapperProps {
  hiddenFooter?: boolean;
  hiddenBlock?: boolean;
  checkAgreementPrivacy?: boolean;
}

export const OuterWithoutVisual: FC = (props) => {
  const { children } = props;
  const { size } = useSize();
  const isWaveEnv = getIsWaveEnv();
  const { data } = usePageConfig();
  const { operation_banner_pic = '', check_agreement_privacy } =
    data?.data || {};
  const showOperation =
    !!operation_banner_pic &&
    (size === ViewportSize.lg || size === ViewportSize.md);

  return (
    <div
      className={cls(`${prefixCls}-wrapper`, {
        [`${prefixCls}-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <div
        className={cls(`${prefixCls}-wrapper-navigation`, {
          [`${prefixCls}-wrapper-navigation-wave`]: isWaveEnv,
          [`${prefixCls}-wrapper-navigation-xs`]: size === ViewportSize.xs,
        })}
      >
        <Navigation />
      </div>
      <div
        className={cls(`${prefixCls}-wrapper-content`, {
          [`${prefixCls}-wrapper-content-xs`]: size === ViewportSize.xs,
        })}
      >
        {children}
      </div>
    </div>
  );
};

export const OuterMost: FC = (props) => {
  const { children } = props;
  const { size } = useSize();
  const isWaveEnv = getIsWaveEnv();

  const { data } = usePageConfig();
  const { operation_banner_pic = '' } = data?.data || {};
  const showOperation =
    !!operation_banner_pic &&
    (size === ViewportSize.lg || size === ViewportSize.md);

  return (
    <div
      className={cls(`${prefixCls}-wrapper`, {
        [`${prefixCls}-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      <div
        className={cls(`${prefixCls}-wrapper-navigation`, {
          [`${prefixCls}-wrapper-navigation-wave`]: isWaveEnv,
          [`${prefixCls}-wrapper-navigation-xs`]: size === ViewportSize.xs,
        })}
      >
        <Navigation />
      </div>
      <div
        className={cls(`${prefixCls}-wrapper-content`, {
          [`${prefixCls}-wrapper-content-xs`]: size === ViewportSize.xs,
        })}
      >
        {children}
      </div>
      <div
        className={cls(`${prefixCls}-visual-wrapper`, {
          [`${prefixCls}-visual-wrapper-sm`]:
            size === ViewportSize.xs || size === ViewportSize.sm,
        })}
      >
        {/*<VisualHead />*/}
        <Visual />
        <VisualFooter />
      </div>
    </div>
  );
};

export const Wrapper: FC<WrapperProps> = (props) => {
  const {
    children,
    hiddenFooter = false,
    hiddenBlock = false,
    checkAgreementPrivacy = false,
  } = props;
  const { size } = useSize();
  const scaleStyle = useScaleStyle();
  return (
    <OuterMost>
      <div
        className={cls(`${prefixCls}-block-wrapper`, {
          [`${prefixCls}-block-wrapper-xs`]: size === ViewportSize.xs,
          [`${prefixCls}-block-wrapper-sm`]: size === ViewportSize.sm,
        })}
      >
        {!hiddenBlock && (
          <div
            className={cls(`${prefixCls}-block-main`, {
              [`${prefixCls}-block-main-xs`]: size === ViewportSize.xs,
            })}
          >
            <div className={cls(`${prefixCls}-block-main-content`, {})}>
              <div
                className={cls(`${prefixCls}-block-inner`, {
                  [`${prefixCls}-block-inner-lg`]: size === ViewportSize.lg,
                  [`${prefixCls}-block-inner-sm`]: size === ViewportSize.sm,
                  [`${prefixCls}-block-inner-xs`]: size === ViewportSize.xs,
                  [`${prefixCls}-block-inner-hidden-footer`]:
                    size !== ViewportSize.xs && hiddenFooter,
                })}
                style={scaleStyle}
              >
                {children}
              </div>
            </div>
          </div>
        )}
        {!hiddenFooter && <Footer />}
      </div>
    </OuterMost>
  );
};

const Index: FC<WrapperProps> = (props) => {
  const { children, hiddenFooter = false, hiddenBlock = false } = props;
  const { loading, data, refreshAsync } = usePageConfig();
  const __ = useLocale();
  const { lang } = useConfig();
  useUpdateEffect(() => {
    if (lang) {
      refreshAsync().then();
    }
  }, [lang]);

  if (
    !loading &&
    (!data || data?.code !== 0 || data?.data?.auth_method_configs?.length === 0)
  ) {
    return (
      <Empty title={__('access:restricted')} description={data?.message} />
    );
  }
  return (
    <Wrapper
      hiddenFooter={hiddenFooter}
      hiddenBlock={hiddenBlock}
      checkAgreementPrivacy={data?.data?.check_agreement_privacy}
    >
      {children}
    </Wrapper>
  );
};

export default Index;
