@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  --base-width: 360px;

  &-wrapper {
    width: 100%;
    height: 100%;
    //background: #fff;
    display: flex;
    flex-direction: column;
  }

  &-wrapper-navigation {
    // position: fixed;
    width: 100%;
    z-index: 99;
  }

  &-wrapper-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    min-height: 0;
    justify-content: flex-end;
    z-index: 100;
  }

  &-wrapper-content-xs {
    // height: calc(100% - 184px);
    // margin-top: 28px;
  }

  &-visual-wrapper {
    height: 100%;
    width: 54.16%;
    overflow: hidden;
    // margin-top: -6.25vw;
    position: fixed;
    left: 0;
    top: 0;
    // display: flex;
    // flex-direction: column;
  }

  &-block-wrapper {
    height: 100%;
    // padding-top: 52px;
    // padding-bottom: 54px;
    display: flex;
    // align-items: center;
    // justify-content: center;
    flex-direction: column;
    width: 50%;
    // position: fixed;
    right: 0;
    // overflow-y: auto;
    top: 0;
  }

  &-block-wrapper-xs {
    width: 100%;
    right: unset;
    top: 126px;
    justify-content: flex-start;
    position: unset;
    // padding: 0 20px;
    // overflow-y: auto;
  }

  &-block-main {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // overflow-y: auto;
    flex: 1;

    &-xs {
      padding: 0 20px;
      justify-content: flex-start;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      //background: #fff;
    }
  }

  &-block-inner {
    width: 360px;
    min-width: 360px;
    // flex: 1;
    position: relative;
  }

  &-block-inner-lg {
    //width: calc((100vw / 1920) * 360);
  }

  &-visual-wrapper-sm {
    display: none;
  }

  &-visual-wrapper-navigation-wave {
    display: none;
  }

  &-wrapper-xs {
    background: linear-gradient(
        174deg,
        rgba(78, 95, 246, 0.06) 7.78%,
        rgba(78, 95, 246, 0.02) 16.38%,
        rgba(78, 95, 246, 0) 20.98%
      ),
      #fff;
  }

  &-block-inner-sm {
    margin-bottom: 24px;
  }

  &-wrapper-navigation-xs {
    position: unset;
  }

  &-block-wrapper-sm {
    width: 100%;
  }

  &-block-inner-xs {
    width: 100%;
    min-width: unset;
  }

  &-block-inner-hidden-footer {
    margin-top: -76px;
  }
}
