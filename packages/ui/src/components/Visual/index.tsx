import { useEffect, useMemo, useRef, useState } from 'react';
import ANIMATION_LIST from './animationList';
import {
  getTLD,
  useAssetsPrefix,
  useConfig,
  useLocale,
  useSize,
} from '@iam/login-shared';
import './index.less';
import classNames from 'classnames';

function shuffleArray(array: any[]) {
  let shuffledArray = [...array];
  for (let i = shuffledArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]];
  }
  return shuffledArray;
}

const preloadImage = (url: string) => {
  const img = new Image();
  img.src = url;
};

const Index = () => {
  const [list, setList] = useState<(typeof ANIMATION_LIST)[]>([]);
  const [index, setIndex] = useState(-1);
  const curIndex = useRef(-1);
  const tld = getTLD();
  const { prefix } = useAssetsPrefix();
  const __ = useLocale();
  const { lang } = useConfig();
  const { width } = useSize();
  const _width = width * 0.5416;
  const _height = (280 / 780) * _width;
  const _heightTop = _width * 0.1025;

  useEffect(() => {
    ANIMATION_LIST.forEach((i) => {
      preloadImage(`${prefix}/name-${i.key}-next.webp`);
      preloadImage(`${prefix}/back-${i.key}.webp`);
      preloadImage(`${prefix}/role-${i.key}.webp`);
    });
    const arr = shuffleArray(ANIMATION_LIST);
    setIndex(0);
    curIndex.current = 0;
    changeImg(arr[0]);
    setList(arr);
  }, []);

  const changeImg = (cur: any) => {
    if (cur) {
      const dom = document.querySelector('#svgContainer');
      const roleNameDom = document.querySelector('#roleNameAnimationRef');
      const roleNameImgDom = document.querySelector(
        '#roleNameAnimationRef img',
      );
      const roleBackDom = document.querySelector('#roleBackAnimationRef');
      const roleDom = document.querySelector('#roleAnimationRef');
      if (roleNameDom) {
        roleNameDom.setAttribute('x', cur?.text.x);
        roleNameDom.setAttribute('y', cur?.text.y);
      }

      if (roleNameImgDom) {
        roleNameImgDom.setAttribute(
          'src',
          `${prefix}/name-${cur?.key}-next.webp`,
        );
      }
      if (roleBackDom) {
        roleBackDom.setAttribute('href', `${prefix}/back-${cur?.key}.webp`);
        roleBackDom.setAttribute('x', cur?.back.x);
        roleBackDom.setAttribute('y', cur?.back.y);
      }
      if (roleDom) {
        roleDom.setAttribute('href', `${prefix}/role-${cur?.key}.webp`);
      }

      if (dom) {
        dom.setAttribute('class', cur?.key || '');
      }
    }
  };

  useEffect(() => {
    const changeProject = () => {
      let nexIndex = curIndex.current;

      if (nexIndex + 1 > list.length - 1) {
        nexIndex = 0;
      } else {
        nexIndex += 1;
      }
      curIndex.current = nexIndex;
      setIndex(nexIndex);
      changeImg(list[nexIndex]);
    };
    const dom = document.querySelector('#roleAnimationRef');
    dom?.addEventListener('animationiteration', changeProject);
    return () => {
      dom?.removeEventListener('animationiteration', changeProject);
    };
  }, [list]);

  const lineGroup = useMemo(() => {
    return new Array(101).fill(null).map((_i, k) => {
      // 每根斜线的间距7.8px
      const x = k * 7.8;
      // 从左往右计算斜线的坐标
      return {
        x1: x,
        y1: 900,
        x2: 0,
        y2: 900 - (x / 665.19) * 1750.83,
      };
    });
  }, []);

  useEffect(() => {
    // TODO: 需要处理中英文切换高度变化
    const dom = document.querySelector('.titleBox .active .text');
    const h = dom?.scrollHeight;
    const preStyle = document.querySelector('#animationTitleTextShowHidden');
    if (preStyle) {
      preStyle.remove();
    }
    const style = document.createElement('style');
    style.id = 'animationTitleTextShowHidden';
    document.head.appendChild(style);
    const sheet = style.sheet;
    const collapseKeyframes = `
    @keyframes animationTitleTextShowHidden {
      0% {
    opacity: 0;
    height: 0;
    margin-top: 0;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 0.6;
    height: ${h}px;
    margin-top: 12px;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 0.6;
    height:  ${h}px;
    margin-top: 12px;

    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    height: 0;
    margin-top: 0px;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
    }
`;
    sheet.insertRule(collapseKeyframes, sheet.cssRules.length);
  }, [index]);

  return (
    <div className="box">
      <div className="svgBox">
        <div className="svgBoxDiv">
          <div
            className="mantle-top"
            style={{ width: _width, height: _heightTop }}
          ></div>
          <svg
            id="svgContainer"
            viewBox="0 0 780 900"
            xmlns="http://www.w3.org/2001/svg"
            style={{ display: 'block' }}
          >
            <g style={{ opacity: '50%' }}>
              {lineGroup?.map((i, k) => {
                if (k <= 36 || k >= 64) {
                  // 36-64之间是半字母A的距离，留出来渲染半个字母A
                  return (
                    <line
                      key={k}
                      {...i}
                      stroke="rgba(22, 33, 99, 0.09)"
                      strokeWidth="2"
                    />
                  );
                }
                return null;
              })}
            </g>
            {/* <!-- 绘制半个字母A --> */}
            <g className="bgWord">
              <path
                d="M-467 900
                H-248.483
                C-248.483 845 -178.687 730.125 -2.00751 730.125
                C174.672 730.125 223.911 845 223.911 845
                H471
                L150.067 0
                L-150.067 0
                L-467 845Z
                M-200.779 643.019
                L-0.188232 149.635
                L190.429 643.019
                C194.429 643.019 130.141 591.298 -2.00751 591.298
                C-134.156 591.298 -200.779 643.019 -200.779 643.019Z"
              />

              <defs>
                {ANIMATION_LIST.map((i, k) => (
                  <linearGradient
                    key={k}
                    id={`defs-${i.key}`}
                    x1="2"
                    y1="0"
                    x2="2"
                    y2="845"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor={i?.bgColor || ''} stopOpacity="0.2" />
                    <stop
                      offset="1"
                      stopColor={i?.bgColor || ''}
                      stopOpacity="0"
                    />
                  </linearGradient>
                ))}
              </defs>
            </g>
            {/* <!-- 使用foreignObject嵌入HTML --> */}
            <foreignObject width="388" height="258" id="roleNameAnimationRef">
              <div className="nameBox">
                <img />
              </div>
            </foreignObject>

            <image
              id="roleBackAnimationRef"
              className="roleBack"
              width="364"
              height="200"
            ></image>
            <image
              id="roleAnimationRef"
              className="role"
              x="-29"
              y="60"
              width="800"
              height="800"
            ></image>
          </svg>
          <div
            className="mantle"
            style={{ width: _width, height: _height }}
          ></div>
        </div>
        <div className="titleBox">
          {list.map((i, k) => (
            <div key={k} className={`item ${index === k ? 'active' : ''}`}>
              <div className="line"></div>
              <div className={classNames('text', `text-${lang}`)}>
                {/*@ts-ignore*/}
                {__(`animation:name:${i.key}`)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Index;
