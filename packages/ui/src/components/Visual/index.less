.box {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  flex: 1;

  position: relative;

  .titleBox {
    display: flex;
    width: 24px;
    height: 100%;
    position: absolute;
    left: 24px;
    top: 0;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-direction: column;

      .line {
        width: 12px;
        height: 1px;
        //border: 1px solid var(--otakus-color-text-description);
        background: var(--otakus-color-text-description);

        opacity: 0.4;
      }

      .text {
        // width: 12px;
        line-height: 1;
        height: 0;
        opacity: 0;
        white-space: nowrap;
        font-size: 12px;
        overflow: hidden;
        text-align: center;
        word-wrap: break-word;
        color: var(--otakus-color-text-heading);
        text-transform: uppercase;
        font-weight: 400;
        text-orientation: mixed;
        writing-mode: vertical-lr;
        -webkit-font-smoothing: antialiased;

        &-zh-CN {
          letter-spacing: 0.6em;
        }

        &-en-US {
          letter-spacing: 2px;
        }
      }
    }

    .active {
      .line {
        background: transparent;
        border: 1px solid var(--otakus-color-text-description);
        animation: lineOpenClose 4s;
      }

      .text {
        animation: animationTitleTextShowHidden 4s;
      }
    }
  }

  .svgBox {
    // width: 780px;
    // height: 900px;
    //border: 1px solid goldenrod;
    //max-width: 1155px;
    width: 100%;

    #svgContainer {
      //width: 100%;
      //min-width: 782px;
      //min-height: 900px;
      max-height: 100vh;

      .nameBox {
        width: 281px;
        height: 102px;
        overflow: hidden;
        margin-left: 5px;
        transform: skew(20.84deg);

        img {
          //width: 281px;
          //height: 102px;
          //margin-left: 10px;
          //transform: skew(-20.84deg);
          //object-fit: cover;
          width: 300px;
          height: 102px;
          transform: skew(-20.84deg);
          object-fit: cover;
        }
      }

      image.role,
      image.roleBack {
        opacity: 0;
      }

      .bgWord {
        path {
          animation: showHide 4s cubic-bezier(0, 0, 0.58, 1) infinite;
        }
      }
    }
  }

  .ys,
  .b2,
  .wd,
  .b3 {
    .nameBox {
      animation: textBoxUpInOut 4s infinite;

      img {
        animation: textUpIn 4s cubic-bezier(0, 0, 0.58, 1) infinite;
      }
    }

    image.role {
      animation: roleUpInOut 4s infinite;
    }

    image.roleBack {
      animation: roleBackUpInOut 4s infinite;
    }

    .bgWord {
      path {
        fill: url(#defs-ys);
      }
    }
  }

  .zzz,
  .xt,
  .mys,
  .lm,
  .luming {
    .nameBox {
      animation: textBoxDownInOut 4s infinite;

      img {
        animation: textDownIn 4s cubic-bezier(0, 0, 0.58, 1) infinite;
      }
    }

    image.role {
      animation: roleUpInOut 4s infinite;
    }

    image.roleBack {
      animation: roleBackDownInOut 4s infinite;
    }

    .bgWord {
      path {
        fill: url(#defs-zzz);
      }
    }
  }

  .b2 {
    .bgWord {
      path {
        fill: url(#defs-b2);
      }
    }
  }

  .xt {
    .bgWord {
      path {
        fill: url(#defs-xt);
      }
    }
  }

  .wd {
    .bgWord {
      path {
        fill: url(#defs-wd);
      }
    }
  }

  .mys {
    .bgWord {
      path {
        fill: url(#defs-mys);
      }
    }
  }

  .lm {
    .bgWord {
      path {
        fill: url(#defs-lm);
      }
    }
  }

  .luming {
    .bgWord {
      path {
        fill: url(#defs-luming);
      }
    }
  }

  .b3 {
    .bgWord {
      path {
        fill: url(#defs-b3);
      }
    }
  }

  .right {
    width: 40%;
    display: flex;
    border: 1px solid green;
    justify-content: center;
    align-items: center;
  }

  img[src=''] {
    display: none;
  }

  img:not([src]) {
    display: none;
  }
}

@keyframes textUpIn {
  0% {
    opacity: 0;
    transform: skew(-20.84deg) translate(0, 108px);
  }
  20%,
  100% {
    opacity: 1;
    transform: skew(-20.84deg) translate(0, 0);
  }
}

@keyframes textDownIn {
  0% {
    opacity: 0;
    transform: skew(-20.84deg) translate(0, -108px);
  }
  20%,
  100% {
    opacity: 1;
    transform: skew(-20.84deg) translate(0, 0);
  }
}

@keyframes roleUpInOut {
  0% {
    opacity: 0;
    transform: translate(-20px, 120px);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    transform: translate(0, 0);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    transform: translate(0, 0);
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    transform: translate(10px, 20px);
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes roleBackUpInOut {
  0% {
    opacity: 0;
    transform: translate(27px, 72px);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    transform: translate(0, 0);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes roleBackDownInOut {
  0% {
    opacity: 0;
    transform: translate(-27px, -72px);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    transform: translate(0, 0);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes textBoxUpInOut {
  0% {
    opacity: 0;
    transform: skew(20.84deg) translate(22px, 58px);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    transform: skew(20.84deg) translate(0, 0);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes textBoxDownInOut {
  0% {
    opacity: 0;
    transform: skew(20.84deg) translate(-22px, -58px);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    transform: skew(20.84deg) translate(0, 0);
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes showHide {
  0% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 1;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

@keyframes lineOpenClose {
  0% {
    opacity: 0.4;
    height: 1px;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  20% {
    opacity: 0.6;
    height: 12px;
    animation-timing-function: cubic-bezier(0, 0, 0.58, 1);
  }
  85% {
    opacity: 0.6;
    height: 12px;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
  100% {
    opacity: 0.4;
    height: 1px;
    animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
  }
}

.svgBoxDiv {
  position: relative;
  width: 100%;
}

.mantle {
  background: linear-gradient(0deg, #fff 41.9%, rgba(255, 255, 255, 0) 99.26%);
  position: absolute;
  bottom: 0;
}

.mantle-top {
  background: linear-gradient(0deg, rgba(255, 255, 245, 0) 0.1%, #fff 100%);
  position: absolute;
  top: 0;
}
