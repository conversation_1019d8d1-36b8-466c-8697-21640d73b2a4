import { GetProps, Modal } from '@otakus/design';
import cls from 'classnames';
import { prefixCls } from '@iam/login-shared';
import './index.less';

type ModalProps = GetProps<typeof Modal>;

interface MaskProps extends ModalProps {}

const Index = (props: MaskProps) => {
  const {
    children,
    footer = null,
    closable = false,
    className,
    width = '456px',
    ...rest
  } = props;
  return (
    <>
      <Modal
        footer={footer}
        closable={closable}
        className={cls(className, `${prefixCls}-mask`)}
        width={width}
        {...rest}
      >
        {children}
      </Modal>
      {/*先不实现*/}
      {/*<div className={`${prefixCls}-mask-footer`}>*/}
      {/*  <Footer />*/}
      {/*</div>*/}
    </>
  );
};

export default Index;
