@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-wave-quick-login-wrapper {
    border-radius: 20px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--otakus-color-border);
    width: 100%;

    &:hover {
      border: 1px solid var(--otakus-color-primary-hover);
      box-shadow: 0px 0px 0px 2px var(--otakus-control-outline);
    }

    .otakus-tooltip {
      max-width: 248px;
    }
  }

  &-wave-quick-login-avatar {
    border-radius: var(--otakus-border-radius-xl);
    height: 60px;
    width: 60px;
    border: 1px solid var(--otakus-color-split);
  }

  &-wave-quick-login-submit {
    margin-top: 12px;
    width: 100%;
    height: 54px;
    border-radius: 12px;
  }

  &-wave-quick-login-name {
    color: rgba(2, 4, 19, 0.87);
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    margin-top: 16px;
    max-width: 320px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &-wave-quick-login-name-last {
    font-size: 14px;
    font-weight: 400;
    color: var(--otakus-color-text-secondary);
    margin-top: 4px;
  }

  &-wave-quick-name-wrapper {
    width: 320px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 16px;
  }

  &-wave-quick-first-name {
    color: rgba(2, 4, 19, 0.87);
    font-size: 20px;
    margin-bottom: 4px;
    font-weight: 600;
  }

  &-wave-quick-second-name {
    color: var(--otakus-color-text-secondary);
    font-size: 14px;
    font-weight: 400;
    font-family: Poppins;
  }

  &-wave-quick-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-wave-quick-title {
    margin-bottom: 36px;
  }

  &-login-form-remember-checkbox-margin {
    margin-top: 16px;
  }
}
