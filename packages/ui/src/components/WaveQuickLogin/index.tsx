import {
  dispatch,
  message,
  OtpAndTicketRes,
  prefixCls,
  quickLogin,
  useAssets,
  useConfig,
  useLocale,
  usePageConfig,
  WaveUserInfo,
} from '@iam/login-shared';
import { useThrottleFn, useToggle } from 'ahooks';
import './index.less';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Checkbox, Tooltip } from '@otakus/design';
import Title from '@ui/components/Title';

interface WaveLoginProps {
  getTicketAndOpenOTPResponse?: () => Promise<OtpAndTicketRes>;
  userInfo?: WaveUserInfo;
  cb?: (ticket?: string) => void;
}

const Index = (props: WaveLoginProps) => {
  const __ = useLocale();
  const { env, lang } = useConfig();
  const [loading, { toggle }] = useToggle(false);
  const { userInfo, getTicketAndOpenOTPResponse, cb } = props;
  const { avatar, name_cn, name_en } = userInfo || {};
  const imgSrc = useAssets('wave-avatar.png');
  const { data } = usePageConfig();
  const [firstNameTitle, setFirstNameTitle] = useState<string>('');
  const [lastNameTitle, setLastNameTitle] = useState<string>('');
  const [rememberMe, setRemember] = useState<boolean>(false);
  const { data: loginConfig } = usePageConfig();
  const { session_expiration } = loginConfig?.data || {};

  const appName = useMemo(() => {
    if (!data) return '';
    return data?.data?.app_name || '';
  }, [data]);

  const handleWaveLogin = async () => {
    toggle();
    const data = await getTicketAndOpenOTPResponse?.();
    if (data?.code === 0) {
      const { otp, ticket } = data?.data;
      // await refreshZtTokenInWave();
      const res = await quickLogin(otp, ticket, env, rememberMe, false);
      if (res?.code === 0) {
        cb?.(res?.data?.ticket);
      }
    } else {
      if (data?.code === -1000001) {
        message.error(__('wave:quick:login:wave:params:error:message'));
      } else if (data?.code === -1) {
        message.error(__('wave:quick:login:wave:error:message'));
      } else if (data?.code === -1000005) {
        message.error(__('wave:quick:login:wave:cancel:message'));
      } else if (data?.code === -1001000) {
        message.error(__('wave:quick:login:wave:cancel:message'));
      } else {
        message.error(__('wave:quick:login:wave:timeout:error:message'));
      }
    }
    toggle();
  };

  const { run: submit } = useThrottleFn(handleWaveLogin, {
    wait: 2000,
    trailing: false,
  });

  const name = useMemo(() => {
    const first_name = lang === 'zh-CN' ? name_cn : name_en;
    const last_name = lang === 'zh-CN' ? name_en : name_cn;
    return {
      first_name,
      last_name,
    };
  }, [lang, name_cn, name_en]);

  useEffect(() => {
    const firstNameElement = document.querySelector('#wave-first-name');
    const isOverflowing = (element: Element) => {
      return (
        element.scrollHeight > element.clientHeight ||
        element.scrollWidth > element.clientWidth
      );
    };
    const resizeObserver = new ResizeObserver(() => {
      if (isOverflowing(firstNameElement)) {
        setFirstNameTitle(name?.first_name);
      }
    });
    resizeObserver.observe(firstNameElement);
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    const lastNameElement = document.querySelector('#wave-last-name');
    const isOverflowing = (element: Element) => {
      return (
        element.scrollHeight > element.clientHeight ||
        element.scrollWidth > element.clientWidth
      );
    };
    const resizeObserver = new ResizeObserver(() => {
      if (isOverflowing(lastNameElement)) {
        setLastNameTitle(name?.last_name);
      }
    });
    resizeObserver.observe(lastNameElement);
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className={`${prefixCls}-wave-quick-container`}>
      <div className={`${prefixCls}-wave-quick-title`}>
        {appName && <Title title={__('otk:title') + appName} />}
      </div>
      <div className={`${prefixCls}-wave-quick-login-wrapper`}>
        <img
          className={`${prefixCls}-wave-quick-login-avatar`}
          src={avatar || imgSrc}
          alt="wave_avatar"
        />
        <Tooltip
          title={firstNameTitle}
          placement="top"
          getPopupContainer={(e) => e.parentElement}
        >
          <div
            className={`${prefixCls}-wave-quick-login-name`}
            id={'wave-first-name'}
          >
            <span>{name?.first_name}</span>
          </div>
        </Tooltip>
        <Tooltip
          title={lastNameTitle}
          placement="top"
          getPopupContainer={(e) => e.parentElement}
        >
          <div
            className={`${prefixCls}-wave-quick-login-name ${prefixCls}-wave-quick-login-name-last`}
            id={'wave-last-name'}
          >
            <span>{name?.last_name}</span>
          </div>
        </Tooltip>
        <Checkbox
          checked={rememberMe}
          onChange={(e) => {
            // for wave scan
            dispatch(
              {
                rememberMe: e.target.checked,
              },
              false,
              'login-form-remember-checkbox',
            );
            setRemember(e.target.checked);
          }}
          className={`${prefixCls}-login-form-remember-checkbox ${prefixCls}-login-form-remember-checkbox-margin`}
        >
          {session_expiration}
        </Checkbox>
        <Button
          type="primary"
          className={`${prefixCls}-wave-quick-login-submit`}
          size="large"
          onClick={submit}
          loading={loading}
        >
          {__('wave:quick:login:btn:text')}
        </Button>
      </div>
    </div>
  );
};

export default Index;
