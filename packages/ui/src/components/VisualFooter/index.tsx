import { prefixCls, useSize } from '@iam/login-shared';
import './index.less';
import Copyright from '@ui/components/Copyright';

const Index = () => {
  const { width } = useSize();
  const _width = width * 0.5416;
  const _height = 0.1025 * _width;
  return (
    <div
      className={`${prefixCls}-visual-footer-wrapper`}
      // style={{ width: `${_width}px`, height: `${_height}px` }}
    >
      <Copyright />
    </div>
  );
};

export default Index;
