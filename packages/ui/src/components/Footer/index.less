@import '../../index.less';

.@{info-iam-login-prefix-cls} {
  &-footer-wrapper {
    // height: 80px;
    padding: 24px 24px 24px;
    margin-top: auto;
    display: flex;
    justify-content: flex-end;
    color: var(--otakus-color-text-description);
    font-size: 14px;
    // position: fixed;
    bottom: 0;
    flex-direction: column;
    align-items: center;
  }

  &-footer-wrapper-xs {
    // height: 104px;
    padding: 24px 20px 20px;
    width: 100%;
    justify-content: flex-end;
    z-index: 99;
    background: #fff;
  }

  &-policy-title {
    text-decoration: underline;
    margin-left: 4px;
    cursor: pointer;
  }

  &-policy-title:hover {
    color: var(--otakus-color-primary-hover);
  }

  &-policy-title:active {
    color: var(--otakus-color-primary-active);
  }

  &-footer-register {
    margin-top: 12px;
  }

  &-footer-register-link {
    text-decoration: underline;
    margin-top: 12px;
    cursor: pointer;
    margin-left: 4px;
    color: #4e5ff6;
  }

  &-footer-register-link:hover {
    color: var(--otakus-color-primary-hover);
  }

  &-footer-register-link:active {
    color: var(--otakus-color-primary-active);
  }

  &-footer-privacy-title {
    color: #18253d;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    text-align: center;
    display: inline-block;
    width: 100%;
  }

  &-footer-privacy-title-xs {
    color: #18253d;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    width: 100%;

    .info-iam-login-title {
      font-size: 18px;
    }
  }

  &-footer-modal {
    .otakus-modal-content {
      overflow: auto;
      height: 80vh;
      max-height: 80vh;
      padding: 48px 32px 32px !important;
      border-radius: 24px;
    }

    .otakus-modal-body {
      overflow: auto;
      height: calc(80vh - 80px);
    }
  }

  &-footer-protocol {
    text-align: center;
  }

  &-footer-protocol-checkbox {
    margin-right: 8px;
  }
}
