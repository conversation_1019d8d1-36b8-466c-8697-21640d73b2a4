import {
  authnV3Privacy,
  AuthPrivacyRes,
  CURRENT_METHOD_FOR_PROTOCOL_KEY,
  goToRegister,
  IV3Method,
  prefixCls,
  QUICK_LOGIN_FOR_PROTOCOL_KEY,
  request,
  subscribe,
  useConfig,
  useLocale,
  usePageConfig,
  usePolicy,
  useScaleStyle,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { useRequest } from 'ahooks';
import './index.less';
import { useEffect, useState } from 'react';
import cls from 'classnames';
import { Checkbox } from '@otakus/design';
import { CheckboxChangeEvent } from '@otakus/design/es/checkbox';
import PrivacyDetailModal from '@ui/components/Footer/PrivacyDetailModal';

const Index = () => {
  const { data } = usePageConfig();
  // 防止空指针
  const { login_page_protocol, enable_user_register, check_agreement_privacy } =
    data?.data || {};
  const [privacyContent, setPrivacyContent] = useState<
    AuthPrivacyRes['data'][]
  >([]);
  const { authApi, env, clientId, lang, privacyChecked, handlePrivacyChecked } =
    useConfig();
  const { size } = useSize();
  const __ = useLocale();
  const [showProtocol, setShowProtocol] = useState(false);
  const [active, setActive] = useState<IV3Method | undefined>(undefined);
  const [quickLoginPage, setQuickLoginPage] = useState<boolean>(false);
  const scaleStyle = useScaleStyle();
  const { handleShowPolicy, handleCancel, visible, privacy } = usePolicy();

  useEffect(() => {
    subscribe((val: { visible: boolean }) => {
      setQuickLoginPage(val?.visible);
    }, QUICK_LOGIN_FOR_PROTOCOL_KEY);
    subscribe((val: { method: IV3Method }) => {
      setActive(val?.method);
    }, CURRENT_METHOD_FOR_PROTOCOL_KEY);
  }, []);

  useEffect(() => {
    if (quickLoginPage) {
      setShowProtocol(false);
    } else {
      if (active === IV3Method.WaveQR) {
        setShowProtocol(false);
      } else {
        setShowProtocol(true);
      }
    }
  }, [active, quickLoginPage]);

  const { loading, run } = useRequest<AuthPrivacyRes, any>(
    (id) =>
      request<AuthPrivacyRes>(`${authApi}/${authnV3Privacy}`, {
        id,
      }),
    {
      manual: true,
      onSuccess(data) {
        setPrivacyContent([...privacyContent, data.data]);
      },
    },
  );

  const _handleCheckPrivacy = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    handlePrivacyChecked?.(checked);
  };

  if (!data) {
    return null;
  }

  return (
    <div
      className={cls(`${prefixCls}-footer-wrapper`, {
        [`${prefixCls}-footer-wrapper-xs`]: size === ViewportSize.xs,
      })}
    >
      {showProtocol && (
        <div className={`${prefixCls}-footer-protocol`} style={scaleStyle}>
          {check_agreement_privacy && (
            <Checkbox
              className={`${prefixCls}-footer-protocol-checkbox`}
              checked={privacyChecked}
              onChange={_handleCheckPrivacy}
            />
          )}
          <span className={`${prefixCls}-policy-text`}>
            {login_page_protocol?.agreement_prefix}
          </span>
          {login_page_protocol?.agreement_list?.map((protocol, index) => (
            <span
              key={index}
              className={`${prefixCls}-policy-title`}
              onClick={() => handleShowPolicy(protocol.id)}
            >
              {protocol?.protocol_name}
            </span>
          ))}
        </div>
      )}
      {enable_user_register && (
        <div className={`${prefixCls}-footer-register`} style={scaleStyle}>
          {__('go:to:register:prefix')}
          <span
            className={`${prefixCls}-footer-register-link`}
            onClick={() => goToRegister(env, clientId, lang)}
          >
            {__('go:to:register')}
          </span>
        </div>
      )}
      <PrivacyDetailModal
        privacy={privacy}
        handleCancel={handleCancel}
        visible={visible}
      />
    </div>
  );
};

export default Index;
