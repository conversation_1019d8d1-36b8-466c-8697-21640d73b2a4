import {
  AuthPrivacy,
  prefixCls,
  useSize,
  ViewportSize,
} from '@iam/login-shared';
import { Modal } from '@otakus/design';
import Drawer from '@ui/components/Drawer';
import './index.less';

interface PrivacyDetailModalProps {
  visible: boolean;
  handleCancel: () => void;
  privacy: AuthPrivacy;
}

const Index = (props: PrivacyDetailModalProps) => {
  const { size } = useSize();
  const { visible, handleCancel, privacy } = props;
  return (
    <>
      {size !== ViewportSize.xs && (
        <Modal
          open={visible}
          footer={null}
          onCancel={handleCancel}
          className={`${prefixCls}-footer-modal`}
          destroyOnClose
          width={'720px'}
          zIndex={9999}
        >
          <span className={`${prefixCls}-footer-privacy-title`}>
            {privacy?.name}
          </span>
          <div
            dangerouslySetInnerHTML={{
              __html: privacy?.content,
            }}
          ></div>
        </Modal>
      )}
      {size === ViewportSize.xs && (
        <Drawer
          open={visible}
          onClose={handleCancel}
          title={''}
          titleClassName={`${prefixCls}-footer-privacy-title-xs`}
          zIndex={9999}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: privacy?.content,
            }}
          ></div>
        </Drawer>
      )}
    </>
  );
};

export default Index;
