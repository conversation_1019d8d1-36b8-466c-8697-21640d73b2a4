import {
  Bc,
  BC_MESSAGE,
  getTLD,
  hwBind,
  IV3_SCENE_TYPE,
  LoginProps,
  moatClientId,
  moatSilentLoginApi,
  preAuthApiPrefix,
  request,
  setRequestGlobalHeader,
  useNextLogin,
} from '@iam/login-shared';
import Login from '@ui/components/Login';
import Hoc from './NextComponentHoc';
import { useCallback, useEffect } from 'react';
import Cookies from 'js-cookie';
import SecondVerificationInstance from '@ui/components/SecondVerification/instance';
import { Spin } from '@otakus/design';

const bcInstance = new Bc();

const Index = (props: LoginProps) => {
  const {
    cb: originalCb,
    sentryCaptureMessage,
    sentryCaptureException,
    ...rest
  } = props;
  const { isBrowserSupported, getHardwareKeyInfo } = hwBind;
  const { env, waveOtp, clientId } = rest;
  const preAuthApi = preAuthApiPrefix[env];
  useEffect(() => {
    isBrowserSupported()
      .then((isSupport) => {
        let _keyType: string;
        if (isSupport) {
          getHardwareKeyInfo().then((res) => {
            const { publicKey, keyType } = res;
            _keyType = keyType;
            setRequestGlobalHeader({
              'x-mi-hw-bind-public-key': publicKey,
              'x-mi-hw-bind-key-type': keyType,
            });
          });
        }
        sentryCaptureMessage?.('report browser support hwBind', {
          level: 'log',
          tags: {
            canUseHwBind: isSupport,
          },
          extra: {
            browser: navigator.userAgent,
            isSupport,
            env,
            keyType: _keyType,
          },
        });
      })
      .catch((err) => {
        const reportedError = err?.toJSON?.() || err;
        sentryCaptureException?.('report browser support hwBind error', {
          level: 'log',
          tags: {
            canUseHwBind: false,
          },
          extra: {
            browser: navigator.userAgent,
            isSupport: false,
            env,
            error: reportedError,
          },
        });
      });
  }, []);
  const cb = useCallback(
    (ticket: string) => {
      const key = env === 'prod' ? 'sso_shim' : `sso_shim_${env}`;
      Cookies.remove(key, { expires: -1, domain: `.${getTLD()}` });
      if (ticket) {
        request(`${preAuthApi}/${moatSilentLoginApi}`, {
          ticket,
          clientId: moatClientId[env],
        }).finally(() => {
          originalCb();
        });
        return;
      }
      originalCb();
    },
    [originalCb],
  ) as unknown as (ticket?: string) => void;
  // 收到广播触发回调
  useEffect(() => {
    bcInstance.onMessage((data) => {
      if (data === BC_MESSAGE) {
        cb();
      }
    });
  }, [cb]);

  const _cb = (ticket = '') => {
    bcInstance.send();
    cb(ticket);
  };
  const { isLogin, workCode, mfa, ticket } = useNextLogin(
    env,
    waveOtp,
    clientId,
  );

  // 单点成功直接触发回调
  useEffect(() => {
    if (isLogin) {
      _cb(ticket);
    }
  }, [isLogin, _cb, ticket]);

  if (isLogin === undefined) {
    return null;
  }

  if (isLogin === true) {
    return (
      <Spin
        size={'large'}
        style={{
          width: '100vw',
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
    );
  }

  if (mfa) {
    return (
      <Hoc {...rest} scene={IV3_SCENE_TYPE.InitialLogin}>
        <SecondVerificationInstance cb={_cb} />
      </Hoc>
    );
  }

  return (
    <Hoc {...rest} scene={IV3_SCENE_TYPE.InitialLogin}>
      <Login cb={_cb} />
    </Hoc>
  );
};

export default Index;
