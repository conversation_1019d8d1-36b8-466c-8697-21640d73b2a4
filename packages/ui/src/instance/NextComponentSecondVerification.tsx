import Mfa from '@ui/components/SecondVerification/instance';

import {
  Bc,
  BC_MESSAGE,
  getTLD,
  IV3_SCENE_TYPE,
  LoginProps,
  moatClientId,
  moatSilentLoginApi,
  preAuthApiPrefix,
  request,
} from '@iam/login-shared';
import Hoc from './NextComponentHoc';
import { useCallback, useEffect } from 'react';
import Cookies from 'js-cookie';

const bcInstance = new Bc();

const Index = (props: LoginProps) => {
  const { cb: originalCb, ...rest } = props;
  const { env } = rest;
  const preAuthApi = preAuthApiPrefix[env];
  const cb = useCallback(
    (ticket: string) => {
      const key = env === 'prod' ? 'sso_shim' : `sso_shim_${env}`;
      Cookies.remove(key, { expires: -1, domain: `.${getTLD()}` });
      if (ticket) {
        request(`${preAuthApi}/${moatSilentLoginApi}`, {
          ticket,
          clientId: moatClientId[env],
        }).finally(() => {
          originalCb();
        });
        return;
      }
      originalCb();
    },
    [originalCb],
  ) as unknown as () => void;
  // 收到广播触发回调
  useEffect(() => {
    bcInstance.onMessage((data) => {
      if (data === BC_MESSAGE) {
        cb();
      }
    });
  }, [cb]);

  const _cb = () => {
    bcInstance.send();
    cb();
  };

  return (
    <Hoc {...rest} scene={IV3_SCENE_TYPE.InitialLogin}>
      <Mfa cb={_cb} />
    </Hoc>
  );
};

export default Index;
