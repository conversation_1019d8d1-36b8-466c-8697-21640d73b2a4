import React, { cloneElement, FC } from 'react';
import {
  AssetsDeviceType,
  AssetsProvider,
  ConfigProvider,
  ConfigProviderProps,
  dispatch,
  ISentryCapture,
  LocaleProvider,
  useErrorFromSdk,
  useFingerPrint,
} from '@iam/login-shared';

import {
  ConfigProvider as AntdConfigProvider,
  StyleProvider,
  ThemeProvider,
} from '@otakus/design';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import Message from '@ui/components/Message';
import { legacyLogicalPropertiesTransformer } from '@ant-design/cssinjs';

const Index: FC<ConfigProviderProps & ISentryCapture> = (props) => {
  const { children, sentryCaptureException, sentryCaptureMessage, ...rest } =
    props;
  const { env, lang, mask = false, messageCenter = false } = rest;
  // 错误上报
  useErrorFromSdk();
  // 设备指纹
  useFingerPrint(env);

  dispatch(
    {
      sentryCaptureMessage,
      sentryCaptureException,
    },
    false,
    'sentry',
  );

  return (
    <div id={'iam-root'}>
      <StyleProvider
        hashPriority={'high'}
        transformers={[legacyLogicalPropertiesTransformer]}
      >
        <ThemeProvider>
          <AntdConfigProvider
            locale={lang === 'zh-CN' ? zhCN : enUS}
            getPopupContainer={() => document.querySelector('#iam-root')}
          >
            <ConfigProvider {...rest}>
              <Message center={mask || messageCenter} />
              <LocaleProvider>
                <AssetsProvider deviceType={AssetsDeviceType.ui}>
                  {cloneElement(children as React.ReactElement<any>, {
                    ...rest,
                    sentryCaptureException,
                    sentryCaptureMessage,
                  })}
                </AssetsProvider>
              </LocaleProvider>
            </ConfigProvider>
          </AntdConfigProvider>
        </ThemeProvider>
      </StyleProvider>
    </div>
  );
};

export default Index;
