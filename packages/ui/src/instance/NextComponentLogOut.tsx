import LogOut from '@ui/components/LogOut';
import Hoc from '@ui/instance/NextComponentHoc';
import { ISentryCapture, LogOutProps } from '@iam/login-shared';

const Index = (props: LogOutProps & ISentryCapture) => {
  const { postLogoutRedirectUri, ...rest } = props;
  return (
    <Hoc {...rest}>
      <LogOut postLogoutRedirectUri={postLogoutRedirectUri} />
    </Hoc>
  );
};

export default Index;
