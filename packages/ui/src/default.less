#iam-root *,
:after,
:before {
  box-sizing: border-box;
}

#iam-root {
  width: 100%;
  height: 100%;
  // overflow: hidden;
}

input {
  caret-color: var(--otakus-color-primary);
  -webkit-appearance: none !important;
  -webkit-tap-highlight-color: transparent;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.otakus-input-outlined.otakus-input-status-error:not(.otakus-input-disabled) {
  input {
    caret-color: var(--otakus-color-error);
  }
}
