<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_3877_25144)">
        <g filter="url(#filter0_ii_3877_25144)">
            <path d="M16.0001 12.6955C16.0001 9.38169 18.6864 6.69531 22.0001 6.69531H54.8947C56.4813 6.69531 58.0033 7.32373 59.1277 8.44307L82.5643 31.7737C83.6953 32.8997 84.3312 34.4298 84.3313 36.0257L84.3331 87.3567C84.3332 90.6705 81.6469 93.3569 78.3331 93.3569H22.002C18.6883 93.3569 16.0021 90.6707 16.002 87.3571L16.0001 12.6955Z"
                  fill="url(#paint0_linear_3877_25144)"/>
            <path d="M16.0001 12.6955C16.0001 9.38169 18.6864 6.69531 22.0001 6.69531H54.8947C56.4813 6.69531 58.0033 7.32373 59.1277 8.44307L82.5643 31.7737C83.6953 32.8997 84.3312 34.4298 84.3313 36.0257L84.3331 87.3567C84.3332 90.6705 81.6469 93.3569 78.3331 93.3569H22.002C18.6883 93.3569 16.0021 90.6707 16.002 87.3571L16.0001 12.6955Z"
                  fill="url(#paint1_linear_3877_25144)" fill-opacity="0.6"/>
            <path d="M16.0001 12.6955C16.0001 9.38169 18.6864 6.69531 22.0001 6.69531H54.8947C56.4813 6.69531 58.0033 7.32373 59.1277 8.44307L82.5643 31.7737C83.6953 32.8997 84.3312 34.4298 84.3313 36.0257L84.3331 87.3567C84.3332 90.6705 81.6469 93.3569 78.3331 93.3569H22.002C18.6883 93.3569 16.0021 90.6707 16.002 87.3571L16.0001 12.6955Z"
                  fill="url(#paint2_linear_3877_25144)"/>
            <path d="M16.0001 12.6955C16.0001 9.38169 18.6864 6.69531 22.0001 6.69531H54.8947C56.4813 6.69531 58.0033 7.32373 59.1277 8.44307L82.5643 31.7737C83.6953 32.8997 84.3312 34.4298 84.3313 36.0257L84.3331 87.3567C84.3332 90.6705 81.6469 93.3569 78.3331 93.3569H22.002C18.6883 93.3569 16.0021 90.6707 16.002 87.3571L16.0001 12.6955Z"
                  fill="url(#paint3_radial_3877_25144)" fill-opacity="0.1"/>
        </g>
        <path d="M16.3001 12.6954C16.3001 9.54737 18.8521 6.99531 22.0001 6.99531H54.8947C56.4019 6.99531 57.8478 7.59231 58.916 8.65568L82.3526 31.9863C83.4271 33.056 84.0312 34.5096 84.0313 36.0258L84.0331 87.3567C84.0332 90.5048 81.4812 93.0569 78.3331 93.0569H22.002C18.854 93.0569 16.3021 90.5051 16.302 87.3571L16.3001 12.6954Z"
              stroke="#162163" stroke-opacity="0.16" stroke-width="0.6"/>
        <g opacity="0.8">
            <g filter="url(#filter1_i_3877_25144)">
                <rect x="28.7578" y="32.1953" width="42.8225" height="4.82326" rx="2.41163" fill="#C9D2FD"/>
            </g>
            <rect x="28.6078" y="32.0453" width="43.1225" height="5.12326" rx="2.56163" stroke="#A3B7FE"
                  stroke-width="0.3"/>
        </g>
        <path d="M57.6289 6.69531L84.332 33.2629H63.7479C60.5575 33.2629 57.9589 30.6998 57.9151 27.5098L57.6289 6.69531Z"
              fill="url(#paint4_linear_3877_25144)"/>
        <g opacity="0.6" filter="url(#filter2_dii_3877_25144)">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M57.6296 8.93839C57.6296 6.88094 56.3047 6.6875 56.3047 6.6875H57.6296V6.69082L84.3302 33.256H63.7461C60.5557 33.256 57.9572 30.6929 57.9133 27.5028L57.6296 6.86597L57.6296 8.93839ZM84.3296 34.5776C84.3296 34.5776 84.1353 33.2587 82.0685 33.2587H84.3296V34.5776Z"
                  fill="url(#paint5_linear_3877_25144)"/>
        </g>
        <g opacity="0.8">
            <g filter="url(#filter3_i_3877_25144)">
                <rect x="28.7539" y="47.6172" width="42.8225" height="4.82326" rx="2.41163" fill="#C9D2FD"/>
            </g>
            <rect x="28.6039" y="47.4672" width="43.1225" height="5.12326" rx="2.56163" stroke="#A3B7FE"
                  stroke-width="0.3"/>
        </g>
        <path d="M33.3125 50.0005C33.3125 51.1781 32.3578 52.1328 31.1802 52.1328C30.0025 52.1328 29.0479 51.1781 29.0479 50.0005C29.0479 48.8228 30.0025 47.8682 31.1802 47.8682C32.3578 47.8682 33.3125 48.8228 33.3125 50.0005Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.662973"/>
        <g opacity="0.8">
            <g filter="url(#filter4_i_3877_25144)">
                <rect x="28.7578" y="63.0312" width="28.3333" height="4.82326" rx="2.41163" fill="#C9D2FD"/>
            </g>
            <rect x="28.6078" y="62.8812" width="28.6333" height="5.12326" rx="2.56163" stroke="#A3B7FE"
                  stroke-width="0.3"/>
        </g>
        <path d="M56.7188 65.4438C56.7188 66.6215 55.7641 67.5762 54.5864 67.5762C53.4088 67.5762 52.4541 66.6215 52.4541 65.4438C52.4541 64.2662 53.4088 63.3115 54.5864 63.3115C55.7641 63.3115 56.7188 64.2662 56.7188 65.4438Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.662973"/>
        <g filter="url(#filter5_d_3877_25144)">
            <g filter="url(#filter6_d_3877_25144)">
                <g filter="url(#filter7_iiiii_3877_25144)">
                    <circle cx="81.6482" cy="82.0363" r="13.75" transform="rotate(4.99826 81.6482 82.0363)"
                            fill="url(#paint6_linear_3877_25144)"/>
                </g>
                <circle cx="81.6482" cy="82.0363" r="12.7438" transform="rotate(4.99826 81.6482 82.0363)"
                        stroke="url(#paint7_linear_3877_25144)" stroke-width="2.01243"/>
                <g filter="url(#filter8_dii_3877_25144)">
                    <circle cx="81.6482" cy="82.0363" r="13.75" transform="rotate(4.99826 81.6482 82.0363)"
                            fill="url(#paint8_linear_3877_25144)"/>
                    <circle cx="81.6482" cy="82.0363" r="13.75" transform="rotate(4.99826 81.6482 82.0363)"
                            stroke="url(#paint9_linear_3877_25144)" stroke-width="0.4"/>
                </g>
            </g>
            <g filter="url(#filter9_di_3877_25144)">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M89.1703 78.8346C88.6555 78.177 87.7278 78.0794 87.0981 78.6165L80.2739 84.4376L77.6905 80.7071C77.2139 80.0189 76.2933 79.8641 75.6343 80.3613C74.9752 80.8585 74.8273 81.8194 75.3039 82.5076L78.7395 87.4687C79.0266 87.8832 79.4747 88.1042 79.9298 88.1058C80.3096 88.15 80.7054 88.0409 81.0246 87.7686L88.9623 80.9976C89.592 80.4605 89.6851 79.4921 89.1703 78.8346Z"
                      fill="url(#paint10_linear_3877_25144)" shape-rendering="crispEdges"/>
                <path d="M80.2229 84.473L80.262 84.5294L80.3142 84.4849L87.1384 78.6637C87.7408 78.1499 88.6281 78.2427 89.1214 78.8728C89.616 79.5046 89.526 80.4352 88.9221 80.9504L80.9843 87.7214C80.6785 87.9822 80.3 88.0864 79.937 88.0441L79.937 88.0437L79.93 88.0437C79.4951 88.0422 79.066 87.8311 78.7905 87.4334L75.3549 82.4723C74.8969 81.8108 75.0398 80.8875 75.6717 80.4108C76.3018 79.9354 77.1825 80.0825 77.6395 80.7424L80.2229 84.473Z"
                      stroke="url(#paint11_linear_3877_25144)" stroke-width="0.12416" shape-rendering="crispEdges"/>
            </g>
        </g>
        <path d="M19.5209 19.0081C17.5509 20.5803 14.7047 20.7232 12.5673 19.1804C10.4298 17.6377 9.67708 14.8812 10.5521 12.5219L7.36641 10.2138C5.28778 14.3449 6.45149 19.5021 10.3142 22.297C14.177 25.0918 19.4421 24.5818 22.7115 21.3158L19.5209 19.0081Z"
              fill="#677EF9"/>
        <path d="M96.8836 58.0289C95.9892 56.7837 94.3445 56.2281 92.8379 56.7836C91.3313 57.3391 90.4366 58.8279 90.5642 60.351L88.3176 61.1787C87.7826 58.4198 89.3119 55.5902 92.0323 54.5848C94.7581 53.5848 97.754 54.748 99.1355 57.2011L96.8889 58.0288L96.8836 58.0289Z"
              fill="#FFC54D"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M79.5548 2.34065L75.6652 8.48941L73.1288 6.88494L77.0184 0.736177L79.5548 2.34065Z" fill="#FF75A6"/>
    </g>
    <defs>
        <filter id="filter0_ii_3877_25144" x="16" y="4.69531" width="68.332" height="88.6621"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2"/>
            <feGaussianBlur stdDeviation="1"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-3"/>
            <feGaussianBlur stdDeviation="0.25"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3877_25144" result="effect2_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter1_i_3877_25144" x="28.157" y="31.8945" width="43.7219" height="5.72383"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1" dy="0.95227"/>
            <feGaussianBlur stdDeviation="0.15"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter2_dii_3877_25144" x="50.3523" y="4.30655" width="37.5512" height="37.4144"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.19048" dy="2.38095"/>
            <feGaussianBlur stdDeviation="2.38095"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.126965 0 0 0 0 0.120083 0 0 0 0 0.464193 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3877_25144"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3877_25144" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.58929"/>
            <feGaussianBlur stdDeviation="3.43573"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.458097" dy="-0.458097"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3877_25144" result="effect3_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter3_i_3877_25144" x="28.1531" y="47.3164" width="43.7219" height="5.72383"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1" dy="0.95227"/>
            <feGaussianBlur stdDeviation="0.15"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter4_i_3877_25144" x="28.157" y="62.7305" width="29.2336" height="5.72383"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1" dy="0.95227"/>
            <feGaussianBlur stdDeviation="0.15"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter5_d_3877_25144" x="66.6992" y="67.5026" width="29.8984" height="29.9004"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.416666"/>
            <feGaussianBlur stdDeviation="0.5"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.095 0 0 0 0 0.181133 0 0 0 0 0.633333 0 0 0 0.22 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3877_25144"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3877_25144" result="shape"/>
        </filter>
        <filter id="filter6_d_3877_25144" x="59.4399" y="61.8915" width="44.417" height="44.4189"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="2.06482"/>
            <feGaussianBlur stdDeviation="4.12964"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.138726 0 0 0 0 0.13322 0 0 0 0 0.408496 0 0 0 0.13 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3877_25144"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3877_25144" result="shape"/>
        </filter>
        <filter id="filter7_iiiii_3877_25144" x="63.8736" y="68.2852" width="33.5373" height="29.5144"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="2.01243"/>
            <feGaussianBlur stdDeviation="1.00621"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-4.8"/>
            <feGaussianBlur stdDeviation="2.01243"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3877_25144" result="effect2_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.01243"/>
            <feGaussianBlur stdDeviation="1.00621"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3877_25144" result="effect3_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="4.02485"/>
            <feGaussianBlur stdDeviation="1.00621"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3877_25144" result="effect4_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.00621"/>
            <feGaussianBlur stdDeviation="1.00621"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3877_25144" result="effect5_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter8_dii_3877_25144" x="66.8992" y="67.2859" width="30.6984" height="30.7004"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.4"/>
            <feGaussianBlur stdDeviation="0.4"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705801 0 0 0 0 0.705801 0 0 0 0 0.720866 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3877_25144"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3877_25144" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="2" dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.400239 0 0 0 0 0.8389 0 0 0 0 0.620931 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3877_25144"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.4" dy="-0.8"/>
            <feGaussianBlur stdDeviation="1"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0775 0 0 0 0 0.6 0 0 0 0 0.33875 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3877_25144" result="effect3_innerShadow_3877_25144"/>
        </filter>
        <filter id="filter9_di_3877_25144" x="74.0234" y="77.7695" width="16.4805" height="11.8457"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.5"/>
            <feGaussianBlur stdDeviation="0.5"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.276068 0 0 0 0 0.629606 0 0 0 0 0.384452 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3877_25144"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3877_25144" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-0.5"/>
            <feGaussianBlur stdDeviation="0.0247731"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.427451 0 0 0 0.06 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3877_25144"/>
        </filter>
        <linearGradient id="paint0_linear_3877_25144" x1="50.4692" y1="6.69531" x2="50.4692" y2="93.3569"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint1_linear_3877_25144" x1="50.4692" y1="6.69531" x2="50.4692" y2="93.3569"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint2_linear_3877_25144" x1="50.4692" y1="6.69531" x2="50.4692" y2="93.3569"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <radialGradient id="paint3_radial_3877_25144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(50.4692 50.0261) rotate(90) scale(43.3308 34.4692)">
            <stop stop-color="#7283D6" stop-opacity="0.37"/>
            <stop offset="1" stop-color="#FDFCFE" stop-opacity="0.68"/>
        </radialGradient>
        <linearGradient id="paint4_linear_3877_25144" x1="79.7072" y1="10.1024" x2="64.152" y2="38.3338"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E2E6FF"/>
            <stop offset="0.855332" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint5_linear_3877_25144" x1="76.6658" y1="12.4282" x2="61.0105" y2="31.2218"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#A6B4FF"/>
            <stop offset="1" stop-color="#6D84FF"/>
        </linearGradient>
        <linearGradient id="paint6_linear_3877_25144" x1="81.6482" y1="68.2863" x2="81.6482" y2="95.7863"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint7_linear_3877_25144" x1="78.289" y1="76.2019" x2="89.0405" y2="91.6512"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#20CA6C"/>
            <stop offset="1" stop-color="#0F9351"/>
        </linearGradient>
        <linearGradient id="paint8_linear_3877_25144" x1="77.5025" y1="67.2048" x2="94.0961" y2="89.2827"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#53C96E"/>
            <stop offset="1" stop-color="#35C564"/>
        </linearGradient>
        <linearGradient id="paint9_linear_3877_25144" x1="78.289" y1="76.2019" x2="89.0405" y2="91.6512"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#42CC6F"/>
            <stop offset="1" stop-color="#07A152"/>
        </linearGradient>
        <linearGradient id="paint10_linear_3877_25144" x1="82.2514" y1="78.2991" x2="82.3013" y2="88.1041"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint11_linear_3877_25144" x1="80.4962" y1="81.1304" x2="83.682" y2="87.806"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#20CA6C"/>
            <stop offset="1" stop-color="#0F9351"/>
        </linearGradient>
        <clipPath id="clip0_3877_25144">
            <rect width="100" height="100" fill="white" transform="translate(0.5)"/>
        </clipPath>
    </defs>
</svg>
