<svg viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_3400_30368)">
        <g opacity="0.25" filter="url(#filter0_f_3400_30368)">
            <ellipse cx="159.525" cy="256.725" rx="101.658" ry="13.2589" fill="#DFE2FF"/>
        </g>
        <g clip-path="url(#clip1_3400_30368)">
            <path d="M216.224 68.9155C217.887 65.9344 221.652 64.8658 224.633 66.5287L264.816 88.9438L289.021 102.446C292.002 104.109 293.071 107.873 291.408 110.854L282.371 127.054L256.372 173.668C254.709 176.649 250.944 177.718 247.963 176.055L183.574 140.138C180.594 138.475 179.525 134.71 181.188 131.729L216.224 68.9155Z"
                  fill="url(#paint0_linear_3400_30368)" stroke="url(#paint1_linear_3400_30368)"
                  stroke-width="0.650574"/>
            <g filter="url(#filter1_dii_3400_30368)">
                <path d="M219.25 77.664C220.708 75.0491 224.01 74.1118 226.625 75.5704L280.284 105.503C282.899 106.961 283.837 110.264 282.378 112.879L253.326 164.959C251.867 167.574 248.565 168.512 245.95 167.053L192.291 137.121C189.676 135.662 188.739 132.36 190.198 129.745L219.25 77.664Z"
                      fill="url(#paint2_linear_3400_30368)" shape-rendering="crispEdges"/>
                <path d="M219.25 77.664C220.708 75.0491 224.01 74.1118 226.625 75.5704L280.284 105.503C282.899 106.961 283.837 110.264 282.378 112.879L253.326 164.959C251.867 167.574 248.565 168.512 245.95 167.053L192.291 137.121C189.676 135.662 188.739 132.36 190.198 129.745L219.25 77.664Z"
                      fill="url(#paint3_linear_3400_30368)" fill-opacity="0.4" shape-rendering="crispEdges"/>
                <path d="M219.25 77.664C220.708 75.0491 224.01 74.1118 226.625 75.5704L280.284 105.503C282.899 106.961 283.837 110.264 282.378 112.879L253.326 164.959C251.867 167.574 248.565 168.512 245.95 167.053L192.291 137.121C189.676 135.662 188.739 132.36 190.198 129.745L219.25 77.664Z"
                      fill="url(#paint4_linear_3400_30368)" shape-rendering="crispEdges"/>
                <path d="M219.534 77.8224C220.905 75.3645 224.009 74.4834 226.467 75.8545L280.126 105.787C282.584 107.158 283.465 110.262 282.094 112.72L253.042 164.801C251.671 167.259 248.567 168.14 246.109 166.769L192.45 136.837C189.992 135.465 189.11 132.361 190.482 129.903L219.534 77.8224Z"
                      stroke="#C4D1FE" stroke-width="0.650574" shape-rendering="crispEdges"/>
            </g>
            <g filter="url(#filter2_iiiii_3400_30368)">
                <path d="M227.753 111.559L204.276 127.498C203.208 128.223 203.278 129.82 204.406 130.449L244.258 152.679C245.434 153.335 246.877 152.459 246.837 151.113L246.296 132.893C246.249 131.316 244.588 130.315 243.172 131.011L232.23 136.386C231.5 136.744 230.651 136.202 230.668 135.39L231.139 113.399C231.177 111.638 229.21 110.569 227.753 111.559Z"
                      fill="url(#paint5_linear_3400_30368)"/>
            </g>
            <path d="M231.229 109.199L204.858 127.103C203.523 128.01 203.61 130.005 205.02 130.791L243.591 152.307C245.061 153.127 246.865 152.032 246.815 150.349L246.246 131.209C246.222 130.421 245.392 129.92 244.684 130.268L232.23 136.386C231.5 136.744 230.651 136.202 230.668 135.39L231.229 109.199Z"
                  fill="url(#paint6_linear_3400_30368)"/>
            <path d="M232.733 110.988C232.222 111.904 231.067 112.232 230.151 111.721C229.236 111.211 228.908 110.055 229.419 109.14C229.929 108.224 231.085 107.896 232 108.407C232.915 108.917 233.243 110.073 232.733 110.988Z"
                  fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
            <path d="M247.096 152.086C246.586 153.001 245.43 153.329 244.515 152.819C243.6 152.308 243.271 151.152 243.782 150.237C244.293 149.322 245.448 148.994 246.364 149.504C247.279 150.015 247.607 151.171 247.096 152.086Z"
                  fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
            <g filter="url(#filter3_iiiii_3400_30368)">
                <circle cx="252.004" cy="113.541" r="5.42145" transform="rotate(29.1538 252.004 113.541)"
                        fill="url(#paint7_linear_3400_30368)"/>
            </g>
            <circle cx="252.004" cy="113.541" r="5.09616" transform="rotate(29.1538 252.004 113.541)" fill="#CCD4FF"
                    stroke="#3E5BEA" stroke-width="0.650574"/>
            <path opacity="0.4"
                  d="M255.41 170.491L244.085 143.541C242.532 139.845 238.914 137.441 234.905 137.441L234.296 167.965L248.648 176.404C252.573 178.712 257.174 174.689 255.41 170.491Z"
                  fill="url(#paint8_linear_3400_30368)" fill-opacity="0.8"/>
        </g>
        <circle opacity="0.2" cx="25.4596" cy="147.257" r="3.21076" transform="rotate(29.1538 25.4596 147.257)"
                fill="#CCD4FF" stroke="#3E5BEA" stroke-width="0.409884"/>
        <path d="M24.7503 150.764L19.6816 171.536" stroke="url(#paint9_linear_3400_30368)" stroke-width="0.650574"/>
        <path d="M92.4464 247.403L71.7109 171.78L85.4329 163.242L112.877 200.444L92.4464 247.403Z"
              fill="url(#paint10_linear_3400_30368)" fill-opacity="0.8"/>
        <g filter="url(#filter4_ii_3400_30368)">
            <path d="M31.3985 104.282C29.953 100.992 31.448 97.1539 34.7376 95.7085L67.3917 81.3608C68.9667 80.6688 70.7517 80.6288 72.3561 81.2495L105.798 94.1873C107.412 94.8116 108.71 96.0533 109.406 97.6375L131.797 148.593C133.243 151.882 131.748 155.721 128.458 157.166L72.5388 181.736C69.2494 183.181 65.4111 181.687 63.9657 178.397L31.3985 104.282Z"
                  fill="url(#paint11_linear_3400_30368)"/>
            <path d="M31.3985 104.282C29.953 100.992 31.448 97.1539 34.7376 95.7085L67.3917 81.3608C68.9667 80.6688 70.7517 80.6288 72.3561 81.2495L105.798 94.1873C107.412 94.8116 108.71 96.0533 109.406 97.6375L131.797 148.593C133.243 151.882 131.748 155.721 128.458 157.166L72.5388 181.736C69.2494 183.181 65.4111 181.687 63.9657 178.397L31.3985 104.282Z"
                  fill="url(#paint12_linear_3400_30368)" fill-opacity="0.6"/>
            <path d="M31.3985 104.282C29.953 100.992 31.448 97.1539 34.7376 95.7085L67.3917 81.3608C68.9667 80.6688 70.7517 80.6288 72.3561 81.2495L105.798 94.1873C107.412 94.8116 108.71 96.0533 109.406 97.6375L131.797 148.593C133.243 151.882 131.748 155.721 128.458 157.166L72.5388 181.736C69.2494 183.181 65.4111 181.687 63.9657 178.397L31.3985 104.282Z"
                  fill="url(#paint13_linear_3400_30368)"/>
            <path d="M31.3985 104.282C29.953 100.992 31.448 97.1539 34.7376 95.7085L67.3917 81.3608C68.9667 80.6688 70.7517 80.6288 72.3561 81.2495L105.798 94.1873C107.412 94.8116 108.71 96.0533 109.406 97.6375L131.797 148.593C133.243 151.882 131.748 155.721 128.458 157.166L72.5388 181.736C69.2494 183.181 65.4111 181.687 63.9657 178.397L31.3985 104.282Z"
                  fill="url(#paint14_radial_3400_30368)" fill-opacity="0.1"/>
        </g>
        <path d="M31.6963 104.151C30.3231 101.026 31.7433 97.3794 34.8684 96.0063L67.5226 81.6586C69.0188 81.0012 70.7145 80.9632 72.2387 81.5529L105.68 94.4906C107.213 95.0838 108.447 96.2633 109.109 97.7684L131.5 148.723C132.873 151.849 131.453 155.495 128.327 156.868L72.408 181.438C69.283 182.811 65.6366 181.391 64.2635 178.266L31.6963 104.151Z"
              stroke="#162163" stroke-opacity="0.16" stroke-width="0.650574"/>
        <path d="M56.074 130.291C55.7337 130.441 55.4913 130.351 55.3466 130.021L50.7172 119.485C50.5725 119.156 50.6703 118.917 51.0105 118.767L56.7396 116.25C56.9591 116.153 57.1412 116.27 57.2859 116.599L57.4016 116.863C57.5463 117.192 57.5088 117.405 57.2893 117.501L52.5316 119.592L54.1808 123.345L58.4447 121.472C58.6642 121.375 58.8463 121.492 58.9909 121.821L59.1067 122.084C59.2514 122.413 59.2139 122.626 58.9944 122.723L54.7306 124.596L56.4955 128.613L61.2697 126.515C61.4892 126.419 61.6713 126.535 61.816 126.865L61.9317 127.128C62.0764 127.457 62.039 127.67 61.8195 127.767L56.074 130.291ZM65.2401 126.303C64.9986 126.409 64.8086 126.453 64.6699 126.436C64.5422 126.413 64.4542 126.347 64.406 126.237L59.6319 115.372C59.5788 115.251 59.5896 115.142 59.6643 115.043C59.7451 114.929 59.9063 114.819 60.1477 114.713L60.7239 114.46C60.9763 114.349 61.1805 114.292 61.3363 114.289C61.4982 114.27 61.6229 114.301 61.7102 114.38L66.965 118.651C67.1045 118.76 67.2537 118.891 67.4125 119.044C67.5823 119.192 67.7277 119.344 67.8487 119.5C67.8216 119.289 67.8141 119.064 67.826 118.823C67.8442 118.566 67.8627 118.355 67.8817 118.189L68.2459 111.509C68.2574 111.386 68.3193 111.273 68.4317 111.172C68.5393 111.059 68.7139 110.949 68.9553 110.843L69.4821 110.612C69.7345 110.501 69.9246 110.457 70.0523 110.479C70.1752 110.491 70.2632 110.557 70.3162 110.677L75.0903 121.543C75.1385 121.653 75.1277 121.762 75.0579 121.871C74.988 121.981 74.8269 122.091 74.5745 122.202L74.4428 122.259C74.2013 122.366 74.0113 122.41 73.8726 122.392C73.7449 122.37 73.6569 122.304 73.6087 122.194L70.1294 114.275C70.0619 114.122 69.9943 113.968 69.9268 113.814C69.8593 113.661 69.7997 113.51 69.748 113.363L69.6749 113.375C69.6669 113.536 69.6565 113.691 69.6437 113.84C69.6419 113.985 69.6346 114.133 69.6218 114.282L69.277 120.247C69.2629 120.424 69.2106 120.558 69.1201 120.65C69.0406 120.738 68.8911 120.829 68.6716 120.926L68.3424 121.071C68.1338 121.162 67.965 121.21 67.836 121.215C67.7179 121.214 67.5837 121.162 67.4332 121.058L62.7134 117.2C62.5945 117.108 62.4756 117.016 62.3567 116.924C62.2488 116.828 62.133 116.728 62.0092 116.625L61.9506 116.671C62.0243 116.809 62.0949 116.954 62.1624 117.108C62.2299 117.262 62.2974 117.415 62.3649 117.569L65.8876 125.586C65.9358 125.696 65.925 125.806 65.8552 125.915C65.7853 126.024 65.6242 126.134 65.3718 126.245L65.2401 126.303ZM79.1145 120.207C78.873 120.313 78.683 120.357 78.5443 120.339C78.4166 120.317 78.3286 120.251 78.2804 120.141L73.5786 109.44C73.4339 109.111 73.5317 108.872 73.872 108.722L77.1151 107.297C78.3553 106.752 79.4112 106.622 80.2827 106.907C81.1494 107.181 81.8239 107.867 82.3061 108.964C82.7883 110.062 82.8396 111.028 82.46 111.862C82.0755 112.686 81.2632 113.37 80.023 113.915L77.7511 114.914L79.762 119.49C79.8102 119.6 79.7994 119.709 79.7296 119.819C79.6597 119.928 79.4986 120.038 79.2462 120.149L79.1145 120.207ZM77.2014 113.662L79.4732 112.664C80.2634 112.317 80.7642 111.907 80.9756 111.434C81.1821 110.951 81.1263 110.347 80.808 109.623C80.4897 108.898 80.0851 108.454 79.594 108.29C79.0981 108.115 78.4551 108.201 77.6649 108.548L75.393 109.547L77.2014 113.662ZM90.66 115.134C90.4405 115.23 90.2669 115.267 90.1392 115.245C90.0115 115.222 89.9235 115.156 89.8753 115.047L85.5063 105.103L82.3454 106.492C82.1369 106.583 81.9603 106.465 81.8156 106.135L81.6999 105.872C81.5552 105.543 81.5872 105.332 81.7957 105.241L89.5332 101.841C89.7527 101.745 89.9348 101.861 90.0795 102.19L90.1952 102.454C90.3399 102.783 90.3025 102.996 90.083 103.092L86.9879 104.452L91.357 114.396C91.4052 114.505 91.3944 114.615 91.3245 114.724C91.2547 114.833 91.11 114.936 90.8905 115.033L90.66 115.134ZM99.7941 111.121C99.5526 111.227 99.3625 111.271 99.2238 111.253C99.0961 111.231 99.0082 111.165 98.9599 111.055L96.5801 105.639L90.8556 101.908C90.7876 101.872 90.733 101.838 90.6917 101.803C90.6457 101.758 90.613 101.714 90.5937 101.67C90.5551 101.582 90.5811 101.492 90.6715 101.4C90.7572 101.297 90.9372 101.185 91.2116 101.064L91.3927 100.985C91.5683 100.908 91.7182 100.861 91.8424 100.846C91.9776 100.826 92.0998 100.851 92.2091 100.921L97.2833 104.289L96.6742 104.556L97.651 98.5098C97.6673 98.3979 97.7341 98.2966 97.8513 98.2058C97.9795 98.1102 98.1259 98.0262 98.2906 97.9538L98.4223 97.896C98.6637 97.7899 98.8459 97.7426 98.9688 97.7541C99.0869 97.7546 99.1652 97.7987 99.2038 97.8865C99.223 97.9304 99.2338 97.9846 99.2359 98.0491C99.2333 98.1027 99.222 98.1665 99.2023 98.2407L98.0835 105.037L100.442 110.404C100.49 110.514 100.479 110.623 100.409 110.732C100.339 110.842 100.178 110.952 99.9258 111.063L99.7941 111.121Z"
              fill="#889BFD"/>
        <path d="M70.1055 80.1678L108.202 94.8941L87.7678 103.872C84.6007 105.264 80.9032 103.853 79.4682 100.705L70.1055 80.1678Z"
              fill="url(#paint15_linear_3400_30368)"/>
        <g opacity="0.6" filter="url(#filter5_dii_3400_30368)">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M71.086 82.3931C70.1886 80.3507 68.7891 80.7366 68.7891 80.7366L70.1043 80.1587L70.1053 80.1611L108.198 94.8861L87.7643 103.864C84.5973 105.256 80.8998 103.845 79.4648 100.697L70.1765 80.3231L71.086 82.3931ZM108.774 96.199C108.774 96.199 108.006 94.9745 105.954 95.876L108.199 94.8897L108.774 96.199Z"
                  fill="url(#paint16_linear_3400_30368)"/>
        </g>
        <g opacity="0.8">
            <g filter="url(#filter6_i_3400_30368)">
                <rect x="58.5547" y="141.376" width="46.4321" height="5.22981" rx="2.61491"
                      transform="rotate(-23.7199 58.5547 141.376)" fill="#C9D2FD"/>
            </g>
            <rect x="58.3404" y="141.292" width="46.7573" height="5.5551" rx="2.77755"
                  transform="rotate(-23.7199 58.3404 141.292)" stroke="#A3B7FE" stroke-width="0.325287"/>
        </g>
        <path d="M64.1207 141.753C64.6344 142.922 64.1031 144.286 62.934 144.8C61.765 145.314 60.4009 144.782 59.8872 143.613C59.3736 142.444 59.9049 141.08 61.0739 140.567C62.243 140.053 63.6071 140.584 64.1207 141.753Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
        <g opacity="0.8">
            <g filter="url(#filter7_i_3400_30368)">
                <rect x="65.2773" y="156.677" width="30.7216" height="5.22981" rx="2.61491"
                      transform="rotate(-23.7199 65.2773 156.677)" fill="#C9D2FD"/>
            </g>
            <rect x="65.063" y="156.593" width="31.0468" height="5.5551" rx="2.77755"
                  transform="rotate(-23.7199 65.063 156.593)" stroke="#A3B7FE" stroke-width="0.325287"/>
        </g>
        <path d="M94.0895 146.874C94.6031 148.043 94.0718 149.407 92.9028 149.921C91.7337 150.435 90.3696 149.903 89.856 148.734C89.3423 147.565 89.8736 146.201 91.0427 145.687C92.2117 145.174 93.5758 145.705 94.0895 146.874Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
        <path opacity="0.2"
              d="M79.3827 138.187L89.8101 136.9L89.8104 173.424L79.8313 177.717L75.669 142.928C75.3898 140.595 77.0503 138.475 79.3827 138.187Z"
              fill="url(#paint17_linear_3400_30368)" fill-opacity="0.8"/>
        <g filter="url(#filter8_ii_3400_30368)">
            <path d="M137.062 50.1565C137.579 46.6177 140.856 44.1599 144.398 44.6541L178.164 49.365C179.868 49.6027 181.408 50.5056 182.448 51.8761L203.2 79.2291C204.259 80.6245 204.71 82.3878 204.452 84.1202L196.101 140.165C195.575 143.696 192.303 146.143 188.767 145.65L130.902 137.576C127.327 137.078 124.841 133.764 125.363 130.192L137.062 50.1565Z"
                  fill="url(#paint18_linear_3400_30368)"/>
            <path d="M137.062 50.1565C137.579 46.6177 140.856 44.1599 144.398 44.6541L178.164 49.365C179.868 49.6027 181.408 50.5056 182.448 51.8761L203.2 79.2291C204.259 80.6245 204.71 82.3878 204.452 84.1202L196.101 140.165C195.575 143.696 192.303 146.143 188.767 145.65L130.902 137.576C127.327 137.078 124.841 133.764 125.363 130.192L137.062 50.1565Z"
                  fill="url(#paint19_linear_3400_30368)" fill-opacity="0.6"/>
            <path d="M137.062 50.1565C137.579 46.6177 140.856 44.1599 144.398 44.6541L178.164 49.365C179.868 49.6027 181.408 50.5056 182.448 51.8761L203.2 79.2291C204.259 80.6245 204.71 82.3878 204.452 84.1202L196.101 140.165C195.575 143.696 192.303 146.143 188.767 145.65L130.902 137.576C127.327 137.078 124.841 133.764 125.363 130.192L137.062 50.1565Z"
                  fill="url(#paint20_linear_3400_30368)"/>
            <path d="M137.062 50.1565C137.579 46.6177 140.856 44.1599 144.398 44.6541L178.164 49.365C179.868 49.6027 181.408 50.5056 182.448 51.8761L203.2 79.2291C204.259 80.6245 204.71 82.3878 204.452 84.1202L196.101 140.165C195.575 143.696 192.303 146.143 188.767 145.65L130.902 137.576C127.327 137.078 124.841 133.764 125.363 130.192L137.062 50.1565Z"
                  fill="url(#paint21_radial_3400_30368)" fill-opacity="0.1"/>
        </g>
        <path d="M137.384 50.2036C137.875 46.8417 140.988 44.5068 144.353 44.9763L178.119 49.6872C179.737 49.913 181.201 50.7708 182.189 52.0728L202.941 79.4258C203.947 80.7514 204.375 82.4265 204.13 84.0722L195.779 140.117C195.279 143.471 192.171 145.796 188.812 145.327L130.947 137.254C127.55 136.78 125.189 133.632 125.685 130.239L137.384 50.2036Z"
              stroke="#162163" stroke-opacity="0.16" stroke-width="0.650574"/>
        <g opacity="0.6" filter="url(#filter9_dii_3400_30368)">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M180.719 50.9003L197.419 72.8988L182.361 70.7979C179.894 70.4537 178.161 68.1913 178.471 65.7196L180.29 51.2258C179.978 50.5179 179.407 50.3514 179.407 50.3514L180.383 50.4876L180.386 50.4618L180.409 50.4912L180.769 50.5415L180.719 50.9003ZM204.23 83.0464C204.23 83.0464 204.219 81.662 202.094 81.3655L204.419 81.6899L204.23 83.0464Z"
                  fill="url(#paint22_linear_3400_30368)"/>
        </g>
        <g filter="url(#filter10_i_3400_30368)">
            <circle cx="140.069" cy="81.3359" r="19.3001" transform="rotate(7.94251 140.069 81.3359)"
                    fill="url(#paint23_linear_3400_30368)"/>
        </g>
        <g filter="url(#filter11_d_3400_30368)">
            <rect x="148.836" y="102.066" width="7.05815" height="8.98311" transform="rotate(-31.1541 148.836 102.066)"
                  fill="url(#paint24_linear_3400_30368)"/>
            <path d="M162.496 124.405L168.536 120.754L172.811 127.824C173.017 128.165 172.907 128.608 172.567 128.814L167.761 131.72C167.42 131.926 166.976 131.816 166.77 131.476L162.496 124.405Z"
                  fill="url(#paint25_linear_3400_30368)"/>
            <path d="M164.137 128.354L171.275 124.039L173.059 126.99C173.669 127.999 173.517 129.301 172.619 130.065C172.088 130.517 171.485 130.989 170.939 131.319C170.367 131.665 169.593 132.015 168.88 132.306C167.77 132.76 166.519 132.295 165.898 131.268L164.137 128.354Z"
                  fill="url(#paint26_linear_3400_30368)"/>
            <g filter="url(#filter12_i_3400_30368)">
                <path d="M151.021 106.809C150.69 106.298 150.845 105.614 151.365 105.295L156.653 102.054C157.155 101.746 157.81 101.895 158.13 102.39L159.423 104.391C160.13 105.486 159.907 106.941 158.86 107.717C158.313 108.122 157.724 108.537 157.22 108.841C156.653 109.184 155.913 109.517 155.235 109.792C154.16 110.227 152.952 109.791 152.321 108.818L151.021 106.809Z"
                      fill="url(#paint27_linear_3400_30368)"/>
            </g>
            <path d="M152.423 108.979L159.48 104.499L171.713 124.712C171.725 124.733 171.736 124.756 171.744 124.779C172.073 125.873 169.955 127.126 168.904 127.762C167.39 128.677 165.704 129.336 164.468 128.792C164.406 128.764 164.355 128.716 164.32 128.658L152.423 108.979Z"
                  fill="url(#paint28_linear_3400_30368)"/>
        </g>
        <g filter="url(#filter13_iiiii_3400_30368)">
            <mask id="mask0_3400_30368" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="115" y="60" width="42"
                  height="42">
                <ellipse cx="136.117" cy="81.2191" rx="20.4052" ry="20.654" transform="rotate(7.94251 136.117 81.2191)"
                         fill="url(#paint29_linear_3400_30368)"/>
            </mask>
            <g mask="url(#mask0_3400_30368)">
                <g filter="url(#filter14_d_3400_30368)">
                    <rect x="130.809" y="48.8561" width="48.3766" height="65.5268"
                          transform="rotate(7.94251 130.809 48.8561)" fill="url(#paint30_linear_3400_30368)"/>
                </g>
            </g>
            <g filter="url(#filter15_di_3400_30368)">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M136.895 104.075C149.462 105.828 161.072 97.0612 162.825 84.4935C164.579 71.9257 155.812 60.3161 143.244 58.5627C130.676 56.8092 119.067 65.576 117.313 78.1438C115.56 90.7115 124.327 102.321 136.895 104.075ZM137.407 100.426C147.964 101.899 157.716 94.5346 159.189 83.9777C160.662 73.4208 153.298 63.6687 142.741 62.1958C132.184 60.7229 122.432 68.087 120.959 78.6439C119.486 89.2008 126.85 98.9529 137.407 100.426Z"
                      fill="url(#paint31_linear_3400_30368)"/>
            </g>
            <g filter="url(#filter16_i_3400_30368)">
                <circle cx="140.069" cy="81.3275" r="19.3001" transform="rotate(7.94251 140.069 81.3275)"
                        fill="url(#paint32_linear_3400_30368)"/>
            </g>
            <g filter="url(#filter17_i_3400_30368)">
                <mask id="mask1_3400_30368" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="120" y="62" width="40"
                      height="40">
                    <circle cx="140.007" cy="81.789" r="19.3001" transform="rotate(7.94251 140.007 81.789)"
                            fill="url(#paint33_linear_3400_30368)"/>
                </mask>
                <g mask="url(#mask1_3400_30368)">
                    <g opacity="0.5" filter="url(#filter18_iiiii_3400_30368)">
                        <path d="M129.564 62.4166C130.029 59.0815 133.11 56.7551 136.445 57.2204L173.234 62.353L197.265 94.028L189.501 149.692C189.018 153.152 185.822 155.566 182.363 155.083L123.931 146.931C120.596 146.465 118.269 143.385 118.735 140.05L129.564 62.4166Z"
                              fill="url(#paint34_linear_3400_30368)"/>
                    </g>
                </g>
            </g>
        </g>
        <mask id="path-42-inside-1_3400_30368" fill="white">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M117.305 78.2359C117.318 78.1296 117.332 78.0232 117.347 77.9167C119.083 65.4746 130.474 56.7813 142.791 58.4997C142.87 58.5108 142.949 58.5222 143.028 58.5341C143.101 58.5436 143.175 58.5535 143.248 58.5637C155.816 60.3171 164.582 71.9267 162.829 84.4945C161.076 97.0623 149.466 105.829 136.898 104.076C124.361 102.326 115.606 90.7686 117.305 78.2359ZM136.505 103.556C124.294 101.853 115.758 90.5546 117.305 78.2359C117.309 78.2055 117.313 78.1752 117.317 78.1448C119.06 65.6504 130.545 56.9127 143.028 58.5341C155.217 60.3657 163.673 71.7774 161.949 84.1395C160.213 96.5815 148.821 105.275 136.505 103.556Z"/>
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M117.305 78.2359C117.318 78.1296 117.332 78.0232 117.347 77.9167C119.083 65.4746 130.474 56.7813 142.791 58.4997C142.87 58.5108 142.949 58.5222 143.028 58.5341C143.101 58.5436 143.175 58.5535 143.248 58.5637C155.816 60.3171 164.582 71.9267 162.829 84.4945C161.076 97.0623 149.466 105.829 136.898 104.076C124.361 102.326 115.606 90.7686 117.305 78.2359ZM136.505 103.556C124.294 101.853 115.758 90.5546 117.305 78.2359C117.309 78.2055 117.313 78.1752 117.317 78.1448C119.06 65.6504 130.545 56.9127 143.028 58.5341C155.217 60.3657 163.673 71.7774 161.949 84.1395C160.213 96.5815 148.821 105.275 136.505 103.556Z"
              fill="url(#paint35_linear_3400_30368)"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M117.305 78.2359C117.318 78.1296 117.332 78.0232 117.347 77.9167C119.083 65.4746 130.474 56.7813 142.791 58.4997C142.87 58.5108 142.949 58.5222 143.028 58.5341C143.101 58.5436 143.175 58.5535 143.248 58.5637C155.816 60.3171 164.582 71.9267 162.829 84.4945C161.076 97.0623 149.466 105.829 136.898 104.076C124.361 102.326 115.606 90.7686 117.305 78.2359ZM136.505 103.556C124.294 101.853 115.758 90.5546 117.305 78.2359C117.309 78.2055 117.313 78.1752 117.317 78.1448C119.06 65.6504 130.545 56.9127 143.028 58.5341C155.217 60.3657 163.673 71.7774 161.949 84.1395C160.213 96.5815 148.821 105.275 136.505 103.556Z"
              fill="url(#paint36_linear_3400_30368)"/>
        <path d="M117.858 77.9881C117.844 78.0922 117.83 78.1963 117.817 78.3002L116.792 78.1716C116.806 78.0629 116.82 77.9542 116.835 77.8454L117.858 77.9881ZM142.719 59.0112C130.69 57.3329 119.556 65.8236 117.858 77.9881L116.835 77.8454C118.61 65.1257 130.258 56.2298 142.862 57.9882L142.719 59.0112ZM142.951 59.0448C142.874 59.0332 142.797 59.022 142.719 59.0112L142.862 57.9882C142.943 57.9995 143.024 58.0113 143.105 58.0234L142.951 59.0448ZM143.177 59.0752C143.105 59.0652 143.033 59.0555 142.961 59.0462L143.094 58.0219C143.169 58.0317 143.244 58.0418 143.319 58.0522L143.177 59.0752ZM162.318 84.4231C164.032 72.1379 155.462 60.7892 143.177 59.0752L143.319 58.0522C156.17 59.8451 165.133 71.7156 163.34 84.5659L162.318 84.4231ZM136.97 103.564C149.255 105.278 160.604 96.7084 162.318 84.4231L163.34 84.5659C161.548 97.4161 149.677 106.38 136.827 104.587L136.97 103.564ZM117.816 78.3053C116.156 90.5562 124.714 101.854 136.97 103.564L136.827 104.587C124.008 102.799 115.056 90.981 116.793 78.1666L117.816 78.3053ZM117.817 78.3002C116.305 90.3439 124.65 101.381 136.576 103.045L136.433 104.068C123.937 102.325 115.211 90.7653 116.792 78.1716L117.817 78.3002ZM117.829 78.2161C117.824 78.2458 117.82 78.2756 117.816 78.3053L116.793 78.1665C116.797 78.1355 116.801 78.1045 116.806 78.0734L117.829 78.2161ZM142.961 59.0462C130.759 57.4613 119.533 66.0026 117.829 78.2161L116.806 78.0734C118.588 65.2982 130.331 56.3641 143.094 58.0219L142.961 59.0462ZM161.437 84.0681C163.123 71.9819 154.856 60.8337 142.951 59.0448L143.105 58.0234C155.578 59.8978 164.223 71.5729 162.46 84.2108L161.437 84.0681ZM136.576 103.045C148.605 104.723 159.74 96.2326 161.437 84.0681L162.46 84.2108C160.685 96.9305 149.037 105.826 136.433 104.068L136.576 103.045Z"
              fill="url(#paint37_linear_3400_30368)" mask="url(#path-42-inside-1_3400_30368)"/>
        <g filter="url(#filter19_iiiii_3400_30368)">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M162.821 84.5262C162.834 84.4397 162.846 84.353 162.859 84.2661C164.594 71.824 156.017 60.3447 143.701 58.6264C143.614 58.6143 143.527 58.6027 143.44 58.5916C143.375 58.5819 143.31 58.5725 143.244 58.5633C130.676 56.8099 119.067 65.5767 117.313 78.1444C115.56 90.7122 124.327 102.322 136.894 104.075C149.451 105.827 161.052 97.0768 162.821 84.5262ZM137.415 103.683C149.645 105.39 160.963 96.8291 162.821 84.5262C162.822 84.5155 162.824 84.5048 162.825 84.4942C164.57 71.9919 155.903 60.4379 143.44 58.5916C131.224 57.0308 119.98 65.689 118.257 78.0434C116.521 90.4855 125.098 101.965 137.415 103.683Z"
                  fill="url(#paint38_linear_3400_30368)"/>
        </g>
        <g filter="url(#filter20_df_3400_30368)">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M155.136 93.2965C157.258 90.615 158.723 87.3038 159.247 83.5459C160.755 72.7362 153.318 62.7649 142.636 61.2745C131.953 59.7841 122.07 67.3389 120.562 78.1487C119.741 84.0315 121.57 89.666 125.129 93.8203C128.028 97.5601 132.31 100.209 137.337 100.91C144.429 101.899 151.157 98.7544 155.136 93.2965ZM155.136 93.2965C156.939 90.8244 158.178 87.8777 158.632 84.6258C160.087 74.1946 152.912 64.5728 142.607 63.135C132.301 61.6972 122.767 68.9878 121.312 79.419C120.566 84.7622 122.085 89.893 125.129 93.8203C128.11 97.2997 132.305 99.7408 137.174 100.42C144.256 101.408 150.965 98.5698 155.136 93.2965Z"
                  fill="url(#paint39_linear_3400_30368)"/>
        </g>
        <g opacity="0.5" filter="url(#filter21_f_3400_30368)">
            <path d="M160.775 84.2209C159.8 92.4475 154.192 98.1497 150.562 100.891C160.22 95.3024 162.825 85.8387 162.056 78.3678C161.539 73.345 159.558 69.6677 157.372 67.0429C158.93 69.1771 161.777 75.777 160.775 84.2209Z"
                  fill="url(#paint40_linear_3400_30368)"/>
        </g>
        <g opacity="0.5" filter="url(#filter22_df_3400_30368)">
            <path d="M137.669 103.512C125.34 101.42 119.04 90.7051 118.537 85.9952C119.545 89.3062 123.493 97.0492 134.604 100.532C145.273 103.877 155.918 95.6182 159.62 89.175C157.625 95.1604 150.175 104.328 137.669 103.512Z"
                  fill="url(#paint41_linear_3400_30368)"/>
        </g>
        <path d="M139.49 103.757C130.833 103.291 125.165 98.3555 123.413 95.9459C124.417 98.1739 128.213 103.111 138.257 104.281C147.196 105.064 155.626 99.5122 156.753 96.4216C155.049 98.6585 148.406 103.702 139.49 103.757Z"
              fill="url(#paint42_linear_3400_30368)"/>
        <path d="M129.074 65.541L125.075 94.2026" stroke="#3E5BEA" stroke-width="0.650574"/>
        <path d="M127.18 92.6294C127.035 93.6673 126.077 94.3913 125.039 94.2465C124.001 94.1017 123.277 93.1429 123.422 92.105C123.566 91.0671 124.525 90.3431 125.563 90.4879C126.601 90.6327 127.325 91.5915 127.18 92.6294Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
        <path d="M130.774 66.8643C130.629 67.9023 129.67 68.6263 128.632 68.4815C127.595 68.3366 126.87 67.3779 127.015 66.34C127.16 65.302 128.119 64.578 129.157 64.7228C130.195 64.8677 130.919 65.8264 130.774 66.8643Z"
              fill="white" stroke="#3E5BEA" stroke-width="0.718855"/>
        <g filter="url(#filter23_dddi_3400_30368)">
            <path d="M91.2902 247.935L86.5878 128.029C86.4844 125.393 88.5933 123.2 91.2312 123.2H132.893C134.693 123.2 136.298 124.333 136.902 126.028L139.987 134.697C140.59 136.393 142.196 137.525 143.996 137.525H233.038C235.674 137.525 237.782 139.715 237.682 142.349L233.665 247.93C233.57 250.426 231.519 252.4 229.021 252.4H95.9336C93.438 252.4 91.388 250.429 91.2902 247.935Z"
                  fill="url(#paint43_linear_3400_30368)" shape-rendering="crispEdges"/>
            <path d="M91.2902 247.935L86.5878 128.029C86.4844 125.393 88.5933 123.2 91.2312 123.2H132.893C134.693 123.2 136.298 124.333 136.902 126.028L139.987 134.697C140.59 136.393 142.196 137.525 143.996 137.525H233.038C235.674 137.525 237.782 139.715 237.682 142.349L233.665 247.93C233.57 250.426 231.519 252.4 229.021 252.4H95.9336C93.438 252.4 91.388 250.429 91.2902 247.935Z"
                  fill="url(#paint44_linear_3400_30368)" fill-opacity="0.6" shape-rendering="crispEdges"/>
            <path d="M91.2902 247.935L86.5878 128.029C86.4844 125.393 88.5933 123.2 91.2312 123.2H132.893C134.693 123.2 136.298 124.333 136.902 126.028L139.987 134.697C140.59 136.393 142.196 137.525 143.996 137.525H233.038C235.674 137.525 237.782 139.715 237.682 142.349L233.665 247.93C233.57 250.426 231.519 252.4 229.021 252.4H95.9336C93.438 252.4 91.388 250.429 91.2902 247.935Z"
                  fill="url(#paint45_linear_3400_30368)" shape-rendering="crispEdges"/>
            <path d="M91.2902 247.935L86.5878 128.029C86.4844 125.393 88.5933 123.2 91.2312 123.2H132.893C134.693 123.2 136.298 124.333 136.902 126.028L139.987 134.697C140.59 136.393 142.196 137.525 143.996 137.525H233.038C235.674 137.525 237.782 139.715 237.682 142.349L233.665 247.93C233.57 250.426 231.519 252.4 229.021 252.4H95.9336C93.438 252.4 91.388 250.429 91.2902 247.935Z"
                  fill="url(#paint46_radial_3400_30368)" fill-opacity="0.1" shape-rendering="crispEdges"/>
            <path d="M91.5224 247.926L86.82 128.02C86.7218 125.516 88.7252 123.432 91.2312 123.432H132.893C134.595 123.432 136.112 124.503 136.683 126.106L139.768 134.775C140.405 136.563 142.097 137.757 143.996 137.757H233.038C235.542 137.757 237.545 139.838 237.449 142.34L233.433 247.921C233.343 250.292 231.394 252.168 229.021 252.168H95.9336C93.5628 252.168 91.6153 250.295 91.5224 247.926Z"
                  stroke="#162163" stroke-opacity="0.16" stroke-width="0.464696" shape-rendering="crispEdges"/>
        </g>
        <g opacity="0.8">
            <g filter="url(#filter24_i_3400_30368)">
                <rect x="111.875" y="188.79" width="69.1528" height="8.53333" rx="4.26667" fill="#C9D2FD"/>
            </g>
            <rect x="111.632" y="188.547" width="69.6389" height="9.01941" rx="4.5097" stroke="#A3B7FE"
                  stroke-width="0.486076"/>
        </g>
        <g opacity="0.8">
            <g filter="url(#filter25_i_3400_30368)">
                <path d="M113.066 212.933C113.066 210.577 114.977 208.667 117.333 208.667H139.733C142.089 208.667 144 210.577 144 212.933C144 215.29 142.089 217.2 139.733 217.2H117.333C114.977 217.2 113.066 215.29 113.066 212.933Z"
                      fill="#C9D2FD"/>
            </g>
            <path d="M117.333 208.424C114.842 208.424 112.823 210.443 112.823 212.933C112.823 215.424 114.842 217.443 117.333 217.443H139.733C142.224 217.443 144.243 215.424 144.243 212.933C144.243 210.443 142.224 208.424 139.733 208.424H117.333Z"
                  stroke="#A3B7FE" stroke-width="0.486076"/>
        </g>
        <path d="M119.73 193.057C119.73 194.971 118.178 196.523 116.264 196.523C114.349 196.523 112.797 194.971 112.797 193.057C112.797 191.142 114.349 189.59 116.264 189.59C118.178 189.59 119.73 191.142 119.73 193.057Z"
              fill="white" stroke="#3E5BEA" stroke-width="1.07418"/>
        <path d="M142.996 212.933C142.996 214.848 141.444 216.4 139.529 216.4C137.615 216.4 136.063 214.848 136.063 212.933C136.063 211.019 137.615 209.467 139.529 209.467C141.444 209.467 142.996 211.019 142.996 212.933Z"
              fill="white" stroke="#3E5BEA" stroke-width="1.07418"/>
        <path d="M25.1317 178.293C25.1317 181.963 22.1566 184.938 18.4866 184.938C14.8166 184.938 11.8414 181.963 11.8414 178.293C11.8414 174.623 14.8166 171.648 18.4866 171.648C22.1566 171.648 25.1317 174.623 25.1317 178.293Z"
              fill="#CCD4FF" stroke="#3E5BEA" stroke-width="0.650574"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M124.336 156.554C122.766 155.57 120.901 155 118.901 155L118.879 155L116.888 152.773C116.72 152.585 116.421 152.571 116.31 152.751C115.764 153.637 115.407 154.623 115.251 155.666C111.462 157.106 108.773 160.724 108.773 164.959C108.773 170.458 113.308 174.917 118.901 174.917C124.494 174.917 129.028 170.458 129.028 164.959C129.028 162.708 128.269 160.631 126.988 158.964C127.577 157.967 127.878 156.872 127.866 155.763C127.864 155.598 127.658 155.491 127.47 155.551L124.336 156.554Z"
              fill="url(#paint47_linear_3400_30368)"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M121.47 162.488C121.29 162.137 120.86 161.998 120.511 162.178L118.968 162.972C118.424 163.252 118.21 163.923 118.49 164.47L119.284 166.02C119.464 166.372 119.894 166.511 120.244 166.331C120.593 166.151 120.731 165.72 120.551 165.368L119.901 164.1L121.163 163.451C121.512 163.271 121.65 162.84 121.47 162.488Z"
              fill="white"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M114.802 159.843C114.366 159.632 113.794 159.896 113.585 160.33C112.837 161.884 112.773 162.431 112.69 162.949C112.69 162.949 112.685 163.029 112.679 163.14C112.656 163.588 112.844 164.085 113.316 164.197C113.788 164.309 114.259 164.02 114.368 163.55C114.394 163.437 114.461 163.039 114.44 163.161C114.533 162.6 114.61 162.201 115.215 161.013C115.424 160.579 115.239 160.055 114.802 159.843Z"
              fill="white"/>
        <path d="M283.48 161.991L297.383 178.774" stroke="url(#paint48_linear_3400_30368)" stroke-width="0.650574"/>
        <circle opacity="0.4" cx="300.914" cy="182.89" r="5.09616" transform="rotate(29.1538 300.914 182.89)"
                fill="#CCD4FF" stroke="#3E5BEA" stroke-width="0.650574"/>
        <circle opacity="0.2" cx="281.307" cy="159.36" r="3.21076" transform="rotate(29.1538 281.307 159.36)"
                fill="#CCD4FF" stroke="#3E5BEA" stroke-width="0.409884"/>
        <path d="M277.39 219.953C277.39 223.897 274.192 227.094 270.248 227.094L269.692 227.094C265.748 227.094 262.55 223.897 262.55 219.953L262.55 219.396C262.55 215.452 265.748 212.255 269.692 212.255L270.248 212.255C274.192 212.255 277.39 215.452 277.39 219.396L277.39 219.953Z"
              fill="#CCD4FF" stroke="#3E5BEA" stroke-width="0.650574"/>
        <path d="M297.595 187.221L274.533 213.563" stroke="url(#paint49_linear_3400_30368)" stroke-width="0.650574"/>
    </g>
    <defs>
        <filter id="filter0_f_3400_30368" x="36.5339" y="222.133" width="245.983" height="69.1844"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="10.6667" result="effect1_foregroundBlur_3400_30368"/>
        </filter>
        <filter id="filter1_dii_3400_30368" x="188.424" y="74.3405" width="95.7272" height="95.0268"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.542145"/>
            <feGaussianBlur stdDeviation="0.542145"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.294118 0 0 0 0 0.368627 0 0 0 0 0.784314 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2.16858"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-3.25287"/>
            <feGaussianBlur stdDeviation="0.271073"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter2_iiiii_3400_30368" x="201.766" y="111.18" width="45.9489" height="42.5964"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.874899"/>
            <feGaussianBlur stdDeviation="0.43745"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.6247"/>
            <feGaussianBlur stdDeviation="0.874899"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.874899"/>
            <feGaussianBlur stdDeviation="0.43745"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.7498"/>
            <feGaussianBlur stdDeviation="0.43745"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3400_30368" result="effect4_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.43745"/>
            <feGaussianBlur stdDeviation="0.43745"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3400_30368" result="effect5_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter3_iiiii_3400_30368" x="244.836" y="108.118" width="13.4634" height="11.7181"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.873211"/>
            <feGaussianBlur stdDeviation="0.436606"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.61963"/>
            <feGaussianBlur stdDeviation="0.873211"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.873211"/>
            <feGaussianBlur stdDeviation="0.436606"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.74642"/>
            <feGaussianBlur stdDeviation="0.436606"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3400_30368" result="effect4_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.436606"/>
            <feGaussianBlur stdDeviation="0.436606"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3400_30368" result="effect5_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter4_ii_3400_30368" x="30.8477" y="78.6427" width="101.5" height="103.645"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2.16858"/>
            <feGaussianBlur stdDeviation="1.08429"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-3.25287"/>
            <feGaussianBlur stdDeviation="0.271073"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter5_dii_3400_30368" x="62.335" y="77.577" width="50.3109" height="34.568"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.29082" dy="2.58164"/>
            <feGaussianBlur stdDeviation="2.58164"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.126965 0 0 0 0 0.120083 0 0 0 0 0.464193 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.80754"/>
            <feGaussianBlur stdDeviation="3.72533"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.49671" dy="-0.49671"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter6_i_3400_30368" x="58.7333" y="123.203" width="43.9308" height="22.7814"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.08429" dy="1.03254"/>
            <feGaussianBlur stdDeviation="0.162644"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter7_i_3400_30368" x="65.456" y="144.823" width="29.5479" height="16.4616"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.08429" dy="1.03254"/>
            <feGaussianBlur stdDeviation="0.162644"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter8_ii_3400_30368" x="125.293" y="42.4224" width="79.2305" height="103.29"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2.16858"/>
            <feGaussianBlur stdDeviation="1.08429"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-3.25287"/>
            <feGaussianBlur stdDeviation="0.271073"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter9_dii_3400_30368" x="171.979" y="47.7697" width="36.3109" height="43.0216"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.29082" dy="2.58164"/>
            <feGaussianBlur stdDeviation="2.58164"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.126965 0 0 0 0 0.120083 0 0 0 0 0.464193 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.80754"/>
            <feGaussianBlur stdDeviation="3.72533"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.49671" dy="-0.49671"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter10_i_3400_30368" x="120.766" y="62.0334" width="38.6055" height="38.605"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.258213"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.352941 0 0 0 0 0.34902 0 0 0 0 0.360784 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter11_d_3400_30368" x="145.041" y="95.7034" width="31.0956" height="40.5716"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.542145" dy="0.542145"/>
            <feGaussianBlur stdDeviation="1.62644"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0877462 0 0 0 0 0.055411 0 0 0 0 0.286377 0 0 0 0.2 0"/>
            <feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
        </filter>
        <filter id="filter12_i_3400_30368" x="150.848" y="101.894" width="9.20123" height="8.06677"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.216858"/>
            <feGaussianBlur stdDeviation="0.271073"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 0.6 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter13_iiiii_3400_30368" x="115.218" y="58.3394" width="48.7646" height="46.8942"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.80754"/>
            <feGaussianBlur stdDeviation="0.935846"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.87169"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3400_30368" result="effect4_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.467923"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3400_30368" result="effect5_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter14_d_3400_30368" x="119.916" y="47.9374" width="60.6437" height="75.2577"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.918725"/>
            <feGaussianBlur stdDeviation="0.918725"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
        </filter>
        <filter id="filter15_di_3400_30368" x="116.057" y="58.3394" width="48.0227" height="48.0241"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="1.03285"/>
            <feGaussianBlur stdDeviation="0.516425"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.154126 0 0 0 0 0.117691 0 0 0 0 0.305501 0 0 0 0.15 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.258213" dy="-0.258213"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.34902 0 0 0 0 0.34902 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter16_i_3400_30368" x="120.766" y="62.025" width="38.6055" height="38.605"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.258213"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.43287 0 0 0 0 0.431051 0 0 0 0 0.436507 0 0 0 0.6 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter17_i_3400_30368" x="120.703" y="62.4865" width="38.6055" height="41.1867"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="3.87246"/>
            <feGaussianBlur stdDeviation="1.29082"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.165745 0 0 0 0 0.0473572 0 0 0 0 0.358903 0 0 0 0.16 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter18_iiiii_3400_30368" x="116.804" y="57.1611" width="81.3974" height="98.9192"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.80754"/>
            <feGaussianBlur stdDeviation="0.935846"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.87169"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3400_30368" result="effect4_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.467923"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3400_30368" result="effect5_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter19_iiiii_3400_30368" x="115.218" y="58.3401" width="48.7997" height="46.8942"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2.80754"/>
            <feGaussianBlur stdDeviation="0.935846"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3400_30368" result="effect2_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.935846"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3400_30368" result="effect3_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="1.87169"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3400_30368" result="effect4_innerShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.467923"/>
            <feGaussianBlur stdDeviation="0.467923"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3400_30368" result="effect5_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter20_df_3400_30368" x="115.204" y="60.9578" width="49.4008" height="50.4595"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="5.16329"/>
            <feGaussianBlur stdDeviation="2.58164"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feGaussianBlur stdDeviation="0.0645532" result="effect2_foregroundBlur_3400_30368"/>
        </filter>
        <filter id="filter21_f_3400_30368" x="149.917" y="66.3975" width="12.9119" height="35.139"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="0.322705" result="effect1_foregroundBlur_3400_30368"/>
        </filter>
        <filter id="filter22_df_3400_30368" x="112.085" y="83.4135" width="51.4086" height="27.894"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.29082" dy="2.58164"/>
            <feGaussianBlur stdDeviation="2.58164"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.126965 0 0 0 0 0.120083 0 0 0 0 0.464193 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3400_30368" result="shape"/>
            <feGaussianBlur stdDeviation="0.322705" result="effect2_foregroundBlur_3400_30368"/>
        </filter>
        <filter id="filter23_dddi_3400_30368" x="65.2526" y="104.965" width="193.764" height="171.867"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-1.54899"/>
            <feGaussianBlur stdDeviation="0.774493"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="3.09797"/>
            <feGaussianBlur stdDeviation="1.06667"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0510976 0 0 0 0 0.0501684 0 0 0 0 0.250863 0 0 0 0.05 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_3400_30368" result="effect2_dropShadow_3400_30368"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="3.09797"/>
            <feGaussianBlur stdDeviation="10.6667"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0510976 0 0 0 0 0.0501684 0 0 0 0 0.250863 0 0 0 0.05 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_3400_30368" result="effect3_dropShadow_3400_30368"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_3400_30368" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2.32348"/>
            <feGaussianBlur stdDeviation="0.193623"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="shape" result="effect4_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter24_i_3400_30368" x="110.905" y="188.304" width="70.6111" height="9.99157"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.62025" dy="1.54292"/>
            <feGaussianBlur stdDeviation="0.243038"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <filter id="filter25_i_3400_30368" x="112.096" y="208.181" width="32.3884" height="9.99157"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1.62025" dy="1.54292"/>
            <feGaussianBlur stdDeviation="0.243038"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.705882 0 0 0 0 0.752941 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3400_30368"/>
        </filter>
        <linearGradient id="paint0_linear_3400_30368" x1="215.609" y1="158.38" x2="254.89" y2="87.9614"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#DAE0FF"/>
            <stop offset="1" stop-color="#E1E6FF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_3400_30368" x1="256.986" y1="84.2039" x2="215.609" y2="158.38"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CDD8FF"/>
            <stop offset="1" stop-color="#A3B7FE"/>
        </linearGradient>
        <linearGradient id="paint2_linear_3400_30368" x1="253.734" y1="90.6925" x2="219.4" y2="152.243"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint3_linear_3400_30368" x1="253.734" y1="90.6925" x2="219.4" y2="152.243"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint4_linear_3400_30368" x1="253.734" y1="90.6925" x2="219.4" y2="152.243"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint5_linear_3400_30368" x1="239.845" y1="114.005" x2="224.439" y2="141.623"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint6_linear_3400_30368" x1="239.845" y1="114.005" x2="224.439" y2="141.623"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CCD4FF"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint7_linear_3400_30368" x1="252.004" y1="108.119" x2="252.004" y2="118.962"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint8_linear_3400_30368" x1="237.827" y1="155.11" x2="250.635" y2="172.439"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CCD4FF"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint9_linear_3400_30368" x1="23.7069" y1="150.618" x2="20.8057" y2="171.413"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#3E5BEA" stop-opacity="0.5"/>
        </linearGradient>
        <linearGradient id="paint10_linear_3400_30368" x1="92.2938" y1="163.242" x2="92.2938" y2="247.403"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CCD4FF"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint11_linear_3400_30368" x1="62.9986" y1="83.2911" x2="100.798" y2="169.319"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint12_linear_3400_30368" x1="62.9986" y1="83.2911" x2="100.798" y2="169.319"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint13_linear_3400_30368" x1="62.9986" y1="83.2911" x2="100.798" y2="169.319"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <radialGradient id="paint14_radial_3400_30368" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(81.8983 126.305) rotate(66.2801) scale(46.9832 37.3747)">
            <stop stop-color="#7283D6" stop-opacity="0.37"/>
            <stop offset="1" stop-color="#FDFCFE" stop-opacity="0.68"/>
        </radialGradient>
        <linearGradient id="paint15_linear_3400_30368" x1="93.5085" y1="73.9201" x2="90.3807" y2="108.73"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E2E6FF"/>
            <stop offset="0.855332" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint16_linear_3400_30368" x1="91.5054" y1="77.5543" x2="84.1617" y2="103.039"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#A6B4FF"/>
            <stop offset="1" stop-color="#6D84FF"/>
        </linearGradient>
        <linearGradient id="paint17_linear_3400_30368" x1="81.8509" y1="121.921" x2="81.8509" y2="180.213"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CCD4FF"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint18_linear_3400_30368" x1="173.679" y1="48.7396" x2="161.162" y2="138.45"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint19_linear_3400_30368" x1="173.679" y1="48.7396" x2="161.162" y2="138.45"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint20_linear_3400_30368" x1="173.679" y1="48.7396" x2="161.162" y2="138.45"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <radialGradient id="paint21_radial_3400_30368" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(167.421 93.5946) rotate(97.9425) scale(45.2895 36.0274)">
            <stop stop-color="#7283D6" stop-opacity="0.37"/>
            <stop offset="1" stop-color="#FDFCFE" stop-opacity="0.68"/>
        </radialGradient>
        <linearGradient id="paint22_linear_3400_30368" x1="199.527" y1="59.1545" x2="180.719" y2="76.2374"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#A6B4FF"/>
            <stop offset="1" stop-color="#6D84FF"/>
        </linearGradient>
        <linearGradient id="paint23_linear_3400_30368" x1="146.342" y1="59.6232" x2="128.007" y2="98.2235"
                        gradientUnits="userSpaceOnUse">
            <stop offset="0.103447" stop-color="white"/>
            <stop offset="1" stop-color="#FBFDFF"/>
        </linearGradient>
        <linearGradient id="paint24_linear_3400_30368" x1="156.42" y1="105.868" x2="148.736" y2="106.193"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="0.193006" stop-color="white"/>
            <stop offset="0.325236" stop-color="white"/>
            <stop offset="0.680346" stop-color="#A2A6DE"/>
            <stop offset="0.849126" stop-color="#777DCF"/>
            <stop offset="0.903225" stop-color="#A9ACE1"/>
            <stop offset="1" stop-color="#C6C9EB"/>
        </linearGradient>
        <linearGradient id="paint25_linear_3400_30368" x1="170.953" y1="123.736" x2="164.546" y2="127.989"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E9E6E4"/>
            <stop offset="0.193006" stop-color="white"/>
            <stop offset="0.325236" stop-color="#F8F8F8"/>
            <stop offset="0.680346" stop-color="#9A9A9A"/>
            <stop offset="0.849126" stop-color="#8B8A89"/>
            <stop offset="0.903225" stop-color="#A09E9C"/>
            <stop offset="1" stop-color="#B3B3B3"/>
        </linearGradient>
        <linearGradient id="paint26_linear_3400_30368" x1="166.217" y1="130.757" x2="170.924" y2="127.743"
                        gradientUnits="userSpaceOnUse">
            <stop offset="0.035" stop-color="#8397FF"/>
            <stop offset="0.442387" stop-color="#92A3FF"/>
            <stop offset="1" stop-color="#C1CBFF"/>
        </linearGradient>
        <linearGradient id="paint27_linear_3400_30368" x1="152.496" y1="108.251" x2="157.315" y2="105.162"
                        gradientUnits="userSpaceOnUse">
            <stop offset="0.035" stop-color="#8397FF"/>
            <stop offset="0.442387" stop-color="#92A3FF"/>
            <stop offset="1" stop-color="#BFC9FF"/>
        </linearGradient>
        <linearGradient id="paint28_linear_3400_30368" x1="165.151" y1="112.71" x2="157.145" y2="117.615"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="0.193006" stop-color="white"/>
            <stop offset="0.325236" stop-color="white"/>
            <stop offset="0.6" stop-color="#DADAE9"/>
            <stop offset="0.82" stop-color="#D6D6E5"/>
            <stop offset="1" stop-color="#E2E2ED"/>
        </linearGradient>
        <linearGradient id="paint29_linear_3400_30368" x1="136.117" y1="60.5651" x2="136.117" y2="101.873"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E8ECFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint30_linear_3400_30368" x1="154.997" y1="48.8561" x2="154.997" y2="114.383"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E8ECFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint31_linear_3400_30368" x1="143.244" y1="58.5627" x2="136.895" y2="104.075"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E8ECFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint32_linear_3400_30368" x1="121.539" y1="80.5657" x2="156.399" y2="84.773"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E9EDFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint33_linear_3400_30368" x1="140.007" y1="62.4889" x2="140.007" y2="101.089"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E8ECFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint34_linear_3400_30368" x1="165.775" y1="61.3124" x2="153.259" y2="151.023"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint35_linear_3400_30368" x1="134.823" y1="105.642" x2="159.425" y2="67.3155"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#C1C1C1"/>
            <stop offset="0.121727" stop-color="white"/>
            <stop offset="0.432632" stop-color="#575757"/>
            <stop offset="0.748781" stop-color="#D1D1D1"/>
            <stop offset="1" stop-color="#E1E0E0"/>
        </linearGradient>
        <linearGradient id="paint36_linear_3400_30368" x1="161.591" y1="93.3692" x2="155.166" y2="86.2088"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#606060"/>
            <stop offset="0.397231" stop-color="white" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="paint37_linear_3400_30368" x1="161.841" y1="57.8717" x2="134.15" y2="109.695"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#3E5BEA"/>
            <stop offset="0.192009" stop-color="#7E8DFF"/>
            <stop offset="0.703503" stop-color="#C6D0FF"/>
            <stop offset="0.962692" stop-color="#9EAEFB"/>
        </linearGradient>
        <linearGradient id="paint38_linear_3400_30368" x1="143.245" y1="58.5628" x2="136.895" y2="104.075"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#E8ECFF"/>
            <stop offset="1" stop-color="#EEF0FA"/>
        </linearGradient>
        <linearGradient id="paint39_linear_3400_30368" x1="139.905" y1="60.8935" x2="137.111" y2="100.878"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#7088FC"/>
            <stop offset="0.777322" stop-color="#FEF6F2"/>
            <stop offset="1" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint40_linear_3400_30368" x1="159.768" y1="67.3771" x2="155.005" y2="101.511"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#909DE5"/>
            <stop offset="1" stop-color="#8B9EFF"/>
        </linearGradient>
        <linearGradient id="paint41_linear_3400_30368" x1="123.256" y1="88.0337" x2="136.872" y2="103.4"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8799EE"/>
            <stop offset="1" stop-color="#8CA0FF"/>
        </linearGradient>
        <linearGradient id="paint42_linear_3400_30368" x1="156.885" y1="97.1361" x2="122.963" y2="97.5071"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#B0B0E2" stop-opacity="0.41"/>
            <stop offset="0.178711" stop-color="#9B9BDA"/>
            <stop offset="0.494368" stop-color="#5163BF"/>
            <stop offset="0.839023" stop-color="#9898D8" stop-opacity="0.829592"/>
            <stop offset="1" stop-color="#B9E4E4" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="paint43_linear_3400_30368" x1="162.802" y1="143.855" x2="162.802" y2="252.4"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint44_linear_3400_30368" x1="162.802" y1="143.855" x2="162.802" y2="252.4"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint45_linear_3400_30368" x1="162.802" y1="143.855" x2="162.802" y2="252.4"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <radialGradient id="paint46_radial_3400_30368" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(162.802 198.127) rotate(90) scale(54.2727 76.404)">
            <stop stop-color="#7283D6" stop-opacity="0.37"/>
            <stop offset="1" stop-color="#FDFCFE" stop-opacity="0.68"/>
        </radialGradient>
        <linearGradient id="paint47_linear_3400_30368" x1="118.901" y1="174.917" x2="118.901" y2="152.624"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8B9EFF"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint48_linear_3400_30368" x1="283.055" y1="160.176" x2="298.161" y2="178.987"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#3E5BEA" stop-opacity="0.5"/>
        </linearGradient>
        <linearGradient id="paint49_linear_3400_30368" x1="297.764" y1="189.281" x2="275.657" y2="213.439"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#3E5BEA" stop-opacity="0.5"/>
        </linearGradient>
        <clipPath id="clip0_3400_30368">
            <rect width="320" height="320" fill="white"/>
        </clipPath>
        <clipPath id="clip1_3400_30368">
            <rect width="108.429" height="108.429" fill="white" transform="translate(215.344 47.5694) rotate(29.1538)"/>
        </clipPath>
    </defs>
</svg>
