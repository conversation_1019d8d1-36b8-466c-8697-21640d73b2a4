<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.16412 79.9327L45.1036 17.9503C47.3142 14.2411 52.6858 14.2411 54.8964 17.9503L91.8359 79.9327C94.1002 83.7322 91.3625 88.5508 86.9395 88.5508H13.0605C8.63751 88.5508 5.89978 83.7322 8.16412 79.9327Z"
          fill="url(#paint0_linear_3535_28294)" stroke="url(#paint1_linear_3535_28294)" stroke-width="0.6"/>
    <g filter="url(#filter0_dii_3535_28294)">
        <path d="M18.1848 74.8902L45.736 28.139C47.6724 24.8532 52.4267 24.858 54.3564 28.1477L81.7805 74.8989C83.7358 78.2321 81.3321 82.4287 77.4678 82.4287H22.4924C18.6235 82.4287 16.2205 78.2233 18.1848 74.8902Z"
              fill="url(#paint2_linear_3535_28294)" shape-rendering="crispEdges"/>
        <path d="M18.1848 74.8902L45.736 28.139C47.6724 24.8532 52.4267 24.858 54.3564 28.1477L81.7805 74.8989C83.7358 78.2321 81.3321 82.4287 77.4678 82.4287H22.4924C18.6235 82.4287 16.2205 78.2233 18.1848 74.8902Z"
              fill="url(#paint3_linear_3535_28294)" fill-opacity="0.4" shape-rendering="crispEdges"/>
        <path d="M18.1848 74.8902L45.736 28.139C47.6724 24.8532 52.4267 24.858 54.3564 28.1477L81.7805 74.8989C83.7358 78.2321 81.3321 82.4287 77.4678 82.4287H22.4924C18.6235 82.4287 16.2205 78.2233 18.1848 74.8902Z"
              fill="url(#paint4_linear_3535_28294)" shape-rendering="crispEdges"/>
        <path d="M18.4432 75.0425L45.9945 28.2913C47.8146 25.2027 52.2837 25.2072 54.0976 28.2995L81.5218 75.0507C83.3597 78.1839 81.1003 82.1287 77.4678 82.1287H22.4924C18.8557 82.1287 16.5968 78.1756 18.4432 75.0425Z"
              stroke="#C4D1FE" stroke-width="0.6" shape-rendering="crispEdges"/>
    </g>
    <g filter="url(#filter1_iiiii_3535_28294)">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M46.1676 43.5179L49.0498 64.1026C49.116 64.5758 49.5208 64.9279 49.9987 64.9279C50.4765 64.9279 50.8813 64.5758 50.9475 64.1026L53.8297 43.5179C54.035 42.0517 53.3855 40.5969 52.1568 39.771C50.8517 38.8937 49.1456 38.8937 47.8406 39.771C46.6119 40.5969 45.9623 42.0517 46.1676 43.5179ZM49.9987 76.2026C52.2998 76.2026 54.1653 74.3699 54.1653 72.1091C54.1653 69.8483 52.2998 68.0156 49.9987 68.0156C47.6975 68.0156 45.832 69.8483 45.832 72.1091C45.832 74.3699 47.6975 76.2026 49.9987 76.2026Z"
              fill="url(#paint5_linear_3535_28294)"/>
    </g>
    <g opacity="0.5" filter="url(#filter2_d_3535_28294)">
        <mask id="path-5-inside-1_3535_28294" fill="white">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M46.1676 43.5179L49.0498 64.1026C49.116 64.5758 49.5208 64.9279 49.9987 64.9279C50.4765 64.9279 50.8813 64.5758 50.9475 64.1026L53.8297 43.5179C54.035 42.0517 53.3855 40.5969 52.1568 39.771C50.8517 38.8937 49.1456 38.8937 47.8406 39.771C46.6119 40.5969 45.9623 42.0517 46.1676 43.5179ZM49.9987 76.2026C52.2998 76.2026 54.1653 74.3699 54.1653 72.1091C54.1653 69.8483 52.2998 68.0156 49.9987 68.0156C47.6975 68.0156 45.832 69.8483 45.832 72.1091C45.832 74.3699 47.6975 76.2026 49.9987 76.2026Z"/>
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M46.1676 43.5179L49.0498 64.1026C49.116 64.5758 49.5208 64.9279 49.9987 64.9279C50.4765 64.9279 50.8813 64.5758 50.9475 64.1026L53.8297 43.5179C54.035 42.0517 53.3855 40.5969 52.1568 39.771C50.8517 38.8937 49.1456 38.8937 47.8406 39.771C46.6119 40.5969 45.9623 42.0517 46.1676 43.5179ZM49.9987 76.2026C52.2998 76.2026 54.1653 74.3699 54.1653 72.1091C54.1653 69.8483 52.2998 68.0156 49.9987 68.0156C47.6975 68.0156 45.832 69.8483 45.832 72.1091C45.832 74.3699 47.6975 76.2026 49.9987 76.2026Z"
              fill="url(#paint6_linear_3535_28294)"/>
        <path d="M49.0498 64.1026L49.644 64.0194L49.0498 64.1026ZM46.1676 43.5179L45.5734 43.6011L46.1676 43.5179ZM50.9475 64.1026L51.5418 64.1858V64.1858L50.9475 64.1026ZM53.8297 43.5179L54.4239 43.6011V43.6011L53.8297 43.5179ZM52.1568 39.771L51.822 40.2689L52.1568 39.771ZM47.8406 39.771L48.1753 40.2689L47.8406 39.771ZM49.644 64.0194L46.7618 43.4347L45.5734 43.6011L48.4556 64.1858L49.644 64.0194ZM49.9987 64.3279C49.8201 64.3279 49.6688 64.1963 49.644 64.0194L48.4556 64.1858C48.5633 64.9554 49.2216 65.5279 49.9987 65.5279V64.3279ZM50.3533 64.0194C50.3286 64.1963 50.1773 64.3279 49.9987 64.3279V65.5279C50.7757 65.5279 51.434 64.9554 51.5418 64.1858L50.3533 64.0194ZM53.2355 43.4347L50.3533 64.0194L51.5418 64.1858L54.4239 43.6011L53.2355 43.4347ZM51.822 40.2689C52.8602 40.9667 53.409 42.1959 53.2355 43.4347L54.4239 43.6011C54.6611 41.9074 53.9108 40.227 52.4915 39.273L51.822 40.2689ZM48.1753 40.2689C49.2779 39.5277 50.7194 39.5277 51.822 40.2689L52.4915 39.273C50.9841 38.2597 49.0133 38.2597 47.5059 39.273L48.1753 40.2689ZM46.7618 43.4347C46.5884 42.1959 47.1372 40.9667 48.1753 40.2689L47.5059 39.273C46.0866 40.227 45.3363 41.9075 45.5734 43.6011L46.7618 43.4347ZM53.5653 72.1091C53.5653 74.0285 51.9785 75.6026 49.9987 75.6026V76.8026C52.6211 76.8026 54.7653 74.7112 54.7653 72.1091H53.5653ZM49.9987 68.6156C51.9785 68.6156 53.5653 70.1897 53.5653 72.1091H54.7653C54.7653 69.507 52.6211 67.4156 49.9987 67.4156V68.6156ZM46.432 72.1091C46.432 70.1897 48.0188 68.6156 49.9987 68.6156V67.4156C47.3762 67.4156 45.232 69.507 45.232 72.1091H46.432ZM49.9987 75.6026C48.0188 75.6026 46.432 74.0285 46.432 72.1091H45.232C45.232 74.7112 47.3762 76.8026 49.9987 76.8026V75.6026Z"
              fill="#4E5FF6" mask="url(#path-5-inside-1_3535_28294)"/>
    </g>
    <path d="M49.3469 64.0611L46.4647 43.4763C46.2753 42.1238 46.8745 40.7818 48.0079 40.02C49.2117 39.2108 50.7856 39.2108 51.9894 40.02C53.1228 40.7818 53.722 42.1238 53.5326 43.4763L50.6504 64.0611C50.6049 64.3861 50.3269 64.6279 49.9987 64.6279C49.6705 64.6279 49.3924 64.3861 49.3469 64.0611Z"
          stroke="#3E5BEA" stroke-width="0.6"/>
    <path d="M51.7188 63.9766C51.7188 64.9431 50.9352 65.7266 49.9687 65.7266C49.0023 65.7266 48.2187 64.9431 48.2187 63.9766C48.2187 63.0101 49.0023 62.2266 49.9687 62.2266C50.9352 62.2266 51.7188 63.0101 51.7188 63.9766Z"
          fill="white" stroke="#3E5BEA" stroke-width="0.6"/>
    <defs>
        <filter id="filter0_dii_3535_28294" x="16.4844" y="25.1776" width="66.9922" height="58.7512"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.5"/>
            <feGaussianBlur stdDeviation="0.5"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.294118 0 0 0 0 0.368627 0 0 0 0 0.784314 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3535_28294"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3535_28294" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0745098 0 0 0 0 0.152941 0 0 0 0 0.423529 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_3535_28294"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="-3"/>
            <feGaussianBlur stdDeviation="0.25"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0815686 0 0 0 0 0.0745098 0 0 0 0 0.427451 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3535_28294" result="effect3_innerShadow_3535_28294"/>
        </filter>
        <filter id="filter1_iiiii_3535_28294" x="45.032" y="39.113" width="9.53203" height="37.4895"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="0.4"/>
            <feGaussianBlur stdDeviation="0.2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3535_28294"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.8"/>
            <feGaussianBlur stdDeviation="0.4"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 0 0.988235 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_innerShadow_3535_28294" result="effect2_innerShadow_3535_28294"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-0.4"/>
            <feGaussianBlur stdDeviation="0.2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.919531 0 0 0 0 0.928998 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect2_innerShadow_3535_28294" result="effect3_innerShadow_3535_28294"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.4"/>
            <feGaussianBlur stdDeviation="0.2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_3535_28294" result="effect4_innerShadow_3535_28294"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="0.2"/>
            <feGaussianBlur stdDeviation="0.2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.874756 0 0 0 0 0.88728 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_3535_28294" result="effect5_innerShadow_3535_28294"/>
        </filter>
        <filter id="filter2_d_3535_28294" x="42.832" y="38.113" width="12.332" height="41.0895"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-1" dy="1"/>
            <feGaussianBlur stdDeviation="1"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.126965 0 0 0 0 0.120083 0 0 0 0 0.464193 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3535_28294"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3535_28294" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_3535_28294" x1="50" y1="88.8508" x2="50" y2="13.1858"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#DAE0FF"/>
            <stop offset="1" stop-color="#E1E6FF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_3535_28294" x1="50" y1="9.14844" x2="50" y2="88.8508"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#CDD8FF"/>
            <stop offset="1" stop-color="#A3B7FE"/>
        </linearGradient>
        <linearGradient id="paint2_linear_3535_28294" x1="50.2907" y1="20.8125" x2="50.2907" y2="82.4287"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint3_linear_3535_28294" x1="50.2907" y1="20.8125" x2="50.2907" y2="82.4287"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#8190FE"/>
            <stop offset="1" stop-color="#7285FF"/>
        </linearGradient>
        <linearGradient id="paint4_linear_3535_28294" x1="50.2907" y1="20.8125" x2="50.2907" y2="82.4287"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="white" stop-opacity="0.9"/>
            <stop offset="1.0001" stop-color="white" stop-opacity="0.9"/>
        </linearGradient>
        <linearGradient id="paint5_linear_3535_28294" x1="49.9987" y1="39.113" x2="49.9987" y2="77.1988"
                        gradientUnits="userSpaceOnUse">
            <stop offset="0.31" stop-color="white"/>
            <stop offset="1" stop-color="#8B9EFF"/>
            <stop offset="1.0001" stop-color="white"/>
        </linearGradient>
        <linearGradient id="paint6_linear_3535_28294" x1="51.8863" y1="46.9523" x2="41.2497" y2="49.7326"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#4361FF"/>
            <stop offset="1" stop-color="#5C76FF"/>
        </linearGradient>
    </defs>
</svg>
