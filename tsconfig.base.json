{"exclude": ["dist", "example", "vite.config.ts", "packages/mb/**", "packages/pc/**"], "include": ["typings.d.ts"], "compilerOptions": {"strict": false, "noImplicitAny": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "target": "es2020", "lib": ["es2021", "dom", "dom.iterable"], "jsx": "react-jsx", "emitDeclarationOnly": true, "declarationDir": "dist", "isolatedModules": true, "forceConsistentCasingInFileNames": true}}