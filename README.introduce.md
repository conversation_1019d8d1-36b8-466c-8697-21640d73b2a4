# 认证流程介绍

## 判断是否登陆

![判断是否登陆](assets/isLogin.png)

## 登陆流程

![登陆流程](assets/introduce.png)

[//]: # '- 是否企微扫码登陆'
[//]: # '  - 首先判断当前主域下存储的临时登录 Code `cookie sso_wx`，不存在为非企微登录'
[//]: # '  - 如果 `sso_wx` 存在 走企微认证 消费 `cookie sso_wx`'
[//]: # '  - 请求业务预检Api判定用户是否登录业务系统'
[//]: # '  - 获取 `ticket`'
[//]: # '  - 消费 `ticket` 换 `token`'
[//]: # '  - `token` 存在 `localStorage`中，后续请求 `Set-Header`'
[//]: # '- 非企微扫码'
[//]: # '  - 为上述 3,4,5,6 流程'

## 忘记密码

![忘记密码](assets/reset.png)
